<template>
  <div class="notification-system">
    <TransitionGroup name="notification" tag="div" class="notifications-container">
      <div
        v-for="notification in notifications"
        :key="notification.id"
        class="notification"
        :class="[`notification--${notification.type}`]"
        @click="removeNotification(notification.id)"
      >
        <div class="notification-icon">
          <component :is="getIcon(notification.type)" />
        </div>

        <div class="notification-content">
          <div class="notification-message">{{ notification.message }}</div>
          <div v-if="notification.description" class="notification-description">
            {{ notification.description }}
          </div>
        </div>

        <button class="notification-close" @click.stop="removeNotification(notification.id)">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
            <line x1="18" y1="6" x2="6" y2="18" stroke="currentColor" stroke-width="2"/>
            <line x1="6" y1="6" x2="18" y2="18" stroke="currentColor" stroke-width="2"/>
          </svg>
        </button>

        <div class="notification-progress" :style="{ animationDuration: `${notification.duration}ms` }"></div>
      </div>
    </TransitionGroup>
  </div>
</template>

<script setup>
import { ref, defineExpose } from 'vue'

const notifications = ref([])
let notificationId = 0

function show(message, type = 'info', options = {}) {
  const notification = {
    id: ++notificationId,
    message,
    type,
    description: options.description,
    duration: options.duration || 4000,
    persistent: options.persistent || false
  }

  notifications.value.push(notification)

  if (!notification.persistent) {
    setTimeout(() => {
      removeNotification(notification.id)
    }, notification.duration)
  }

  return notification.id
}

function removeNotification(id) {
  const index = notifications.value.findIndex(n => n.id === id)
  if (index > -1) {
    notifications.value.splice(index, 1)
  }
}

function clear() {
  notifications.value = []
}

function getIcon(type) {
  const icons = {
    success: 'CheckIcon',
    error: 'ErrorIcon',
    warning: 'WarningIcon',
    info: 'InfoIcon'
  }
  return icons[type] || 'InfoIcon'
}

// 暴露方法给父组件
defineExpose({
  show,
  removeNotification,
  clear
})
</script>

<style lang="scss" scoped>
.notification-system {
  position: fixed;
  top: 80px;
  right: 20px;
  z-index: 1000;
  pointer-events: none;
}

.notifications-container {
  display: flex;
  flex-direction: column;
  gap: 12px;
  max-width: 400px;
}

.notification {
  position: relative;
  display: flex;
  align-items: flex-start;
  gap: 12px;
  background: rgba(0, 0, 0, 0.9);
  backdrop-filter: blur(20px);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
  padding: 16px;
  cursor: pointer;
  pointer-events: auto;
  overflow: hidden;
  transition: all var(--transition-normal);

  &:hover {
    background: rgba(0, 0, 0, 0.95);
    border-color: var(--border-hover);
    transform: translateX(-4px);
  }

  &--success {
    border-left: 4px solid var(--secondary-color);

    .notification-icon {
      color: var(--secondary-color);
    }
  }

  &--error {
    border-left: 4px solid var(--danger-color);

    .notification-icon {
      color: var(--danger-color);
    }
  }

  &--warning {
    border-left: 4px solid var(--accent-color);

    .notification-icon {
      color: var(--accent-color);
    }
  }

  &--info {
    border-left: 4px solid var(--primary-color);

    .notification-icon {
      color: var(--primary-color);
    }
  }
}

.notification-icon {
  width: 20px;
  height: 20px;
  flex-shrink: 0;
  margin-top: 2px;
}

.notification-content {
  flex: 1;
  min-width: 0;

  .notification-message {
    font-size: 14px;
    font-weight: 500;
    color: var(--text-primary);
    line-height: 1.4;
  }

  .notification-description {
    font-size: 12px;
    color: var(--text-secondary);
    margin-top: 4px;
    line-height: 1.3;
  }
}

.notification-close {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: transparent;
  border: none;
  border-radius: var(--radius-sm);
  color: var(--text-tertiary);
  cursor: pointer;
  transition: all var(--transition-fast);
  flex-shrink: 0;

  &:hover {
    background: rgba(255, 255, 255, 0.1);
    color: var(--text-primary);
  }
}

.notification-progress {
  position: absolute;
  bottom: 0;
  left: 0;
  height: 2px;
  background: currentColor;
  opacity: 0.3;
  animation: progress linear;
  transform-origin: left;
}

@keyframes progress {
  from {
    transform: scaleX(1);
  }
  to {
    transform: scaleX(0);
  }
}

// 过渡动画
.notification-enter-active {
  transition: all 0.3s ease;
}

.notification-leave-active {
  transition: all 0.3s ease;
}

.notification-enter-from {
  opacity: 0;
  transform: translateX(100%);
}

.notification-leave-to {
  opacity: 0;
  transform: translateX(100%);
}

// 响应式设计
@media (max-width: 768px) {
  .notification-system {
    top: 70px;
    right: 16px;
    left: 16px;
  }

  .notifications-container {
    max-width: none;
  }

  .notification {
    padding: 12px;

    .notification-content {
      .notification-message {
        font-size: 13px;
      }

      .notification-description {
        font-size: 11px;
      }
    }
  }
}
</style>

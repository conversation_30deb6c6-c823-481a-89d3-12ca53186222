<template>
  <div v-if="results.length > 0" class="search-results">
    <div class="search-results-header">
      <h3 class="search-results-title">搜索结果</h3>
      <button class="close-button" @click="$emit('close')">
        <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
          <line x1="18" y1="6" x2="6" y2="18" stroke="currentColor" stroke-width="2"/>
          <line x1="6" y1="6" x2="18" y2="18" stroke="currentColor" stroke-width="2"/>
        </svg>
      </button>
    </div>

    <div class="search-results-list">
      <div
        v-for="(result, index) in results"
        :key="index"
        class="search-result-item"
        @click="selectLocation(result)"
      >
        <div class="result-icon">
          <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
            <path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z" stroke="currentColor" stroke-width="2"/>
            <circle cx="12" cy="10" r="3" stroke="currentColor" stroke-width="2"/>
          </svg>
        </div>

        <div class="result-content">
          <div class="result-name">{{ result.name }}</div>
          <div class="result-type">{{ result.type }}</div>
          <div class="result-coordinates">
            {{ formatCoordinate(result.lat) }}, {{ formatCoordinate(result.lng) }}
          </div>
        </div>

        <div class="result-action">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
            <line x1="7" y1="17" x2="17" y2="7" stroke="currentColor" stroke-width="2"/>
            <polyline points="7,7 17,7 17,17" stroke="currentColor" stroke-width="2"/>
          </svg>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
const props = defineProps(['results'])
const emit = defineEmits(['select-location', 'close'])

function selectLocation(location) {
  emit('select-location', location)
}

function formatCoordinate(value) {
  return typeof value === 'number' ? value.toFixed(4) + '°' : '0°'
}
</script>

<style lang="scss" scoped>
.search-results {
  position: fixed;
  top: 80px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 200;
  width: 100%;
  max-width: 500px;
  background: rgba(0, 0, 0, 0.9);
  backdrop-filter: blur(20px);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-lg);
  animation: slideDown 0.3s ease;
}

.search-results-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;
  border-bottom: 1px solid var(--border-color);

  .search-results-title {
    font-size: 16px;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
  }

  .close-button {
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: transparent;
    border: none;
    border-radius: var(--radius-md);
    color: var(--text-secondary);
    cursor: pointer;
    transition: all var(--transition-fast);

    &:hover {
      background: rgba(255, 255, 255, 0.1);
      color: var(--text-primary);
    }
  }
}

.search-results-list {
  max-height: 400px;
  overflow-y: auto;
}

.search-result-item {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px 20px;
  cursor: pointer;
  transition: all var(--transition-fast);
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);

  &:last-child {
    border-bottom: none;
  }

  &:hover {
    background: rgba(66, 133, 244, 0.1);

    .result-action {
      opacity: 1;
      transform: translateX(0);
    }
  }

  .result-icon {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(66, 133, 244, 0.2);
    border-radius: var(--radius-md);
    color: var(--primary-color);
    flex-shrink: 0;
  }

  .result-content {
    flex: 1;
    min-width: 0;

    .result-name {
      font-size: 15px;
      font-weight: 500;
      color: var(--text-primary);
      margin-bottom: 2px;
    }

    .result-type {
      font-size: 12px;
      color: var(--text-tertiary);
      text-transform: uppercase;
      letter-spacing: 0.5px;
      margin-bottom: 4px;
    }

    .result-coordinates {
      font-size: 12px;
      color: var(--text-secondary);
      font-family: 'Monaco', 'Menlo', monospace;
    }
  }

  .result-action {
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-tertiary);
    opacity: 0;
    transform: translateX(8px);
    transition: all var(--transition-fast);
  }
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateX(-50%) translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(-50%) translateY(0);
  }
}

// 响应式设计
@media (max-width: 768px) {
  .search-results {
    top: 70px;
    left: 16px;
    right: 16px;
    transform: none;
    max-width: none;
  }

  .search-results-header {
    padding: 12px 16px;

    .search-results-title {
      font-size: 15px;
    }
  }

  .search-result-item {
    padding: 12px 16px;
    gap: 12px;

    .result-icon {
      width: 36px;
      height: 36px;
    }

    .result-content {
      .result-name {
        font-size: 14px;
      }
    }
  }
}
</style>

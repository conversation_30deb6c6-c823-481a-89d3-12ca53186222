export class PerformanceMonitor {
    constructor() {
        this.stats = {
            fps: 0,
            frameTime: 0,
            drawCalls: 0,
            triangles: 0,
            memory: 0
        };
        
        this.frameCount = 0;
        this.lastTime = performance.now();
        this.fpsUpdateInterval = 1000; // 每秒更新一次FPS
        this.lastFpsUpdate = 0;
        
        this.panel = null;
        this.isVisible = false;
        
        this.createPanel();
    }
    
    createPanel() {
        this.panel = document.createElement('div');
        this.panel.style.cssText = `
            position: fixed;
            top: 10px;
            left: 10px;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            z-index: 10000;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            display: none;
        `;
        
        document.body.appendChild(this.panel);
    }
    
    update(renderer) {
        const currentTime = performance.now();
        this.frameCount++;
        
        // 计算帧时间
        this.stats.frameTime = currentTime - this.lastTime;
        this.lastTime = currentTime;
        
        // 每秒更新一次FPS
        if (currentTime - this.lastFpsUpdate >= this.fpsUpdateInterval) {
            this.stats.fps = Math.round((this.frameCount * 1000) / (currentTime - this.lastFpsUpdate));
            this.frameCount = 0;
            this.lastFpsUpdate = currentTime;
        }
        
        // 获取渲染器信息
        if (renderer && renderer.info) {
            this.stats.drawCalls = renderer.info.render.calls;
            this.stats.triangles = renderer.info.render.triangles;
        }
        
        // 获取内存使用情况（如果支持）
        if (performance.memory) {
            this.stats.memory = Math.round(performance.memory.usedJSHeapSize / 1024 / 1024);
        }
        
        this.updatePanel();
    }
    
    updatePanel() {
        if (!this.isVisible || !this.panel) return;
        
        const fpsColor = this.stats.fps >= 50 ? '#4CAF50' : this.stats.fps >= 30 ? '#FF9800' : '#F44336';
        
        this.panel.innerHTML = `
            <div style="color: ${fpsColor}; font-weight: bold;">FPS: ${this.stats.fps}</div>
            <div>Frame Time: ${this.stats.frameTime.toFixed(2)}ms</div>
            <div>Draw Calls: ${this.stats.drawCalls}</div>
            <div>Triangles: ${this.stats.triangles.toLocaleString()}</div>
            ${this.stats.memory > 0 ? `<div>Memory: ${this.stats.memory}MB</div>` : ''}
        `;
    }
    
    toggle() {
        this.isVisible = !this.isVisible;
        this.panel.style.display = this.isVisible ? 'block' : 'none';
    }
    
    show() {
        this.isVisible = true;
        this.panel.style.display = 'block';
    }
    
    hide() {
        this.isVisible = false;
        this.panel.style.display = 'none';
    }
    
    getStats() {
        return { ...this.stats };
    }
    
    // 性能建议
    getPerformanceAdvice() {
        const advice = [];
        
        if (this.stats.fps < 30) {
            advice.push('FPS过低，建议降低渲染质量或减少几何体复杂度');
        }
        
        if (this.stats.drawCalls > 100) {
            advice.push('绘制调用过多，建议合并几何体或使用实例化渲染');
        }
        
        if (this.stats.triangles > 1000000) {
            advice.push('三角形数量过多，建议使用LOD系统');
        }
        
        if (this.stats.memory > 500) {
            advice.push('内存使用过高，建议释放未使用的纹理和几何体');
        }
        
        return advice;
    }
}

// LOD (Level of Detail) 系统
export class LODSystem {
    constructor() {
        this.lodLevels = [
            { distance: 2, detail: 'high' },
            { distance: 5, detail: 'medium' },
            { distance: 10, detail: 'low' },
            { distance: Infinity, detail: 'minimal' }
        ];
    }
    
    getLODLevel(camera, object) {
        const distance = camera.position.distanceTo(object.position);
        
        for (const level of this.lodLevels) {
            if (distance <= level.distance) {
                return level.detail;
            }
        }
        
        return 'minimal';
    }
    
    updateLOD(camera, objects) {
        objects.forEach(object => {
            const lodLevel = this.getLODLevel(camera, object);
            
            if (object.userData.lodLevel !== lodLevel) {
                object.userData.lodLevel = lodLevel;
                this.applyLOD(object, lodLevel);
            }
        });
    }
    
    applyLOD(object, level) {
        switch (level) {
            case 'high':
                object.visible = true;
                if (object.material) {
                    object.material.wireframe = false;
                }
                break;
                
            case 'medium':
                object.visible = true;
                if (object.material) {
                    object.material.wireframe = false;
                }
                break;
                
            case 'low':
                object.visible = true;
                if (object.material) {
                    object.material.wireframe = true;
                }
                break;
                
            case 'minimal':
                object.visible = false;
                break;
        }
    }
}

// 纹理优化器
export class TextureOptimizer {
    static optimizeTexture(texture, maxSize = 1024) {
        if (!texture.image) return texture;
        
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        
        const { width, height } = texture.image;
        const scale = Math.min(maxSize / width, maxSize / height, 1);
        
        canvas.width = width * scale;
        canvas.height = height * scale;
        
        ctx.drawImage(texture.image, 0, 0, canvas.width, canvas.height);
        
        const optimizedTexture = new THREE.CanvasTexture(canvas);
        optimizedTexture.wrapS = texture.wrapS;
        optimizedTexture.wrapT = texture.wrapT;
        optimizedTexture.minFilter = THREE.LinearFilter;
        optimizedTexture.magFilter = THREE.LinearFilter;
        
        return optimizedTexture;
    }
    
    static createMipMaps(texture) {
        if (texture.image && texture.image.width && texture.image.height) {
            texture.generateMipmaps = true;
            texture.minFilter = THREE.LinearMipmapLinearFilter;
        }
    }
}

// 几何体优化器
export class GeometryOptimizer {
    static simplifyGeometry(geometry, ratio = 0.5) {
        // 简化几何体（这里是一个简单的实现）
        const positions = geometry.attributes.position.array;
        const indices = geometry.index ? geometry.index.array : null;
        
        // 这里可以实现更复杂的几何体简化算法
        // 目前只是一个占位符
        
        return geometry;
    }
    
    static mergeGeometries(geometries) {
        // 合并多个几何体以减少绘制调用
        return THREE.BufferGeometryUtils.mergeBufferGeometries(geometries);
    }
}

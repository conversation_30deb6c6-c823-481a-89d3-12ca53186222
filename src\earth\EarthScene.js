import * as THREE from 'three';

export class EarthScene {
    constructor() {
        this.group = new THREE.Group();
        this.earthMesh = null;
        this.earthGeometry = null;
        this.earthMaterial = null;
        
        // 纹理
        this.dayTexture = null;
        this.nightTexture = null;
        this.normalTexture = null;
        this.specularTexture = null;
        this.cloudsTexture = null;
        
        // 光照
        this.sunLight = null;
        this.ambientLight = null;
    }
    
    async init() {
        await this.loadTextures();
        this.createGeometry();
        this.createMaterial();
        this.createMesh();
        this.setupLighting();
        this.setupGroup();
    }
    
    async loadTextures() {
        const loader = new THREE.TextureLoader();
        
        try {
            // 使用免费的地球纹理资源
            const texturePromises = [
                this.loadTextureWithFallback(loader, [
                    'https://raw.githubusercontent.com/mrdoob/three.js/dev/examples/textures/planets/earth_atmos_2048.jpg',
                    'https://threejs.org/examples/textures/planets/earth_atmos_2048.jpg'
                ]),
                this.loadTextureWithFallback(loader, [
                    'https://raw.githubusercontent.com/mrdoob/three.js/dev/examples/textures/planets/earth_lights_2048.jpg',
                    'https://threejs.org/examples/textures/planets/earth_lights_2048.jpg'
                ]),
                this.loadTextureWithFallback(loader, [
                    'https://raw.githubusercontent.com/mrdoob/three.js/dev/examples/textures/planets/earth_normal_2048.jpg',
                    'https://threejs.org/examples/textures/planets/earth_normal_2048.jpg'
                ]),
                this.loadTextureWithFallback(loader, [
                    'https://raw.githubusercontent.com/mrdoob/three.js/dev/examples/textures/planets/earth_specular_2048.jpg',
                    'https://threejs.org/examples/textures/planets/earth_specular_2048.jpg'
                ])
            ];
            
            const [dayTexture, nightTexture, normalTexture, specularTexture] = await Promise.all(texturePromises);
            
            this.dayTexture = dayTexture;
            this.nightTexture = nightTexture;
            this.normalTexture = normalTexture;
            this.specularTexture = specularTexture;
            
            // 设置纹理属性
            [this.dayTexture, this.nightTexture, this.normalTexture, this.specularTexture].forEach(texture => {
                if (texture) {
                    texture.wrapS = THREE.RepeatWrapping;
                    texture.wrapT = THREE.RepeatWrapping;
                    texture.anisotropy = 16;
                }
            });
            
        } catch (error) {
            console.warn('部分纹理加载失败，使用默认纹理:', error);
            this.createFallbackTextures();
        }
    }
    
    async loadTextureWithFallback(loader, urls) {
        for (const url of urls) {
            try {
                return await new Promise((resolve, reject) => {
                    loader.load(url, resolve, undefined, reject);
                });
            } catch (error) {
                console.warn(`纹理加载失败: ${url}`, error);
                continue;
            }
        }
        throw new Error('所有纹理URL都加载失败');
    }
    
    createFallbackTextures() {
        // 创建简单的程序化纹理作为后备
        const canvas = document.createElement('canvas');
        canvas.width = 512;
        canvas.height = 256;
        const ctx = canvas.getContext('2d');
        
        // 创建简单的地球颜色纹理
        const gradient = ctx.createLinearGradient(0, 0, 0, 256);
        gradient.addColorStop(0, '#4A90E2');  // 蓝色（海洋）
        gradient.addColorStop(0.3, '#7ED321'); // 绿色（陆地）
        gradient.addColorStop(0.7, '#F5A623'); // 黄色（沙漠）
        gradient.addColorStop(1, '#FFFFFF');   // 白色（极地）
        
        ctx.fillStyle = gradient;
        ctx.fillRect(0, 0, 512, 256);
        
        this.dayTexture = new THREE.CanvasTexture(canvas);
        this.dayTexture.wrapS = THREE.RepeatWrapping;
        this.dayTexture.wrapT = THREE.RepeatWrapping;
        
        // 创建简单的夜晚纹理
        ctx.fillStyle = '#001122';
        ctx.fillRect(0, 0, 512, 256);
        
        // 添加一些"城市灯光"
        ctx.fillStyle = '#FFFF88';
        for (let i = 0; i < 100; i++) {
            const x = Math.random() * 512;
            const y = Math.random() * 256;
            ctx.fillRect(x, y, 2, 2);
        }
        
        this.nightTexture = new THREE.CanvasTexture(canvas);
        this.nightTexture.wrapS = THREE.RepeatWrapping;
        this.nightTexture.wrapT = THREE.RepeatWrapping;
    }
    
    createGeometry() {
        // 创建更高精度的球体以支持地形
        this.earthGeometry = new THREE.SphereGeometry(1, 128, 64);

        // 可以在这里添加地形高度图的顶点位移
        this.addTerrainDisplacement();
    }

    addTerrainDisplacement() {
        // 简单的程序化地形生成
        const positions = this.earthGeometry.attributes.position;
        const vertex = new THREE.Vector3();

        for (let i = 0; i < positions.count; i++) {
            vertex.fromBufferAttribute(positions, i);

            // 使用噪声函数生成地形高度
            const height = this.getTerrainHeight(vertex);
            vertex.normalize().multiplyScalar(1 + height * 0.02); // 最大高度2%

            positions.setXYZ(i, vertex.x, vertex.y, vertex.z);
        }

        positions.needsUpdate = true;
        this.earthGeometry.computeVertexNormals();
    }

    getTerrainHeight(vertex) {
        // 简单的噪声函数模拟地形
        const lat = Math.asin(vertex.y);
        const lng = Math.atan2(vertex.z, vertex.x);

        let height = 0;
        height += Math.sin(lat * 8) * 0.3;
        height += Math.sin(lng * 6) * 0.2;
        height += Math.sin(lat * 16 + lng * 12) * 0.1;

        return Math.max(0, height);
    }
    
    createMaterial() {
        // 创建自定义着色器材质以实现日夜过渡效果
        const vertexShader = `
            varying vec2 vUv;
            varying vec3 vNormal;
            varying vec3 vPosition;
            
            void main() {
                vUv = uv;
                vNormal = normalize(normalMatrix * normal);
                vPosition = position;
                gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
            }
        `;
        
        const fragmentShader = `
            uniform sampler2D dayTexture;
            uniform sampler2D nightTexture;
            uniform vec3 sunDirection;
            uniform float atmosphereIntensity;
            
            varying vec2 vUv;
            varying vec3 vNormal;
            varying vec3 vPosition;
            
            void main() {
                vec3 dayColor = texture2D(dayTexture, vUv).rgb;
                vec3 nightColor = texture2D(nightTexture, vUv).rgb;
                
                // 计算太阳光照强度
                float sunIntensity = dot(vNormal, sunDirection);
                sunIntensity = smoothstep(-0.1, 0.1, sunIntensity);
                
                // 混合日夜纹理
                vec3 color = mix(nightColor * 0.3, dayColor, sunIntensity);
                
                // 添加大气散射效果
                float atmosphere = pow(1.0 - dot(vNormal, vec3(0.0, 0.0, 1.0)), 2.0);
                color += vec3(0.3, 0.6, 1.0) * atmosphere * atmosphereIntensity;
                
                gl_FragColor = vec4(color, 1.0);
            }
        `;
        
        this.earthMaterial = new THREE.ShaderMaterial({
            uniforms: {
                dayTexture: { value: this.dayTexture },
                nightTexture: { value: this.nightTexture },
                sunDirection: { value: new THREE.Vector3(1, 0, 0) },
                atmosphereIntensity: { value: 0.1 }
            },
            vertexShader,
            fragmentShader
        });
    }
    
    createMesh() {
        this.earthMesh = new THREE.Mesh(this.earthGeometry, this.earthMaterial);
        this.earthMesh.name = 'Earth';
        this.earthMesh.castShadow = true;
        this.earthMesh.receiveShadow = true;
    }
    
    setupLighting() {
        // 太阳光（方向光）
        this.sunLight = new THREE.DirectionalLight(0xffffff, 1.0);
        this.sunLight.position.set(5, 0, 0);
        this.sunLight.castShadow = true;
        this.sunLight.shadow.mapSize.width = 2048;
        this.sunLight.shadow.mapSize.height = 2048;
        this.sunLight.shadow.camera.near = 0.1;
        this.sunLight.shadow.camera.far = 50;
        this.sunLight.shadow.camera.left = -10;
        this.sunLight.shadow.camera.right = 10;
        this.sunLight.shadow.camera.top = 10;
        this.sunLight.shadow.camera.bottom = -10;
        
        // 环境光
        this.ambientLight = new THREE.AmbientLight(0x404040, 0.2);
    }
    
    setupGroup() {
        this.group.add(this.earthMesh);
        this.group.add(this.sunLight);
        this.group.add(this.ambientLight);
    }
    
    update(rotationSpeed = 0.001) {
        if (this.earthMesh) {
            this.earthMesh.rotation.y += rotationSpeed;
        }
        
        // 更新太阳方向
        if (this.earthMaterial && this.earthMaterial.uniforms) {
            const time = Date.now() * 0.0001;
            this.earthMaterial.uniforms.sunDirection.value.set(
                Math.cos(time),
                0,
                Math.sin(time)
            );
        }
    }
    
    getGroup() {
        return this.group;
    }
    
    getEarthMesh() {
        return this.earthMesh;
    }
    
    dispose() {
        if (this.earthGeometry) this.earthGeometry.dispose();
        if (this.earthMaterial) this.earthMaterial.dispose();
        if (this.dayTexture) this.dayTexture.dispose();
        if (this.nightTexture) this.nightTexture.dispose();
        if (this.normalTexture) this.normalTexture.dispose();
        if (this.specularTexture) this.specularTexture.dispose();
    }
}

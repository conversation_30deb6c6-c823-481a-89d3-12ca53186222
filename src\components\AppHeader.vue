<template>
  <header class="app-header">
    <div class="header-content">
      <!-- Logo和标题 -->
      <div class="brand">
        <div class="logo">
          <svg width="32" height="32" viewBox="0 0 32 32" fill="none">
            <circle cx="16" cy="16" r="14" stroke="currentColor" stroke-width="2" fill="none"/>
            <path d="M8 16c0-4.4 3.6-8 8-8s8 3.6 8 8" stroke="currentColor" stroke-width="2" fill="none"/>
            <path d="M12 20c0-2.2 1.8-4 4-4s4 1.8 4 4" stroke="currentColor" stroke-width="2" fill="none"/>
            <circle cx="16" cy="16" r="2" fill="currentColor"/>
          </svg>
        </div>
        <div class="brand-text">
          <h1 class="title">Vue Earth</h1>
          <span class="subtitle">3D地球浏览器</span>
        </div>
      </div>
      
      <!-- 搜索栏 -->
      <div class="search-section">
        <div class="search-container">
          <div class="search-input-wrapper">
            <svg class="search-icon" width="20" height="20" viewBox="0 0 24 24" fill="none">
              <circle cx="11" cy="11" r="8" stroke="currentColor" stroke-width="2"/>
              <path d="m21 21-4.35-4.35" stroke="currentColor" stroke-width="2"/>
            </svg>
            <input
              ref="searchInput"
              v-model="searchQuery"
              type="text"
              class="search-input"
              placeholder="搜索城市、地标或坐标..."
              @keypress.enter="handleSearch"
              @input="handleSearchInput"
              @focus="searchFocused = true"
              @blur="handleSearchBlur"
            />
            <button 
              v-if="searchQuery"
              class="clear-button"
              @click="clearSearch"
            >
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                <line x1="18" y1="6" x2="6" y2="18" stroke="currentColor" stroke-width="2"/>
                <line x1="6" y1="6" x2="18" y2="18" stroke="currentColor" stroke-width="2"/>
              </svg>
            </button>
          </div>
          
          <!-- 搜索建议 -->
          <div 
            v-if="showSuggestions && suggestions.length > 0"
            class="search-suggestions"
          >
            <div
              v-for="(suggestion, index) in suggestions"
              :key="index"
              class="suggestion-item"
              @click="selectSuggestion(suggestion)"
            >
              <svg class="suggestion-icon" width="16" height="16" viewBox="0 0 24 24" fill="none">
                <path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z" stroke="currentColor" stroke-width="2"/>
                <circle cx="12" cy="10" r="3" stroke="currentColor" stroke-width="2"/>
              </svg>
              <div class="suggestion-content">
                <div class="suggestion-name">{{ suggestion.name }}</div>
                <div class="suggestion-type">{{ suggestion.type }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 控制按钮组 -->
      <div class="controls">
        <div class="control-group">
          <!-- 图层控制 -->
          <div class="layer-controls">
            <button
              v-for="layer in layers"
              :key="layer.key"
              class="layer-button"
              :class="{ active: layer.active }"
              :title="layer.tooltip"
              @click="toggleLayer(layer.key)"
            >
              <component :is="layer.icon" />
            </button>
          </div>
          
          <!-- 视图控制 -->
          <div class="view-controls">
            <button class="control-button" title="重置视图 (R)" @click="resetView">
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                <path d="M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8" stroke="currentColor" stroke-width="2"/>
                <path d="M21 3v5h-5" stroke="currentColor" stroke-width="2"/>
                <path d="M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16" stroke="currentColor" stroke-width="2"/>
                <path d="M8 16H3v5" stroke="currentColor" stroke-width="2"/>
              </svg>
            </button>
            
            <button class="control-button" title="全屏 (F11)" @click="toggleFullscreen">
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                <path d="M8 3H5a2 2 0 0 0-2 2v3m18 0V5a2 2 0 0 0-2-2h-3m0 18h3a2 2 0 0 0 2-2v-3M3 16v3a2 2 0 0 0 2 2h3" stroke="currentColor" stroke-width="2"/>
              </svg>
            </button>
            
            <button class="control-button" title="控制面板 (C)" @click="openControlPanel">
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                <rect x="3" y="3" width="6" height="6" stroke="currentColor" stroke-width="2"/>
                <rect x="15" y="3" width="6" height="6" stroke="currentColor" stroke-width="2"/>
                <rect x="3" y="15" width="6" height="6" stroke="currentColor" stroke-width="2"/>
                <rect x="15" y="15" width="6" height="6" stroke="currentColor" stroke-width="2"/>
              </svg>
            </button>

            <button class="control-button" title="设置" @click="openSettings">
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                <circle cx="12" cy="12" r="3" stroke="currentColor" stroke-width="2"/>
                <path d="M12 1v6m0 6v6m11-7h-6m-6 0H1" stroke="currentColor" stroke-width="2"/>
              </svg>
            </button>
          </div>
        </div>
      </div>
    </div>
  </header>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'

// Props
const emit = defineEmits(['search', 'toggle-layer', 'reset-view', 'open-settings', 'open-control-panel'])

// 响应式数据
const searchQuery = ref('')
const searchFocused = ref(false)
const suggestions = ref([])
const showSuggestions = ref(false)

// 图层配置
const layers = ref([
  {
    key: 'showAtmosphere',
    active: true,
    tooltip: '大气层 (1)',
    icon: 'AtmosphereIcon'
  },
  {
    key: 'showClouds',
    active: true,
    tooltip: '云层 (2)',
    icon: 'CloudIcon'
  },
  {
    key: 'showStars',
    active: true,
    tooltip: '星空 (3)',
    icon: 'StarIcon'
  },
  {
    key: 'showLabels',
    active: true,
    tooltip: '标签 (4)',
    icon: 'LabelIcon'
  }
])

// 搜索数据
const searchData = [
  // 中国主要城市
  { name: '北京', type: '首都', lat: 39.9042, lng: 116.4074 },
  { name: '上海', type: '直辖市', lat: 31.2304, lng: 121.4737 },
  { name: '广州', type: '省会', lat: 23.1291, lng: 113.2644 },
  { name: '深圳', type: '特区', lat: 22.5431, lng: 114.0579 },
  { name: '杭州', type: '省会', lat: 30.2741, lng: 120.1551 },
  { name: '南京', type: '省会', lat: 32.0603, lng: 118.7969 },
  { name: '成都', type: '省会', lat: 30.5728, lng: 104.0668 },
  { name: '重庆', type: '直辖市', lat: 29.5630, lng: 106.5516 },
  { name: '西安', type: '省会', lat: 34.3416, lng: 108.9398 },
  { name: '武汉', type: '省会', lat: 30.5928, lng: 114.3055 },

  // 国际主要城市
  { name: '纽约', type: '城市', lat: 40.7128, lng: -74.0060 },
  { name: '伦敦', type: '首都', lat: 51.5074, lng: -0.1278 },
  { name: '巴黎', type: '首都', lat: 48.8566, lng: 2.3522 },
  { name: '东京', type: '首都', lat: 35.6762, lng: 139.6503 },
  { name: '悉尼', type: '城市', lat: -33.8688, lng: 151.2093 },
  { name: '莫斯科', type: '首都', lat: 55.7558, lng: 37.6176 },
  { name: '洛杉矶', type: '城市', lat: 34.0522, lng: -118.2437 },
  { name: '孟买', type: '城市', lat: 19.0760, lng: 72.8777 },
  { name: '迪拜', type: '城市', lat: 25.2048, lng: 55.2708 },
  { name: '新加坡', type: '首都', lat: 1.3521, lng: 103.8198 },
  { name: '香港', type: '特区', lat: 22.3193, lng: 114.1694 },
  { name: '台北', type: '城市', lat: 25.0330, lng: 121.5654 },

  // 著名地标
  { name: '埃菲尔铁塔', type: '地标', lat: 48.8584, lng: 2.2945 },
  { name: '自由女神像', type: '地标', lat: 40.6892, lng: -74.0445 },
  { name: '长城', type: '地标', lat: 40.4319, lng: 116.5704 },
  { name: '金字塔', type: '地标', lat: 29.9792, lng: 31.1342 },
  { name: '泰姬陵', type: '地标', lat: 27.1751, lng: 78.0421 },
  { name: '马丘比丘', type: '地标', lat: -13.1631, lng: -72.5450 },
  { name: '富士山', type: '地标', lat: 35.3606, lng: 138.7274 },
  { name: '珠穆朗玛峰', type: '地标', lat: 27.9881, lng: 86.9250 }
]

// 方法
function handleSearch() {
  if (searchQuery.value.trim()) {
    emit('search', searchQuery.value.trim())
    showSuggestions.value = false
  }
}

function handleSearchInput() {
  if (searchQuery.value.length >= 1) {
    const lowerQuery = searchQuery.value.toLowerCase().trim()
    const filtered = searchData.filter(item =>
      item.name.toLowerCase().includes(lowerQuery) ||
      item.type.toLowerCase().includes(lowerQuery)
    )
    suggestions.value = filtered.slice(0, 6)
    showSuggestions.value = filtered.length > 0
  } else {
    showSuggestions.value = false
    suggestions.value = []
  }
}

function handleSearchBlur() {
  // 延迟隐藏建议，以便点击建议项
  setTimeout(() => {
    searchFocused.value = false
    showSuggestions.value = false
  }, 200)
}

function selectSuggestion(suggestion) {
  searchQuery.value = suggestion.name
  emit('search', suggestion.name)
  showSuggestions.value = false
}

function clearSearch() {
  searchQuery.value = ''
  showSuggestions.value = false
}

function toggleLayer(layerKey) {
  const layer = layers.value.find(l => l.key === layerKey)
  if (layer) {
    layer.active = !layer.active
    emit('toggle-layer', layerKey)
  }
}

function resetView() {
  emit('reset-view')
}

function toggleFullscreen() {
  if (!document.fullscreenElement) {
    document.documentElement.requestFullscreen()
  } else {
    document.exitFullscreen()
  }
}

function openSettings() {
  emit('open-settings')
}

// 添加控制面板按钮
function openControlPanel() {
  emit('open-control-panel')
}
</script>

<style lang="scss" scoped>
.app-header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  padding: 16px 24px;
  pointer-events: none;
  
  .header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    max-width: 1400px;
    margin: 0 auto;
    gap: 24px;
  }
}

.brand {
  display: flex;
  align-items: center;
  gap: 12px;
  pointer-events: auto;
  
  .logo {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--primary-color);
    border-radius: var(--radius-md);
    color: white;
  }
  
  .brand-text {
    .title {
      font-size: 20px;
      font-weight: 700;
      color: var(--text-primary);
      margin: 0;
      line-height: 1;
    }
    
    .subtitle {
      font-size: 12px;
      color: var(--text-tertiary);
      line-height: 1;
    }
  }
}

.search-section {
  flex: 1;
  max-width: 600px;
  position: relative;
  pointer-events: auto;
  
  .search-container {
    position: relative;
  }
  
  .search-input-wrapper {
    position: relative;
    display: flex;
    align-items: center;
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(20px);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-xl);
    padding: 0 16px;
    transition: all var(--transition-fast);
    
    &:focus-within {
      border-color: var(--primary-color);
      box-shadow: 0 0 0 3px rgba(66, 133, 244, 0.2);
    }
    
    .search-icon {
      color: var(--text-tertiary);
      margin-right: 12px;
    }
    
    .search-input {
      flex: 1;
      background: none;
      border: none;
      outline: none;
      color: var(--text-primary);
      font-size: 16px;
      padding: 14px 0;
      
      &::placeholder {
        color: var(--text-disabled);
      }
    }
    
    .clear-button {
      background: none;
      border: none;
      color: var(--text-tertiary);
      cursor: pointer;
      padding: 4px;
      border-radius: 50%;
      transition: all var(--transition-fast);
      
      &:hover {
        color: var(--text-primary);
        background: rgba(255, 255, 255, 0.1);
      }
    }
  }
  
  .search-suggestions {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: rgba(0, 0, 0, 0.9);
    backdrop-filter: blur(20px);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    margin-top: 8px;
    overflow: hidden;
    box-shadow: var(--shadow-lg);
    
    .suggestion-item {
      display: flex;
      align-items: center;
      gap: 12px;
      padding: 12px 16px;
      cursor: pointer;
      transition: background var(--transition-fast);
      border-bottom: 1px solid rgba(255, 255, 255, 0.1);
      
      &:last-child {
        border-bottom: none;
      }
      
      &:hover {
        background: rgba(66, 133, 244, 0.2);
      }
      
      .suggestion-icon {
        color: var(--text-tertiary);
      }
      
      .suggestion-content {
        .suggestion-name {
          font-size: 14px;
          color: var(--text-primary);
          font-weight: 500;
        }
        
        .suggestion-type {
          font-size: 12px;
          color: var(--text-tertiary);
        }
      }
    }
  }
}

.controls {
  pointer-events: auto;
  
  .control-group {
    display: flex;
    align-items: center;
    gap: 16px;
  }
  
  .layer-controls {
    display: flex;
    gap: 8px;
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(20px);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    padding: 8px;
  }
  
  .view-controls {
    display: flex;
    gap: 8px;
  }
  
  .layer-button,
  .control-button {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: transparent;
    border: none;
    border-radius: var(--radius-md);
    color: var(--text-secondary);
    cursor: pointer;
    transition: all var(--transition-fast);
    
    &:hover {
      background: rgba(255, 255, 255, 0.1);
      color: var(--text-primary);
      transform: translateY(-1px);
    }
    
    &.active {
      background: var(--primary-color);
      color: white;
    }
  }
  
  .control-button {
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(20px);
    border: 1px solid var(--border-color);
  }
}

// 响应式设计
@media (max-width: 1024px) {
  .app-header {
    padding: 12px 16px;
    
    .header-content {
      gap: 16px;
    }
  }
  
  .brand {
    .brand-text {
      display: none;
    }
  }
  
  .search-section {
    max-width: none;
  }
}

@media (max-width: 768px) {
  .app-header {
    .header-content {
      flex-wrap: wrap;
      gap: 12px;
    }
  }
  
  .search-section {
    order: 3;
    width: 100%;
  }
  
  .controls {
    .control-group {
      gap: 8px;
    }
    
    .layer-controls {
      padding: 6px;
    }
    
    .layer-button,
    .control-button {
      width: 36px;
      height: 36px;
    }
  }
}
</style>

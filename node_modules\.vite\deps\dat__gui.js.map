{"version": 3, "sources": ["../../dat.gui/src/dat/color/toString.js", "../../dat.gui/src/dat/utils/common.js", "../../dat.gui/src/dat/color/interpret.js", "../../dat.gui/src/dat/color/math.js", "../../dat.gui/src/dat/color/Color.js", "../../dat.gui/src/dat/controllers/Controller.js", "../../dat.gui/src/dat/dom/dom.js", "../../dat.gui/src/dat/controllers/BooleanController.js", "../../dat.gui/src/dat/controllers/OptionController.js", "../../dat.gui/src/dat/controllers/StringController.js", "../../dat.gui/src/dat/controllers/NumberController.js", "../../dat.gui/src/dat/controllers/NumberControllerBox.js", "../../dat.gui/src/dat/controllers/NumberControllerSlider.js", "../../dat.gui/src/dat/controllers/FunctionController.js", "../../dat.gui/src/dat/controllers/ColorController.js", "../../dat.gui/src/dat/utils/css.js", "../../dat.gui/src/dat/gui/saveDialogue.html.js", "../../dat.gui/src/dat/controllers/ControllerFactory.js", "../../dat.gui/src/dat/utils/requestAnimationFrame.js", "../../dat.gui/src/dat/dom/CenteredDiv.js", "../../dat.gui/src/dat/gui/GUI.js", "../../dat.gui/src/dat/index.js"], "sourcesContent": ["/**\n * dat-gui JavaScript Controller Library\n * https://github.com/dataarts/dat.gui\n *\n * Copyright 2011 Data Arts Team, Google Creative Lab\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n */\n\nexport default function(color, forceCSSHex) {\n  const colorFormat = color.__state.conversionName.toString();\n\n  const r = Math.round(color.r);\n  const g = Math.round(color.g);\n  const b = Math.round(color.b);\n  const a = color.a;\n  const h = Math.round(color.h);\n  const s = color.s.toFixed(1);\n  const v = color.v.toFixed(1);\n\n  if (forceCSSHex || (colorFormat === 'THREE_CHAR_HEX') || (colorFormat === 'SIX_CHAR_HEX')) {\n    let str = color.hex.toString(16);\n    while (str.length < 6) {\n      str = '0' + str;\n    }\n    return '#' + str;\n  } else if (colorFormat === 'CSS_RGB') {\n    return 'rgb(' + r + ',' + g + ',' + b + ')';\n  } else if (colorFormat === 'CSS_RGBA') {\n    return 'rgba(' + r + ',' + g + ',' + b + ',' + a + ')';\n  } else if (colorFormat === 'HEX') {\n    return '0x' + color.hex.toString(16);\n  } else if (colorFormat === 'RGB_ARRAY') {\n    return '[' + r + ',' + g + ',' + b + ']';\n  } else if (colorFormat === 'RGBA_ARRAY') {\n    return '[' + r + ',' + g + ',' + b + ',' + a + ']';\n  } else if (colorFormat === 'RGB_OBJ') {\n    return '{r:' + r + ',g:' + g + ',b:' + b + '}';\n  } else if (colorFormat === 'RGBA_OBJ') {\n    return '{r:' + r + ',g:' + g + ',b:' + b + ',a:' + a + '}';\n  } else if (colorFormat === 'HSV_OBJ') {\n    return '{h:' + h + ',s:' + s + ',v:' + v + '}';\n  } else if (colorFormat === 'HSVA_OBJ') {\n    return '{h:' + h + ',s:' + s + ',v:' + v + ',a:' + a + '}';\n  }\n\n  return 'unknown format';\n}\n", "/**\n * dat-gui JavaScript Controller Library\n * https://github.com/dataarts/dat.gui\n *\n * Copyright 2011 Data Arts Team, Google Creative Lab\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n */\n\nconst ARR_EACH = Array.prototype.forEach;\nconst ARR_SLICE = Array.prototype.slice;\n\n/**\n * Band-aid methods for things that should be a lot easier in JavaScript.\n * Implementation and structure inspired by underscore.js\n * http://documentcloud.github.com/underscore/\n */\n\nconst Common = {\n  BREAK: {},\n\n  extend: function(target) {\n    this.each(ARR_SLICE.call(arguments, 1), function(obj) {\n      const keys = this.isObject(obj) ? Object.keys(obj) : [];\n      keys.forEach(function(key) {\n        if (!this.isUndefined(obj[key])) {\n          target[key] = obj[key];\n        }\n      }.bind(this));\n    }, this);\n\n    return target;\n  },\n\n  defaults: function(target) {\n    this.each(ARR_SLICE.call(arguments, 1), function(obj) {\n      const keys = this.isObject(obj) ? Object.keys(obj) : [];\n      keys.forEach(function(key) {\n        if (this.isUndefined(target[key])) {\n          target[key] = obj[key];\n        }\n      }.bind(this));\n    }, this);\n\n    return target;\n  },\n\n  compose: function() {\n    const toCall = ARR_SLICE.call(arguments);\n    return function() {\n      let args = ARR_SLICE.call(arguments);\n      for (let i = toCall.length - 1; i >= 0; i--) {\n        args = [toCall[i].apply(this, args)];\n      }\n      return args[0];\n    };\n  },\n\n  each: function(obj, itr, scope) {\n    if (!obj) {\n      return;\n    }\n\n    if (ARR_EACH && obj.forEach && obj.forEach === ARR_EACH) {\n      obj.forEach(itr, scope);\n    } else if (obj.length === obj.length + 0) { // Is number but not NaN\n      let key;\n      let l;\n      for (key = 0, l = obj.length; key < l; key++) {\n        if (key in obj && itr.call(scope, obj[key], key) === this.BREAK) {\n          return;\n        }\n      }\n    } else {\n      for (const key in obj) {\n        if (itr.call(scope, obj[key], key) === this.BREAK) {\n          return;\n        }\n      }\n    }\n  },\n\n  defer: function(fnc) {\n    setTimeout(fnc, 0);\n  },\n\n  // if the function is called repeatedly, wait until threshold passes until we execute the function\n  debounce: function(func, threshold, callImmediately) {\n    let timeout;\n\n    return function() {\n      const obj = this;\n      const args = arguments;\n      function delayed() {\n        timeout = null;\n        if (!callImmediately) func.apply(obj, args);\n      }\n\n      const callNow = callImmediately || !timeout;\n\n      clearTimeout(timeout);\n      timeout = setTimeout(delayed, threshold);\n\n      if (callNow) {\n        func.apply(obj, args);\n      }\n    };\n  },\n\n  toArray: function(obj) {\n    if (obj.toArray) return obj.toArray();\n    return ARR_SLICE.call(obj);\n  },\n\n  isUndefined: function(obj) {\n    return obj === undefined;\n  },\n\n  isNull: function(obj) {\n    return obj === null;\n  },\n\n  isNaN: function(obj) {\n    return isNaN(obj);\n  },\n\n  isArray: Array.isArray || function(obj) {\n    return obj.constructor === Array;\n  },\n\n  isObject: function(obj) {\n    return obj === Object(obj);\n  },\n\n  isNumber: function(obj) {\n    return obj === obj + 0;\n  },\n\n  isString: function(obj) {\n    return obj === obj + '';\n  },\n\n  isBoolean: function(obj) {\n    return obj === false || obj === true;\n  },\n\n  isFunction: function(obj) {\n    return obj instanceof Function;\n  }\n\n};\n\nexport default Common;\n", "/**\n * dat-gui JavaScript Controller Library\n * https://github.com/dataarts/dat.gui\n *\n * Copyright 2011 Data Arts Team, Google Creative Lab\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n */\n\nimport toString from './toString';\nimport common from '../utils/common';\n\nconst INTERPRETATIONS = [\n  // Strings\n  {\n    litmus: common.isString,\n    conversions: {\n      THREE_CHAR_HEX: {\n        read: function(original) {\n          const test = original.match(/^#([A-F0-9])([A-F0-9])([A-F0-9])$/i);\n          if (test === null) {\n            return false;\n          }\n\n          return {\n            space: 'HEX',\n            hex: parseInt(\n              '0x' +\n              test[1].toString() + test[1].toString() +\n              test[2].toString() + test[2].toString() +\n              test[3].toString() + test[3].toString(), 0\n            )\n          };\n        },\n\n        write: toString\n      },\n\n      SIX_CHAR_HEX: {\n        read: function(original) {\n          const test = original.match(/^#([A-F0-9]{6})$/i);\n          if (test === null) {\n            return false;\n          }\n\n          return {\n            space: 'HEX',\n            hex: parseInt('0x' + test[1].toString(), 0)\n          };\n        },\n\n        write: toString\n      },\n\n      CSS_RGB: {\n        read: function(original) {\n          const test = original.match(/^rgb\\(\\s*(\\S+)\\s*,\\s*(\\S+)\\s*,\\s*(\\S+)\\s*\\)/);\n          if (test === null) {\n            return false;\n          }\n\n          return {\n            space: 'RGB',\n            r: parseFloat(test[1]),\n            g: parseFloat(test[2]),\n            b: parseFloat(test[3])\n          };\n        },\n\n        write: toString\n      },\n\n      CSS_RGBA: {\n        read: function(original) {\n          const test = original.match(/^rgba\\(\\s*(\\S+)\\s*,\\s*(\\S+)\\s*,\\s*(\\S+)\\s*,\\s*(\\S+)\\s*\\)/);\n          if (test === null) {\n            return false;\n          }\n\n          return {\n            space: 'RGB',\n            r: parseFloat(test[1]),\n            g: parseFloat(test[2]),\n            b: parseFloat(test[3]),\n            a: parseFloat(test[4])\n          };\n        },\n\n        write: toString\n      }\n    }\n  },\n\n  // Numbers\n  {\n    litmus: common.isNumber,\n\n    conversions: {\n\n      HEX: {\n        read: function(original) {\n          return {\n            space: 'HEX',\n            hex: original,\n            conversionName: 'HEX'\n          };\n        },\n\n        write: function(color) {\n          return color.hex;\n        }\n      }\n\n    }\n\n  },\n\n  // Arrays\n  {\n    litmus: common.isArray,\n    conversions: {\n      RGB_ARRAY: {\n        read: function(original) {\n          if (original.length !== 3) {\n            return false;\n          }\n\n          return {\n            space: 'RGB',\n            r: original[0],\n            g: original[1],\n            b: original[2]\n          };\n        },\n\n        write: function(color) {\n          return [color.r, color.g, color.b];\n        }\n      },\n\n      RGBA_ARRAY: {\n        read: function(original) {\n          if (original.length !== 4) return false;\n          return {\n            space: 'RGB',\n            r: original[0],\n            g: original[1],\n            b: original[2],\n            a: original[3]\n          };\n        },\n\n        write: function(color) {\n          return [color.r, color.g, color.b, color.a];\n        }\n      }\n    }\n  },\n\n  // Objects\n  {\n    litmus: common.isObject,\n    conversions: {\n\n      RGBA_OBJ: {\n        read: function(original) {\n          if (common.isNumber(original.r) &&\n            common.isNumber(original.g) &&\n            common.isNumber(original.b) &&\n            common.isNumber(original.a)) {\n            return {\n              space: 'RGB',\n              r: original.r,\n              g: original.g,\n              b: original.b,\n              a: original.a\n            };\n          }\n          return false;\n        },\n\n        write: function(color) {\n          return {\n            r: color.r,\n            g: color.g,\n            b: color.b,\n            a: color.a\n          };\n        }\n      },\n\n      RGB_OBJ: {\n        read: function(original) {\n          if (common.isNumber(original.r) &&\n            common.isNumber(original.g) &&\n            common.isNumber(original.b)) {\n            return {\n              space: 'RGB',\n              r: original.r,\n              g: original.g,\n              b: original.b\n            };\n          }\n          return false;\n        },\n\n        write: function(color) {\n          return {\n            r: color.r,\n            g: color.g,\n            b: color.b\n          };\n        }\n      },\n\n      HSVA_OBJ: {\n        read: function(original) {\n          if (common.isNumber(original.h) &&\n            common.isNumber(original.s) &&\n            common.isNumber(original.v) &&\n            common.isNumber(original.a)) {\n            return {\n              space: 'HSV',\n              h: original.h,\n              s: original.s,\n              v: original.v,\n              a: original.a\n            };\n          }\n          return false;\n        },\n\n        write: function(color) {\n          return {\n            h: color.h,\n            s: color.s,\n            v: color.v,\n            a: color.a\n          };\n        }\n      },\n\n      HSV_OBJ: {\n        read: function(original) {\n          if (common.isNumber(original.h) &&\n            common.isNumber(original.s) &&\n            common.isNumber(original.v)) {\n            return {\n              space: 'HSV',\n              h: original.h,\n              s: original.s,\n              v: original.v\n            };\n          }\n          return false;\n        },\n\n        write: function(color) {\n          return {\n            h: color.h,\n            s: color.s,\n            v: color.v\n          };\n        }\n      }\n    }\n  }\n];\n\nlet result;\nlet toReturn;\n\nconst interpret = function() {\n  toReturn = false;\n\n  const original = arguments.length > 1 ? common.toArray(arguments) : arguments[0];\n  common.each(INTERPRETATIONS, function(family) {\n    if (family.litmus(original)) {\n      common.each(family.conversions, function(conversion, conversionName) {\n        result = conversion.read(original);\n\n        if (toReturn === false && result !== false) {\n          toReturn = result;\n          result.conversionName = conversionName;\n          result.conversion = conversion;\n          return common.BREAK;\n        }\n      });\n\n      return common.BREAK;\n    }\n  });\n\n  return toReturn;\n};\n\nexport default interpret;\n", "/**\n * dat-gui JavaScript Controller Library\n * https://github.com/dataarts/dat.gui\n *\n * Copyright 2011 Data Arts Team, Google Creative Lab\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n */\n\nlet tmpComponent;\n\nconst ColorMath = {\n  hsv_to_rgb: function(h, s, v) {\n    const hi = Math.floor(h / 60) % 6;\n\n    const f = h / 60 - Math.floor(h / 60);\n    const p = v * (1.0 - s);\n    const q = v * (1.0 - (f * s));\n    const t = v * (1.0 - ((1.0 - f) * s));\n\n    const c = [\n      [v, t, p],\n      [q, v, p],\n      [p, v, t],\n      [p, q, v],\n      [t, p, v],\n      [v, p, q]\n    ][hi];\n\n    return {\n      r: c[0] * 255,\n      g: c[1] * 255,\n      b: c[2] * 255\n    };\n  },\n\n  rgb_to_hsv: function(r, g, b) {\n    const min = Math.min(r, g, b);\n    const max = Math.max(r, g, b);\n    const delta = max - min;\n    let h;\n    let s;\n\n    if (max !== 0) {\n      s = delta / max;\n    } else {\n      return {\n        h: NaN,\n        s: 0,\n        v: 0\n      };\n    }\n\n    if (r === max) {\n      h = (g - b) / delta;\n    } else if (g === max) {\n      h = 2 + (b - r) / delta;\n    } else {\n      h = 4 + (r - g) / delta;\n    }\n    h /= 6;\n    if (h < 0) {\n      h += 1;\n    }\n\n    return {\n      h: h * 360,\n      s: s,\n      v: max / 255\n    };\n  },\n\n  rgb_to_hex: function(r, g, b) {\n    let hex = this.hex_with_component(0, 2, r);\n    hex = this.hex_with_component(hex, 1, g);\n    hex = this.hex_with_component(hex, 0, b);\n    return hex;\n  },\n\n  component_from_hex: function(hex, componentIndex) {\n    return (hex >> (componentIndex * 8)) & 0xFF;\n  },\n\n  hex_with_component: function(hex, componentIndex, value) {\n    return value << (tmpComponent = componentIndex * 8) | (hex & ~(0xFF << tmpComponent));\n  }\n};\n\nexport default ColorMath;\n", "/**\n * dat-gui JavaScript Controller Library\n * https://github.com/dataarts/dat.gui\n *\n * Copyright 2011 Data Arts Team, Google Creative Lab\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n */\n\nimport interpret from './interpret';\nimport math from './math';\nimport colorToString from './toString';\nimport common from '../utils/common';\n\nclass Color {\n  constructor() {\n    this.__state = interpret.apply(this, arguments);\n\n    if (this.__state === false) {\n      throw new Error('Failed to interpret color arguments');\n    }\n\n    this.__state.a = this.__state.a || 1;\n  }\n\n  toString() {\n    return colorToString(this);\n  }\n\n  toHexString() {\n    return colorToString(this, true);\n  }\n\n  toOriginal() {\n    return this.__state.conversion.write(this);\n  }\n}\n\nfunction defineRGBComponent(target, component, componentHexIndex) {\n  Object.defineProperty(target, component, {\n    get: function() {\n      if (this.__state.space === 'RGB') {\n        return this.__state[component];\n      }\n\n      Color.recalculateRGB(this, component, componentHexIndex);\n\n      return this.__state[component];\n    },\n\n    set: function(v) {\n      if (this.__state.space !== 'RGB') {\n        Color.recalculateRGB(this, component, componentHexIndex);\n        this.__state.space = 'RGB';\n      }\n\n      this.__state[component] = v;\n    }\n  });\n}\n\nfunction defineHSVComponent(target, component) {\n  Object.defineProperty(target, component, {\n    get: function() {\n      if (this.__state.space === 'HSV') {\n        return this.__state[component];\n      }\n\n      Color.recalculateHSV(this);\n\n      return this.__state[component];\n    },\n\n    set: function(v) {\n      if (this.__state.space !== 'HSV') {\n        Color.recalculateHSV(this);\n        this.__state.space = 'HSV';\n      }\n\n      this.__state[component] = v;\n    }\n  });\n}\n\n\nColor.recalculateRGB = function(color, component, componentHexIndex) {\n  if (color.__state.space === 'HEX') {\n    color.__state[component] = math.component_from_hex(color.__state.hex, componentHexIndex);\n  } else if (color.__state.space === 'HSV') {\n    common.extend(color.__state, math.hsv_to_rgb(color.__state.h, color.__state.s, color.__state.v));\n  } else {\n    throw new Error('Corrupted color state');\n  }\n};\n\nColor.recalculateHSV = function(color) {\n  const result = math.rgb_to_hsv(color.r, color.g, color.b);\n\n  common.extend(color.__state,\n    {\n      s: result.s,\n      v: result.v\n    });\n\n  if (!common.isNaN(result.h)) {\n    color.__state.h = result.h;\n  } else if (common.isUndefined(color.__state.h)) {\n    color.__state.h = 0;\n  }\n};\n\nColor.COMPONENTS = ['r', 'g', 'b', 'h', 's', 'v', 'hex', 'a'];\n\ndefineRGBComponent(Color.prototype, 'r', 2);\ndefineRGBComponent(Color.prototype, 'g', 1);\ndefineRGBComponent(Color.prototype, 'b', 0);\n\ndefineHSVComponent(Color.prototype, 'h');\ndefineHSVComponent(Color.prototype, 's');\ndefineHSVComponent(Color.prototype, 'v');\n\nObject.defineProperty(Color.prototype, 'a', {\n  get: function() {\n    return this.__state.a;\n  },\n\n  set: function(v) {\n    this.__state.a = v;\n  }\n});\n\nObject.defineProperty(Color.prototype, 'hex', {\n  get: function() {\n    if (this.__state.space !== 'HEX') {\n      this.__state.hex = math.rgb_to_hex(this.r, this.g, this.b);\n      this.__state.space = 'HEX';\n    }\n\n    return this.__state.hex;\n  },\n\n  set: function(v) {\n    this.__state.space = 'HEX';\n    this.__state.hex = v;\n  }\n});\n\nexport default Color;\n", "/**\n * dat-gui JavaScript Controller Library\n * https://github.com/dataarts/dat.gui\n *\n * Copyright 2011 Data Arts Team, Google Creative Lab\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n */\n\n/**\n * @class An \"abstract\" class that represents a given property of an object.\n *\n * @param {Object} object The object to be manipulated\n * @param {string} property The name of the property to be manipulated\n */\nclass Controller {\n  constructor(object, property) {\n    this.initialValue = object[property];\n\n    /**\n     * Those who extend this class will put their DOM elements in here.\n     * @type {DOMElement}\n     */\n    this.domElement = document.createElement('div');\n\n    /**\n     * The object to manipulate\n     * @type {Object}\n     */\n    this.object = object;\n\n    /**\n     * The name of the property to manipulate\n     * @type {String}\n     */\n    this.property = property;\n\n    /**\n     * The function to be called on change.\n     * @type {Function}\n     * @ignore\n     */\n    this.__onChange = undefined;\n\n    /**\n     * The function to be called on finishing change.\n     * @type {Function}\n     * @ignore\n     */\n    this.__onFinishChange = undefined;\n  }\n\n  /**\n   * Specify that a function fire every time someone changes the value with\n   * this Controller.\n   *\n   * @param {Function} fnc This function will be called whenever the value\n   * is modified via this Controller.\n   * @returns {Controller} this\n   */\n  onChange(fnc) {\n    this.__onChange = fnc;\n    return this;\n  }\n\n  /**\n   * Specify that a function fire every time someone \"finishes\" changing\n   * the value wih this Controller. Useful for values that change\n   * incrementally like numbers or strings.\n   *\n   * @param {Function} fnc This function will be called whenever\n   * someone \"finishes\" changing the value via this Controller.\n   * @returns {Controller} this\n   */\n  onFinishChange(fnc) {\n    this.__onFinishChange = fnc;\n    return this;\n  }\n\n  /**\n   * Change the value of <code>object[property]</code>\n   *\n   * @param {Object} newValue The new value of <code>object[property]</code>\n   */\n  setValue(newValue) {\n    this.object[this.property] = newValue;\n    if (this.__onChange) {\n      this.__onChange.call(this, newValue);\n    }\n\n    this.updateDisplay();\n    return this;\n  }\n\n  /**\n   * Gets the value of <code>object[property]</code>\n   *\n   * @returns {Object} The current value of <code>object[property]</code>\n   */\n  getValue() {\n    return this.object[this.property];\n  }\n\n  /**\n   * Refreshes the visual display of a Controller in order to keep sync\n   * with the object's current value.\n   * @returns {Controller} this\n   */\n  updateDisplay() {\n    return this;\n  }\n\n  /**\n   * @returns {boolean} true if the value has deviated from initialValue\n   */\n  isModified() {\n    return this.initialValue !== this.getValue();\n  }\n}\n\nexport default Controller;\n", "/**\n * dat-gui JavaScript Controller Library\n * https://github.com/dataarts/dat.gui\n *\n * Copyright 2011 Data Arts Team, Google Creative Lab\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n */\n\nimport common from '../utils/common';\n\nconst EVENT_MAP = {\n  HTMLEvents: ['change'],\n  MouseEvents: ['click', 'mousemove', 'mousedown', 'mouseup', 'mouseover'],\n  KeyboardEvents: ['keydown']\n};\n\nconst EVENT_MAP_INV = {};\ncommon.each(EVENT_MAP, function(v, k) {\n  common.each(v, function(e) {\n    EVENT_MAP_INV[e] = k;\n  });\n});\n\nconst CSS_VALUE_PIXELS = /(\\d+(\\.\\d+)?)px/;\n\nfunction cssValueToPixels(val) {\n  if (val === '0' || common.isUndefined(val)) {\n    return 0;\n  }\n\n  const match = val.match(CSS_VALUE_PIXELS);\n\n  if (!common.isNull(match)) {\n    return parseFloat(match[1]);\n  }\n\n  // TODO ...ems? %?\n\n  return 0;\n}\n\n/**\n * @namespace\n * @member dat.dom\n */\nconst dom = {\n\n  /**\n   *\n   * @param elem\n   * @param selectable\n   */\n  makeSelectable: function(elem, selectable) {\n    if (elem === undefined || elem.style === undefined) return;\n\n    elem.onselectstart = selectable ? function() {\n      return false;\n    } : function() {\n    };\n\n    elem.style.MozUserSelect = selectable ? 'auto' : 'none';\n    elem.style.KhtmlUserSelect = selectable ? 'auto' : 'none';\n    elem.unselectable = selectable ? 'on' : 'off';\n  },\n\n  /**\n   *\n   * @param elem\n   * @param horizontal\n   * @param vert\n   */\n  makeFullscreen: function(elem, hor, vert) {\n    let vertical = vert;\n    let horizontal = hor;\n\n    if (common.isUndefined(horizontal)) {\n      horizontal = true;\n    }\n\n    if (common.isUndefined(vertical)) {\n      vertical = true;\n    }\n\n    elem.style.position = 'absolute';\n\n    if (horizontal) {\n      elem.style.left = 0;\n      elem.style.right = 0;\n    }\n    if (vertical) {\n      elem.style.top = 0;\n      elem.style.bottom = 0;\n    }\n  },\n\n  /**\n   *\n   * @param elem\n   * @param eventType\n   * @param params\n   */\n  fakeEvent: function(elem, eventType, pars, aux) {\n    const params = pars || {};\n    const className = EVENT_MAP_INV[eventType];\n    if (!className) {\n      throw new Error('Event type ' + eventType + ' not supported.');\n    }\n    const evt = document.createEvent(className);\n    switch (className) {\n      case 'MouseEvents':\n      {\n        const clientX = params.x || params.clientX || 0;\n        const clientY = params.y || params.clientY || 0;\n        evt.initMouseEvent(eventType, params.bubbles || false,\n          params.cancelable || true, window, params.clickCount || 1,\n          0, // screen X\n          0, // screen Y\n          clientX, // client X\n          clientY, // client Y\n          false, false, false, false, 0, null);\n        break;\n      }\n      case 'KeyboardEvents':\n      {\n        const init = evt.initKeyboardEvent || evt.initKeyEvent; // webkit || moz\n        common.defaults(params, {\n          cancelable: true,\n          ctrlKey: false,\n          altKey: false,\n          shiftKey: false,\n          metaKey: false,\n          keyCode: undefined,\n          charCode: undefined\n        });\n        init(eventType, params.bubbles || false,\n          params.cancelable, window,\n          params.ctrlKey, params.altKey,\n          params.shiftKey, params.metaKey,\n          params.keyCode, params.charCode);\n        break;\n      }\n      default:\n      {\n        evt.initEvent(eventType, params.bubbles || false, params.cancelable || true);\n        break;\n      }\n    }\n    common.defaults(evt, aux);\n    elem.dispatchEvent(evt);\n  },\n\n  /**\n   *\n   * @param elem\n   * @param event\n   * @param func\n   * @param bool\n   */\n  bind: function(elem, event, func, newBool) {\n    const bool = newBool || false;\n    if (elem.addEventListener) {\n      elem.addEventListener(event, func, bool);\n    } else if (elem.attachEvent) {\n      elem.attachEvent('on' + event, func);\n    }\n    return dom;\n  },\n\n  /**\n   *\n   * @param elem\n   * @param event\n   * @param func\n   * @param bool\n   */\n  unbind: function(elem, event, func, newBool) {\n    const bool = newBool || false;\n    if (elem.removeEventListener) {\n      elem.removeEventListener(event, func, bool);\n    } else if (elem.detachEvent) {\n      elem.detachEvent('on' + event, func);\n    }\n    return dom;\n  },\n\n  /**\n   *\n   * @param elem\n   * @param className\n   */\n  addClass: function(elem, className) {\n    if (elem.className === undefined) {\n      elem.className = className;\n    } else if (elem.className !== className) {\n      const classes = elem.className.split(/ +/);\n      if (classes.indexOf(className) === -1) {\n        classes.push(className);\n        elem.className = classes.join(' ').replace(/^\\s+/, '').replace(/\\s+$/, '');\n      }\n    }\n    return dom;\n  },\n\n  /**\n   *\n   * @param elem\n   * @param className\n   */\n  removeClass: function(elem, className) {\n    if (className) {\n      if (elem.className === className) {\n        elem.removeAttribute('class');\n      } else {\n        const classes = elem.className.split(/ +/);\n        const index = classes.indexOf(className);\n        if (index !== -1) {\n          classes.splice(index, 1);\n          elem.className = classes.join(' ');\n        }\n      }\n    } else {\n      elem.className = undefined;\n    }\n    return dom;\n  },\n\n  hasClass: function(elem, className) {\n    return new RegExp('(?:^|\\\\s+)' + className + '(?:\\\\s+|$)').test(elem.className) || false;\n  },\n\n  /**\n   *\n   * @param elem\n   */\n  getWidth: function(elem) {\n    const style = getComputedStyle(elem);\n\n    return cssValueToPixels(style['border-left-width']) +\n      cssValueToPixels(style['border-right-width']) +\n      cssValueToPixels(style['padding-left']) +\n      cssValueToPixels(style['padding-right']) +\n      cssValueToPixels(style.width);\n  },\n\n  /**\n   *\n   * @param elem\n   */\n  getHeight: function(elem) {\n    const style = getComputedStyle(elem);\n\n    return cssValueToPixels(style['border-top-width']) +\n      cssValueToPixels(style['border-bottom-width']) +\n      cssValueToPixels(style['padding-top']) +\n      cssValueToPixels(style['padding-bottom']) +\n      cssValueToPixels(style.height);\n  },\n\n  /**\n   *\n   * @param el\n   */\n  getOffset: function(el) {\n    let elem = el;\n    const offset = { left: 0, top: 0 };\n    if (elem.offsetParent) {\n      do {\n        offset.left += elem.offsetLeft;\n        offset.top += elem.offsetTop;\n        elem = elem.offsetParent;\n      } while (elem);\n    }\n    return offset;\n  },\n\n  // http://stackoverflow.com/posts/2684561/revisions\n  /**\n   *\n   * @param elem\n   */\n  isActive: function(elem) {\n    return elem === document.activeElement && (elem.type || elem.href);\n  }\n\n};\n\nexport default dom;\n", "/**\n * dat-gui JavaScript Controller Library\n * https://github.com/dataarts/dat.gui\n *\n * Copyright 2011 Data Arts Team, Google Creative Lab\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n */\n\nimport Controller from './Controller';\nimport dom from '../dom/dom';\n\n/**\n * @class Provides a checkbox input to alter the boolean property of an object.\n *\n * @extends dat.controllers.Controller\n *\n * @param {Object} object The object to be manipulated\n * @param {string} property The name of the property to be manipulated\n */\nclass BooleanController extends Controller {\n  constructor(object, property) {\n    super(object, property);\n\n    const _this = this;\n    this.__prev = this.getValue();\n\n    this.__checkbox = document.createElement('input');\n    this.__checkbox.setAttribute('type', 'checkbox');\n\n    function onChange() {\n      _this.setValue(!_this.__prev);\n    }\n\n    dom.bind(this.__checkbox, 'change', onChange, false);\n\n    this.domElement.appendChild(this.__checkbox);\n\n    // Match original value\n    this.updateDisplay();\n  }\n\n  setValue(v) {\n    const toReturn = super.setValue(v);\n    if (this.__onFinishChange) {\n      this.__onFinishChange.call(this, this.getValue());\n    }\n    this.__prev = this.getValue();\n    return toReturn;\n  }\n\n  updateDisplay() {\n    if (this.getValue() === true) {\n      this.__checkbox.setAttribute('checked', 'checked');\n      this.__checkbox.checked = true;\n      this.__prev = true;\n    } else {\n      this.__checkbox.checked = false;\n      this.__prev = false;\n    }\n\n    return super.updateDisplay();\n  }\n}\n\nexport default BooleanController;\n", "/**\n * dat-gui JavaScript Controller Library\n * https://github.com/dataarts/dat.gui\n *\n * Copyright 2011 Data Arts Team, Google Creative Lab\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n */\n\nimport Controller from './Controller';\nimport dom from '../dom/dom';\nimport common from '../utils/common';\n\n/**\n * @class Provides a select input to alter the property of an object, using a\n * list of accepted values.\n *\n * @extends dat.controllers.Controller\n *\n * @param {Object} object The object to be manipulated\n * @param {string} property The name of the property to be manipulated\n * @param {Object|string[]} options A map of labels to acceptable values, or\n * a list of acceptable string values.\n */\nclass OptionController extends Controller {\n  constructor(object, property, opts) {\n    super(object, property);\n\n    let options = opts;\n\n    const _this = this;\n\n    /**\n     * The drop down menu\n     * @ignore\n     */\n    this.__select = document.createElement('select');\n\n    if (common.isArray(options)) {\n      const map = {};\n      common.each(options, function(element) {\n        map[element] = element;\n      });\n      options = map;\n    }\n\n    common.each(options, function(value, key) {\n      const opt = document.createElement('option');\n      opt.innerHTML = key;\n      opt.setAttribute('value', value);\n      _this.__select.appendChild(opt);\n    });\n\n    // Acknowledge original value\n    this.updateDisplay();\n\n    dom.bind(this.__select, 'change', function() {\n      const desiredValue = this.options[this.selectedIndex].value;\n      _this.setValue(desiredValue);\n    });\n\n    this.domElement.appendChild(this.__select);\n  }\n\n  setValue(v) {\n    const toReturn = super.setValue(v);\n\n    if (this.__onFinishChange) {\n      this.__onFinishChange.call(this, this.getValue());\n    }\n    return toReturn;\n  }\n\n  updateDisplay() {\n    if (dom.isActive(this.__select)) return this; // prevent number from updating if user is trying to manually update\n    this.__select.value = this.getValue();\n    return super.updateDisplay();\n  }\n}\n\nexport default OptionController;\n", "/**\n * dat-gui JavaScript Controller Library\n * https://github.com/dataarts/dat.gui\n *\n * Copyright 2011 Data Arts Team, Google Creative Lab\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n */\n\nimport Controller from './Controller';\nimport dom from '../dom/dom';\n\n/**\n * @class Provides a text input to alter the string property of an object.\n *\n * @extends dat.controllers.Controller\n *\n * @param {Object} object The object to be manipulated\n * @param {string} property The name of the property to be manipulated\n */\nclass StringController extends Controller {\n  constructor(object, property) {\n    super(object, property);\n\n    const _this = this;\n\n    function onChange() {\n      _this.setValue(_this.__input.value);\n    }\n\n    function onBlur() {\n      if (_this.__onFinishChange) {\n        _this.__onFinishChange.call(_this, _this.getValue());\n      }\n    }\n\n    this.__input = document.createElement('input');\n    this.__input.setAttribute('type', 'text');\n\n    dom.bind(this.__input, 'keyup', onChange);\n    dom.bind(this.__input, 'change', onChange);\n    dom.bind(this.__input, 'blur', onBlur);\n    dom.bind(this.__input, 'keydown', function(e) {\n      if (e.keyCode === 13) {\n        this.blur();\n      }\n    });\n\n    this.updateDisplay();\n\n    this.domElement.appendChild(this.__input);\n  }\n\n  updateDisplay() {\n    // Stops the caret from moving on account of:\n    // keyup -> setValue -> updateDisplay\n    if (!dom.isActive(this.__input)) {\n      this.__input.value = this.getValue();\n    }\n    return super.updateDisplay();\n  }\n}\n\nexport default StringController;\n", "/**\n * dat-gui JavaScript Controller Library\n * https://github.com/dataarts/dat.gui\n *\n * Copyright 2011 Data Arts Team, Google Creative Lab\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n */\n\nimport Controller from './Controller';\nimport common from '../utils/common';\n\nfunction numDecimals(x) {\n  const _x = x.toString();\n  if (_x.indexOf('.') > -1) {\n    return _x.length - _x.indexOf('.') - 1;\n  }\n\n  return 0;\n}\n\n/**\n * @class Represents a given property of an object that is a number.\n *\n * @extends dat.controllers.Controller\n *\n * @param {Object} object The object to be manipulated\n * @param {string} property The name of the property to be manipulated\n * @param {Object} [params] Optional parameters\n * @param {Number} [params.min] Minimum allowed value\n * @param {Number} [params.max] Maximum allowed value\n * @param {Number} [params.step] Increment by which to change value\n */\nclass NumberController extends Controller {\n  constructor(object, property, params) {\n    super(object, property);\n\n    const _params = params || {};\n\n    this.__min = _params.min;\n    this.__max = _params.max;\n    this.__step = _params.step;\n\n    if (common.isUndefined(this.__step)) {\n      if (this.initialValue === 0) {\n        this.__impliedStep = 1; // What are we, psychics?\n      } else {\n        // Hey Doug, check this out.\n        this.__impliedStep = Math.pow(10, Math.floor(Math.log(Math.abs(this.initialValue)) / Math.LN10)) / 10;\n      }\n    } else {\n      this.__impliedStep = this.__step;\n    }\n\n    this.__precision = numDecimals(this.__impliedStep);\n  }\n\n  setValue(v) {\n    let _v = v;\n\n    if (this.__min !== undefined && _v < this.__min) {\n      _v = this.__min;\n    } else if (this.__max !== undefined && _v > this.__max) {\n      _v = this.__max;\n    }\n\n    if (this.__step !== undefined && _v % this.__step !== 0) {\n      _v = Math.round(_v / this.__step) * this.__step;\n    }\n\n    return super.setValue(_v);\n  }\n\n  /**\n   * Specify a minimum value for <code>object[property]</code>.\n   *\n   * @param {Number} minValue The minimum value for\n   * <code>object[property]</code>\n   * @returns {dat.controllers.NumberController} this\n   */\n  min(minValue) {\n    this.__min = minValue;\n    return this;\n  }\n\n  /**\n   * Specify a maximum value for <code>object[property]</code>.\n   *\n   * @param {Number} maxValue The maximum value for\n   * <code>object[property]</code>\n   * @returns {dat.controllers.NumberController} this\n   */\n  max(maxValue) {\n    this.__max = maxValue;\n    return this;\n  }\n\n  /**\n   * Specify a step value that dat.controllers.NumberController\n   * increments by.\n   *\n   * @param {Number} stepValue The step value for\n   * dat.controllers.NumberController\n   * @default if minimum and maximum specified increment is 1% of the\n   * difference otherwise stepValue is 1\n   * @returns {dat.controllers.NumberController} this\n   */\n  step(stepValue) {\n    this.__step = stepValue;\n    this.__impliedStep = stepValue;\n    this.__precision = numDecimals(stepValue);\n    return this;\n  }\n}\n\nexport default NumberController;\n", "/**\n * dat-gui JavaScript Controller Library\n * https://github.com/dataarts/dat.gui\n *\n * Copyright 2011 Data Arts Team, Google Creative Lab\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n */\n\nimport NumberController from './NumberController';\nimport dom from '../dom/dom';\nimport common from '../utils/common';\n\nfunction roundToDecimal(value, decimals) {\n  const tenTo = Math.pow(10, decimals);\n  return Math.round(value * tenTo) / tenTo;\n}\n\n/**\n * @class Represents a given property of an object that is a number and\n * provides an input element with which to manipulate it.\n *\n * @extends dat.controllers.Controller\n * @extends dat.controllers.NumberController\n *\n * @param {Object} object The object to be manipulated\n * @param {string} property The name of the property to be manipulated\n * @param {Object} [params] Optional parameters\n * @param {Number} [params.min] Minimum allowed value\n * @param {Number} [params.max] Maximum allowed value\n * @param {Number} [params.step] Increment by which to change value\n */\nclass NumberControllerBox extends NumberController {\n  constructor(object, property, params) {\n    super(object, property, params);\n\n    this.__truncationSuspended = false;\n\n    const _this = this;\n\n    /**\n     * {Number} Previous mouse y position\n     * @ignore\n     */\n    let prevY;\n\n    function onChange() {\n      const attempted = parseFloat(_this.__input.value);\n      if (!common.isNaN(attempted)) {\n        _this.setValue(attempted);\n      }\n    }\n\n    function onFinish() {\n      if (_this.__onFinishChange) {\n        _this.__onFinishChange.call(_this, _this.getValue());\n      }\n    }\n\n    function onBlur() {\n      onFinish();\n    }\n\n    function onMouseDrag(e) {\n      const diff = prevY - e.clientY;\n      _this.setValue(_this.getValue() + diff * _this.__impliedStep);\n\n      prevY = e.clientY;\n    }\n\n    function onMouseUp() {\n      dom.unbind(window, 'mousemove', onMouseDrag);\n      dom.unbind(window, 'mouseup', onMouseUp);\n      onFinish();\n    }\n\n    function onMouseDown(e) {\n      dom.bind(window, 'mousemove', onMouseDrag);\n      dom.bind(window, 'mouseup', onMouseUp);\n      prevY = e.clientY;\n    }\n\n    this.__input = document.createElement('input');\n    this.__input.setAttribute('type', 'text');\n\n    // Makes it so manually specified values are not truncated.\n\n    dom.bind(this.__input, 'change', onChange);\n    dom.bind(this.__input, 'blur', onBlur);\n    dom.bind(this.__input, 'mousedown', onMouseDown);\n    dom.bind(this.__input, 'keydown', function(e) {\n      // When pressing enter, you can be as precise as you want.\n      if (e.keyCode === 13) {\n        _this.__truncationSuspended = true;\n        this.blur();\n        _this.__truncationSuspended = false;\n        onFinish();\n      }\n    });\n\n    this.updateDisplay();\n\n    this.domElement.appendChild(this.__input);\n  }\n\n  updateDisplay() {\n    this.__input.value = this.__truncationSuspended ? this.getValue() : roundToDecimal(this.getValue(), this.__precision);\n    return super.updateDisplay();\n  }\n}\n\nexport default NumberControllerBox;\n", "/**\n * dat-gui JavaScript Controller Library\n * https://github.com/dataarts/dat.gui\n *\n * Copyright 2011 Data Arts Team, Google Creative Lab\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n */\n\nimport NumberController from './NumberController';\nimport dom from '../dom/dom';\n\nfunction map(v, i1, i2, o1, o2) {\n  return o1 + (o2 - o1) * ((v - i1) / (i2 - i1));\n}\n\n/**\n * @class Represents a given property of an object that is a number, contains\n * a minimum and maximum, and provides a slider element with which to\n * manipulate it. It should be noted that the slider element is made up of\n * <code>&lt;div&gt;</code> tags, <strong>not</strong> the html5\n * <code>&lt;slider&gt;</code> element.\n *\n * @extends dat.controllers.Controller\n * @extends dat.controllers.NumberController\n *\n * @param {Object} object The object to be manipulated\n * @param {string} property The name of the property to be manipulated\n * @param {Number} minValue Minimum allowed value\n * @param {Number} maxValue Maximum allowed value\n * @param {Number} stepValue Increment by which to change value\n */\nclass NumberControllerSlider extends NumberController {\n  constructor(object, property, min, max, step) {\n    super(object, property, { min: min, max: max, step: step });\n\n    const _this = this;\n\n    this.__background = document.createElement('div');\n    this.__foreground = document.createElement('div');\n\n    dom.bind(this.__background, 'mousedown', onMouseDown);\n    dom.bind(this.__background, 'touchstart', onTouchStart);\n\n    dom.addClass(this.__background, 'slider');\n    dom.addClass(this.__foreground, 'slider-fg');\n\n    function onMouseDown(e) {\n      document.activeElement.blur();\n\n      dom.bind(window, 'mousemove', onMouseDrag);\n      dom.bind(window, 'mouseup', onMouseUp);\n\n      onMouseDrag(e);\n    }\n\n    function onMouseDrag(e) {\n      e.preventDefault();\n\n      const bgRect = _this.__background.getBoundingClientRect();\n\n      _this.setValue(\n        map(e.clientX, bgRect.left, bgRect.right, _this.__min, _this.__max)\n      );\n\n      return false;\n    }\n\n    function onMouseUp() {\n      dom.unbind(window, 'mousemove', onMouseDrag);\n      dom.unbind(window, 'mouseup', onMouseUp);\n      if (_this.__onFinishChange) {\n        _this.__onFinishChange.call(_this, _this.getValue());\n      }\n    }\n\n    function onTouchStart(e) {\n      if (e.touches.length !== 1) { return; }\n      dom.bind(window, 'touchmove', onTouchMove);\n      dom.bind(window, 'touchend', onTouchEnd);\n      onTouchMove(e);\n    }\n\n    function onTouchMove(e) {\n      const clientX = e.touches[0].clientX;\n      const bgRect = _this.__background.getBoundingClientRect();\n\n      _this.setValue(\n        map(clientX, bgRect.left, bgRect.right, _this.__min, _this.__max)\n      );\n    }\n\n    function onTouchEnd() {\n      dom.unbind(window, 'touchmove', onTouchMove);\n      dom.unbind(window, 'touchend', onTouchEnd);\n      if (_this.__onFinishChange) {\n        _this.__onFinishChange.call(_this, _this.getValue());\n      }\n    }\n\n    this.updateDisplay();\n\n    this.__background.appendChild(this.__foreground);\n    this.domElement.appendChild(this.__background);\n  }\n\n  updateDisplay() {\n    const pct = (this.getValue() - this.__min) / (this.__max - this.__min);\n    this.__foreground.style.width = pct * 100 + '%';\n    return super.updateDisplay();\n  }\n}\n\nexport default NumberControllerSlider;\n", "/**\n * dat-gui JavaScript Controller Library\n * https://github.com/dataarts/dat.gui\n *\n * Copyright 2011 Data Arts Team, Google Creative Lab\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n */\n\nimport Controller from './Controller';\nimport dom from '../dom/dom';\n\n/**\n * @class Provides a GUI interface to fire a specified method, a property of an object.\n *\n * @extends dat.controllers.Controller\n *\n * @param {Object} object The object to be manipulated\n * @param {string} property The name of the property to be manipulated\n */\nclass FunctionController extends Controller {\n  constructor(object, property, text) {\n    super(object, property);\n\n    const _this = this;\n\n    this.__button = document.createElement('div');\n    this.__button.innerHTML = text === undefined ? 'Fire' : text;\n\n    dom.bind(this.__button, 'click', function(e) {\n      e.preventDefault();\n      _this.fire();\n      return false;\n    });\n\n    dom.addClass(this.__button, 'button');\n\n    this.domElement.appendChild(this.__button);\n  }\n\n  fire() {\n    if (this.__onChange) {\n      this.__onChange.call(this);\n    }\n    this.getValue().call(this.object);\n    if (this.__onFinishChange) {\n      this.__onFinishChange.call(this, this.getValue());\n    }\n  }\n}\n\nexport default FunctionController;\n", "/**\n * dat-gui JavaScript Controller Library\n * https://github.com/dataarts/dat.gui\n *\n * Copyright 2011 Data Arts Team, Google Creative Lab\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n */\n\nimport Controller from './Controller';\nimport dom from '../dom/dom';\nimport Color from '../color/Color';\nimport interpret from '../color/interpret';\nimport common from '../utils/common';\n\n/**\n * @class Represents a given property of an object that is a color.\n * @param {Object} object\n * @param {string} property\n */\nclass ColorController extends Controller {\n  constructor(object, property) {\n    super(object, property);\n\n    this.__color = new Color(this.getValue());\n    this.__temp = new Color(0);\n\n    const _this = this;\n\n    this.domElement = document.createElement('div');\n\n    dom.makeSelectable(this.domElement, false);\n\n    this.__selector = document.createElement('div');\n    this.__selector.className = 'selector';\n\n    this.__saturation_field = document.createElement('div');\n    this.__saturation_field.className = 'saturation-field';\n\n    this.__field_knob = document.createElement('div');\n    this.__field_knob.className = 'field-knob';\n    this.__field_knob_border = '2px solid ';\n\n    this.__hue_knob = document.createElement('div');\n    this.__hue_knob.className = 'hue-knob';\n\n    this.__hue_field = document.createElement('div');\n    this.__hue_field.className = 'hue-field';\n\n    this.__input = document.createElement('input');\n    this.__input.type = 'text';\n    this.__input_textShadow = '0 1px 1px ';\n\n    dom.bind(this.__input, 'keydown', function(e) {\n      if (e.keyCode === 13) { // on enter\n        onBlur.call(this);\n      }\n    });\n\n    dom.bind(this.__input, 'blur', onBlur);\n\n    dom.bind(this.__selector, 'mousedown', function(/* e */) {\n      dom\n        .addClass(this, 'drag')\n        .bind(window, 'mouseup', function(/* e */) {\n          dom.removeClass(_this.__selector, 'drag');\n        });\n    });\n\n    dom.bind(this.__selector, 'touchstart', function(/* e */) {\n      dom\n        .addClass(this, 'drag')\n        .bind(window, 'touchend', function(/* e */) {\n          dom.removeClass(_this.__selector, 'drag');\n        });\n    });\n\n    const valueField = document.createElement('div');\n\n    common.extend(this.__selector.style, {\n      width: '122px',\n      height: '102px',\n      padding: '3px',\n      backgroundColor: '#222',\n      boxShadow: '0px 1px 3px rgba(0,0,0,0.3)'\n    });\n\n    common.extend(this.__field_knob.style, {\n      position: 'absolute',\n      width: '12px',\n      height: '12px',\n      border: this.__field_knob_border + (this.__color.v < 0.5 ? '#fff' : '#000'),\n      boxShadow: '0px 1px 3px rgba(0,0,0,0.5)',\n      borderRadius: '12px',\n      zIndex: 1\n    });\n\n    common.extend(this.__hue_knob.style, {\n      position: 'absolute',\n      width: '15px',\n      height: '2px',\n      borderRight: '4px solid #fff',\n      zIndex: 1\n    });\n\n    common.extend(this.__saturation_field.style, {\n      width: '100px',\n      height: '100px',\n      border: '1px solid #555',\n      marginRight: '3px',\n      display: 'inline-block',\n      cursor: 'pointer'\n    });\n\n    common.extend(valueField.style, {\n      width: '100%',\n      height: '100%',\n      background: 'none'\n    });\n\n    linearGradient(valueField, 'top', 'rgba(0,0,0,0)', '#000');\n\n    common.extend(this.__hue_field.style, {\n      width: '15px',\n      height: '100px',\n      border: '1px solid #555',\n      cursor: 'ns-resize',\n      position: 'absolute',\n      top: '3px',\n      right: '3px'\n    });\n\n    hueGradient(this.__hue_field);\n\n    common.extend(this.__input.style, {\n      outline: 'none',\n      //      width: '120px',\n      textAlign: 'center',\n      //      padding: '4px',\n      //      marginBottom: '6px',\n      color: '#fff',\n      border: 0,\n      fontWeight: 'bold',\n      textShadow: this.__input_textShadow + 'rgba(0,0,0,0.7)'\n    });\n\n    dom.bind(this.__saturation_field, 'mousedown', fieldDown);\n    dom.bind(this.__saturation_field, 'touchstart', fieldDown);\n\n    dom.bind(this.__field_knob, 'mousedown', fieldDown);\n    dom.bind(this.__field_knob, 'touchstart', fieldDown);\n\n    dom.bind(this.__hue_field, 'mousedown', fieldDownH);\n    dom.bind(this.__hue_field, 'touchstart', fieldDownH);\n\n    function fieldDown(e) {\n      setSV(e);\n      dom.bind(window, 'mousemove', setSV);\n      dom.bind(window, 'touchmove', setSV);\n      dom.bind(window, 'mouseup', fieldUpSV);\n      dom.bind(window, 'touchend', fieldUpSV);\n    }\n\n    function fieldDownH(e) {\n      setH(e);\n      dom.bind(window, 'mousemove', setH);\n      dom.bind(window, 'touchmove', setH);\n      dom.bind(window, 'mouseup', fieldUpH);\n      dom.bind(window, 'touchend', fieldUpH);\n    }\n\n    function fieldUpSV() {\n      dom.unbind(window, 'mousemove', setSV);\n      dom.unbind(window, 'touchmove', setSV);\n      dom.unbind(window, 'mouseup', fieldUpSV);\n      dom.unbind(window, 'touchend', fieldUpSV);\n      onFinish();\n    }\n\n    function fieldUpH() {\n      dom.unbind(window, 'mousemove', setH);\n      dom.unbind(window, 'touchmove', setH);\n      dom.unbind(window, 'mouseup', fieldUpH);\n      dom.unbind(window, 'touchend', fieldUpH);\n      onFinish();\n    }\n\n    function onBlur() {\n      const i = interpret(this.value);\n      if (i !== false) {\n        _this.__color.__state = i;\n        _this.setValue(_this.__color.toOriginal());\n      } else {\n        this.value = _this.__color.toString();\n      }\n    }\n\n    function onFinish() {\n      if (_this.__onFinishChange) {\n        _this.__onFinishChange.call(_this, _this.__color.toOriginal());\n      }\n    }\n\n    this.__saturation_field.appendChild(valueField);\n    this.__selector.appendChild(this.__field_knob);\n    this.__selector.appendChild(this.__saturation_field);\n    this.__selector.appendChild(this.__hue_field);\n    this.__hue_field.appendChild(this.__hue_knob);\n\n    this.domElement.appendChild(this.__input);\n    this.domElement.appendChild(this.__selector);\n\n    this.updateDisplay();\n\n    function setSV(e) {\n      if (e.type.indexOf('touch') === -1) { e.preventDefault(); }\n\n      const fieldRect = _this.__saturation_field.getBoundingClientRect();\n      const { clientX, clientY } = (e.touches && e.touches[0]) || e;\n      let s = (clientX - fieldRect.left) / (fieldRect.right - fieldRect.left);\n      let v = 1 - (clientY - fieldRect.top) / (fieldRect.bottom - fieldRect.top);\n\n      if (v > 1) {\n        v = 1;\n      } else if (v < 0) {\n        v = 0;\n      }\n\n      if (s > 1) {\n        s = 1;\n      } else if (s < 0) {\n        s = 0;\n      }\n\n      _this.__color.v = v;\n      _this.__color.s = s;\n\n      _this.setValue(_this.__color.toOriginal());\n\n\n      return false;\n    }\n\n    function setH(e) {\n      if (e.type.indexOf('touch') === -1) { e.preventDefault(); }\n\n      const fieldRect = _this.__hue_field.getBoundingClientRect();\n      const { clientY } = (e.touches && e.touches[0]) || e;\n      let h = 1 - (clientY - fieldRect.top) / (fieldRect.bottom - fieldRect.top);\n\n      if (h > 1) {\n        h = 1;\n      } else if (h < 0) {\n        h = 0;\n      }\n\n      _this.__color.h = h * 360;\n\n      _this.setValue(_this.__color.toOriginal());\n\n      return false;\n    }\n  }\n\n  updateDisplay() {\n    const i = interpret(this.getValue());\n\n    if (i !== false) {\n      let mismatch = false;\n\n      // Check for mismatch on the interpreted value.\n\n      common.each(Color.COMPONENTS, function(component) {\n        if (!common.isUndefined(i[component]) && !common.isUndefined(this.__color.__state[component]) &&\n          i[component] !== this.__color.__state[component]) {\n          mismatch = true;\n          return {}; // break\n        }\n      }, this);\n\n      // If nothing diverges, we keep our previous values\n      // for statefulness, otherwise we recalculate fresh\n      if (mismatch) {\n        common.extend(this.__color.__state, i);\n      }\n    }\n\n    common.extend(this.__temp.__state, this.__color.__state);\n\n    this.__temp.a = 1;\n\n    const flip = (this.__color.v < 0.5 || this.__color.s > 0.5) ? 255 : 0;\n    const _flip = 255 - flip;\n\n    common.extend(this.__field_knob.style, {\n      marginLeft: 100 * this.__color.s - 7 + 'px',\n      marginTop: 100 * (1 - this.__color.v) - 7 + 'px',\n      backgroundColor: this.__temp.toHexString(),\n      border: this.__field_knob_border + 'rgb(' + flip + ',' + flip + ',' + flip + ')'\n    });\n\n    this.__hue_knob.style.marginTop = (1 - this.__color.h / 360) * 100 + 'px';\n\n    this.__temp.s = 1;\n    this.__temp.v = 1;\n\n    linearGradient(this.__saturation_field, 'left', '#fff', this.__temp.toHexString());\n\n    this.__input.value = this.__color.toString();\n\n    common.extend(this.__input.style, {\n      backgroundColor: this.__color.toHexString(),\n      color: 'rgb(' + flip + ',' + flip + ',' + flip + ')',\n      textShadow: this.__input_textShadow + 'rgba(' + _flip + ',' + _flip + ',' + _flip + ',.7)'\n    });\n  }\n}\n\nconst vendors = ['-moz-', '-o-', '-webkit-', '-ms-', ''];\n\nfunction linearGradient(elem, x, a, b) {\n  elem.style.background = '';\n  common.each(vendors, function(vendor) {\n    elem.style.cssText += 'background: ' + vendor + 'linear-gradient(' + x + ', ' + a + ' 0%, ' + b + ' 100%); ';\n  });\n}\n\nfunction hueGradient(elem) {\n  elem.style.background = '';\n  elem.style.cssText += 'background: -moz-linear-gradient(top,  #ff0000 0%, #ff00ff 17%, #0000ff 34%, #00ffff 50%, #00ff00 67%, #ffff00 84%, #ff0000 100%);';\n  elem.style.cssText += 'background: -webkit-linear-gradient(top,  #ff0000 0%,#ff00ff 17%,#0000ff 34%,#00ffff 50%,#00ff00 67%,#ffff00 84%,#ff0000 100%);';\n  elem.style.cssText += 'background: -o-linear-gradient(top,  #ff0000 0%,#ff00ff 17%,#0000ff 34%,#00ffff 50%,#00ff00 67%,#ffff00 84%,#ff0000 100%);';\n  elem.style.cssText += 'background: -ms-linear-gradient(top,  #ff0000 0%,#ff00ff 17%,#0000ff 34%,#00ffff 50%,#00ff00 67%,#ffff00 84%,#ff0000 100%);';\n  elem.style.cssText += 'background: linear-gradient(top,  #ff0000 0%,#ff00ff 17%,#0000ff 34%,#00ffff 50%,#00ff00 67%,#ffff00 84%,#ff0000 100%);';\n}\n\nexport default ColorController;\n", "/**\n * dat-gui JavaScript Controller Library\n * https://github.com/dataarts/dat.gui\n *\n * Copyright 2011 Data Arts Team, Google Creative Lab\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n */\n\nconst css = {\n  load: function(url, indoc) {\n    const doc = indoc || document;\n    const link = doc.createElement('link');\n    link.type = 'text/css';\n    link.rel = 'stylesheet';\n    link.href = url;\n    doc.getElementsByTagName('head')[0].appendChild(link);\n  },\n\n  inject: function(cssContent, indoc) {\n    const doc = indoc || document;\n    const injected = document.createElement('style');\n    injected.type = 'text/css';\n    injected.innerHTML = cssContent;\n    const head = doc.getElementsByTagName('head')[0];\n    try {\n      head.appendChild(injected);\n    } catch (e) { // Unable to inject CSS, probably because of a Content Security Policy\n    }\n  }\n};\n\nexport default css;\n", "const saveDialogContents = `<div id=\"dg-save\" class=\"dg dialogue\">\n\n  Here's the new load parameter for your <code>GUI</code>'s constructor:\n\n  <textarea id=\"dg-new-constructor\"></textarea>\n\n  <div id=\"dg-save-locally\">\n\n    <input id=\"dg-local-storage\" type=\"checkbox\"/> Automatically save\n    values to <code>localStorage</code> on exit.\n\n    <div id=\"dg-local-explain\">The values saved to <code>localStorage</code> will\n      override those passed to <code>dat.GUI</code>'s constructor. This makes it\n      easier to work incrementally, but <code>localStorage</code> is fragile,\n      and your friends may not see the same values you do.\n\n    </div>\n\n  </div>\n\n</div>`;\n\nexport default saveDialogContents;\n", "/**\n * dat-gui JavaScript Controller Library\n * https://github.com/dataarts/dat.gui\n *\n * Copyright 2011 Data Arts Team, Google Creative Lab\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n */\n\nimport OptionController from './OptionController';\nimport NumberControllerBox from './NumberControllerBox';\nimport NumberControllerSlider from './NumberControllerSlider';\nimport StringController from './StringController';\nimport FunctionController from './FunctionController';\nimport BooleanController from './BooleanController';\nimport common from '../utils/common';\n\nconst ControllerFactory = function(object, property) {\n  const initialValue = object[property];\n\n  // Providing options?\n  if (common.isArray(arguments[2]) || common.isObject(arguments[2])) {\n    return new OptionController(object, property, arguments[2]);\n  }\n\n  // Providing a map?\n  if (common.isNumber(initialValue)) {\n    // Has min and max? (slider)\n    if (common.isNumber(arguments[2]) && common.isNumber(arguments[3])) {\n      // has step?\n      if (common.isNumber(arguments[4])) {\n        return new NumberControllerSlider(object, property,\n          arguments[2], arguments[3], arguments[4]);\n      }\n\n      return new NumberControllerSlider(object, property, arguments[2], arguments[3]);\n    }\n\n    // number box\n    if (common.isNumber(arguments[4])) { // has step\n      return new NumberControllerBox(object, property,\n        { min: arguments[2], max: arguments[3], step: arguments[4] });\n    }\n    return new NumberControllerBox(object, property, { min: arguments[2], max: arguments[3] });\n  }\n\n  if (common.isString(initialValue)) {\n    return new StringController(object, property);\n  }\n\n  if (common.isFunction(initialValue)) {\n    return new FunctionController(object, property, '');\n  }\n\n  if (common.isBoolean(initialValue)) {\n    return new BooleanController(object, property);\n  }\n\n  return null;\n};\n\nexport default ControllerFactory;\n", "/**\n * dat-gui JavaScript Controller Library\n * https://github.com/dataarts/dat.gui\n *\n * Copyright 2011 Data Arts Team, Google Creative Lab\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n */\n\nfunction requestAnimationFrame(callback) {\n  setTimeout(callback, 1000 / 60);\n}\n\nexport default window.requestAnimationFrame ||\n    window.webkitRequestAnimationFrame ||\n    window.mozRequestAnimationFrame ||\n    window.oRequestAnimationFrame ||\n    window.msRequestAnimationFrame ||\n    requestAnimationFrame;\n", "/**\n * dat-gui JavaScript Controller Library\n * https://github.com/dataarts/dat.gui\n *\n * Copyright 2011 Data Arts Team, Google Creative Lab\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n */\n\nimport dom from './dom';\nimport common from '../utils/common';\n\nclass CenteredDiv {\n  constructor() {\n    this.backgroundElement = document.createElement('div');\n    common.extend(this.backgroundElement.style, {\n      backgroundColor: 'rgba(0,0,0,0.8)',\n      top: 0,\n      left: 0,\n      display: 'none',\n      zIndex: '1000',\n      opacity: 0,\n      WebkitTransition: 'opacity 0.2s linear',\n      transition: 'opacity 0.2s linear'\n    });\n\n    dom.makeFullscreen(this.backgroundElement);\n    this.backgroundElement.style.position = 'fixed';\n\n    this.domElement = document.createElement('div');\n    common.extend(this.domElement.style, {\n      position: 'fixed',\n      display: 'none',\n      zIndex: '1001',\n      opacity: 0,\n      WebkitTransition: '-webkit-transform 0.2s ease-out, opacity 0.2s linear',\n      transition: 'transform 0.2s ease-out, opacity 0.2s linear'\n    });\n\n\n    document.body.appendChild(this.backgroundElement);\n    document.body.appendChild(this.domElement);\n\n    const _this = this;\n    dom.bind(this.backgroundElement, 'click', function() {\n      _this.hide();\n    });\n  }\n\n  show() {\n    const _this = this;\n\n    this.backgroundElement.style.display = 'block';\n\n    this.domElement.style.display = 'block';\n    this.domElement.style.opacity = 0;\n    //    this.domElement.style.top = '52%';\n    this.domElement.style.webkitTransform = 'scale(1.1)';\n\n    this.layout();\n\n    common.defer(function() {\n      _this.backgroundElement.style.opacity = 1;\n      _this.domElement.style.opacity = 1;\n      _this.domElement.style.webkitTransform = 'scale(1)';\n    });\n  }\n\n  /**\n   * Hide centered div\n   */\n  hide() {\n    const _this = this;\n\n    const hide = function() {\n      _this.domElement.style.display = 'none';\n      _this.backgroundElement.style.display = 'none';\n\n      dom.unbind(_this.domElement, 'webkitTransitionEnd', hide);\n      dom.unbind(_this.domElement, 'transitionend', hide);\n      dom.unbind(_this.domElement, 'oTransitionEnd', hide);\n    };\n\n    dom.bind(this.domElement, 'webkitTransitionEnd', hide);\n    dom.bind(this.domElement, 'transitionend', hide);\n    dom.bind(this.domElement, 'oTransitionEnd', hide);\n\n    this.backgroundElement.style.opacity = 0;\n    //    this.domElement.style.top = '48%';\n    this.domElement.style.opacity = 0;\n    this.domElement.style.webkitTransform = 'scale(1.1)';\n  }\n\n  layout() {\n    this.domElement.style.left = window.innerWidth / 2 - dom.getWidth(this.domElement) / 2 + 'px';\n    this.domElement.style.top = window.innerHeight / 2 - dom.getHeight(this.domElement) / 2 + 'px';\n  }\n}\n\nexport default CenteredDiv;\n", "/**\n * dat-gui JavaScript Controller Library\n * https://github.com/dataarts/dat.gui\n *\n * Copyright 2011 Data Arts Team, Google Creative Lab\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n */\n\nimport css from '../utils/css';\nimport saveDialogueContents from './saveDialogue.html';\nimport ControllerFactory from '../controllers/ControllerFactory';\nimport Controller from '../controllers/Controller';\nimport BooleanController from '../controllers/BooleanController';\nimport FunctionController from '../controllers/FunctionController';\nimport Number<PERSON>ontrollerBox from '../controllers/NumberControllerBox';\nimport NumberControllerSlider from '../controllers/NumberControllerSlider';\nimport ColorController from '../controllers/ColorController';\nimport requestAnimationFrame from '../utils/requestAnimationFrame';\nimport CenteredDiv from '../dom/CenteredDiv';\nimport dom from '../dom/dom';\nimport common from '../utils/common';\n\nimport styleSheet from './style.scss'; // CSS to embed in build\n\ncss.inject(styleSheet);\n\n/** @ignore Outer-most className for GUI's */\nconst CSS_NAMESPACE = 'dg';\n\nconst HIDE_KEY_CODE = 72;\n\n/** @ignore The only value shared between the JS and SCSS. Use caution. */\nconst CLOSE_BUTTON_HEIGHT = 20;\n\nconst DEFAULT_DEFAULT_PRESET_NAME = 'Default';\n\nconst SUPPORTS_LOCAL_STORAGE = (function() {\n  try {\n    return !!window.localStorage;\n  } catch (e) {\n    return false;\n  }\n}());\n\nlet SAVE_DIALOGUE;\n\n/** @ignore Have we yet to create an autoPlace GUI? */\nlet autoPlaceVirgin = true;\n\n/** @ignore Fixed position div that auto place GUI's go inside */\nlet autoPlaceContainer;\n\n/** @ignore Are we hiding the GUI's ? */\nlet hide = false;\n\n/** @private GUI's which should be hidden */\nconst hideableGuis = [];\n\n/**\n * @class A lightweight controller library for JavaScript. It allows you to easily\n * manipulate variables and fire functions on the fly.\n *\n * @typicalname gui\n *\n * @example\n * // Creating a GUI with options.\n * var gui = new dat.GUI({name: 'My GUI'});\n *\n * @example\n * // Creating a GUI and a subfolder.\n * var gui = new dat.GUI();\n * var folder1 = gui.addFolder('Flow Field');\n *\n * @param {Object} [params]\n * @param {String} [params.name] The name of this GUI.\n * @param {Object} [params.load] JSON object representing the saved state of\n * this GUI.\n * @param {dat.gui.GUI} [params.parent] The GUI I'm nested in.\n * @param {Boolean} [params.autoPlace=true]\n * @param {Boolean} [params.hideable=true] If true, GUI is shown/hidden by <kbd>h</kbd> keypress.\n * @param {Boolean} [params.closed=false] If true, starts closed\n * @param {Boolean} [params.closeOnTop=false] If true, close/open button shows on top of the GUI\n */\nconst GUI = function(pars) {\n  const _this = this;\n\n  let params = pars || {};\n\n  /**\n   * Outermost DOM Element\n   * @type {DOMElement}\n   */\n  this.domElement = document.createElement('div');\n  this.__ul = document.createElement('ul');\n  this.domElement.appendChild(this.__ul);\n\n  dom.addClass(this.domElement, CSS_NAMESPACE);\n\n  /**\n   * Nested GUI's by name\n   * @ignore\n   */\n  this.__folders = {};\n\n  this.__controllers = [];\n\n  /**\n   * List of objects I'm remembering for save, only used in top level GUI\n   * @ignore\n   */\n  this.__rememberedObjects = [];\n\n  /**\n   * Maps the index of remembered objects to a map of controllers, only used\n   * in top level GUI.\n   *\n   * @private\n   * @ignore\n   *\n   * @example\n   * [\n   *  {\n     *    propertyName: Controller,\n     *    anotherPropertyName: Controller\n     *  },\n   *  {\n     *    propertyName: Controller\n     *  }\n   * ]\n   */\n  this.__rememberedObjectIndecesToControllers = [];\n\n  this.__listening = [];\n\n  // Default parameters\n  params = common.defaults(params, {\n    closeOnTop: false,\n    autoPlace: true,\n    width: GUI.DEFAULT_WIDTH\n  });\n\n  params = common.defaults(params, {\n    resizable: params.autoPlace,\n    hideable: params.autoPlace\n  });\n\n  if (!common.isUndefined(params.load)) {\n    // Explicit preset\n    if (params.preset) {\n      params.load.preset = params.preset;\n    }\n  } else {\n    params.load = { preset: DEFAULT_DEFAULT_PRESET_NAME };\n  }\n\n  if (common.isUndefined(params.parent) && params.hideable) {\n    hideableGuis.push(this);\n  }\n\n  // Only root level GUI's are resizable.\n  params.resizable = common.isUndefined(params.parent) && params.resizable;\n\n  if (params.autoPlace && common.isUndefined(params.scrollable)) {\n    params.scrollable = true;\n  }\n  //    params.scrollable = common.isUndefined(params.parent) && params.scrollable === true;\n\n  // Not part of params because I don't want people passing this in via\n  // constructor. Should be a 'remembered' value.\n  let useLocalStorage =\n    SUPPORTS_LOCAL_STORAGE &&\n    localStorage.getItem(getLocalStorageHash(this, 'isLocal')) === 'true';\n\n  let saveToLocalStorage;\n  let titleRow;\n\n  Object.defineProperties(this,\n    /** @lends GUI.prototype */\n    {\n      /**\n       * The parent <code>GUI</code>\n       * @type dat.gui.GUI\n       */\n      parent: {\n        get: function() {\n          return params.parent;\n        }\n      },\n\n      scrollable: {\n        get: function() {\n          return params.scrollable;\n        }\n      },\n\n      /**\n       * Handles <code>GUI</code>'s element placement for you\n       * @type Boolean\n       */\n      autoPlace: {\n        get: function() {\n          return params.autoPlace;\n        }\n      },\n\n      /**\n       * Handles <code>GUI</code>'s position of open/close button\n       * @type Boolean\n       */\n      closeOnTop: {\n        get: function() {\n          return params.closeOnTop;\n        }\n      },\n\n      /**\n       * The identifier for a set of saved values\n       * @type String\n       */\n      preset: {\n        get: function() {\n          if (_this.parent) {\n            return _this.getRoot().preset;\n          }\n\n          return params.load.preset;\n        },\n\n        set: function(v) {\n          if (_this.parent) {\n            _this.getRoot().preset = v;\n          } else {\n            params.load.preset = v;\n          }\n          setPresetSelectIndex(this);\n          _this.revert();\n        }\n      },\n\n      /**\n       * The width of <code>GUI</code> element\n       * @type Number\n       */\n      width: {\n        get: function() {\n          return params.width;\n        },\n        set: function(v) {\n          params.width = v;\n          setWidth(_this, v);\n        }\n      },\n\n      /**\n       * The name of <code>GUI</code>. Used for folders. i.e\n       * a folder's name\n       * @type String\n       */\n      name: {\n        get: function() {\n          return params.name;\n        },\n        set: function(v) {\n          // TODO Check for collisions among sibling folders\n          params.name = v;\n          if (titleRow) {\n            titleRow.innerHTML = params.name;\n          }\n        }\n      },\n\n      /**\n       * Whether the <code>GUI</code> is collapsed or not\n       * @type Boolean\n       */\n      closed: {\n        get: function() {\n          return params.closed;\n        },\n        set: function(v) {\n          params.closed = v;\n          if (params.closed) {\n            dom.addClass(_this.__ul, GUI.CLASS_CLOSED);\n          } else {\n            dom.removeClass(_this.__ul, GUI.CLASS_CLOSED);\n          }\n          // For browsers that aren't going to respect the CSS transition,\n          // Lets just check our height against the window height right off\n          // the bat.\n          this.onResize();\n\n          if (_this.__closeButton) {\n            _this.__closeButton.innerHTML = v ? GUI.TEXT_OPEN : GUI.TEXT_CLOSED;\n          }\n        }\n      },\n\n      /**\n       * Contains all presets\n       * @type Object\n       */\n      load: {\n        get: function() {\n          return params.load;\n        }\n      },\n\n      /**\n       * Determines whether or not to use <a href=\"https://developer.mozilla.org/en/DOM/Storage#localStorage\">localStorage</a> as the means for\n       * <code>remember</code>ing\n       * @type Boolean\n       */\n      useLocalStorage: {\n\n        get: function() {\n          return useLocalStorage;\n        },\n        set: function(bool) {\n          if (SUPPORTS_LOCAL_STORAGE) {\n            useLocalStorage = bool;\n            if (bool) {\n              dom.bind(window, 'unload', saveToLocalStorage);\n            } else {\n              dom.unbind(window, 'unload', saveToLocalStorage);\n            }\n            localStorage.setItem(getLocalStorageHash(_this, 'isLocal'), bool);\n          }\n        }\n      }\n    });\n\n  // Are we a root level GUI?\n  if (common.isUndefined(params.parent)) {\n    this.closed = params.closed || false;\n\n    dom.addClass(this.domElement, GUI.CLASS_MAIN);\n    dom.makeSelectable(this.domElement, false);\n\n    // Are we supposed to be loading locally?\n    if (SUPPORTS_LOCAL_STORAGE) {\n      if (useLocalStorage) {\n        _this.useLocalStorage = true;\n\n        const savedGui = localStorage.getItem(getLocalStorageHash(this, 'gui'));\n\n        if (savedGui) {\n          params.load = JSON.parse(savedGui);\n        }\n      }\n    }\n\n    this.__closeButton = document.createElement('div');\n    this.__closeButton.innerHTML = GUI.TEXT_CLOSED;\n    dom.addClass(this.__closeButton, GUI.CLASS_CLOSE_BUTTON);\n    if (params.closeOnTop) {\n      dom.addClass(this.__closeButton, GUI.CLASS_CLOSE_TOP);\n      this.domElement.insertBefore(this.__closeButton, this.domElement.childNodes[0]);\n    } else {\n      dom.addClass(this.__closeButton, GUI.CLASS_CLOSE_BOTTOM);\n      this.domElement.appendChild(this.__closeButton);\n    }\n\n    dom.bind(this.__closeButton, 'click', function() {\n      _this.closed = !_this.closed;\n    });\n    // Oh, you're a nested GUI!\n  } else {\n    if (params.closed === undefined) {\n      params.closed = true;\n    }\n\n    const titleRowName = document.createTextNode(params.name);\n    dom.addClass(titleRowName, 'controller-name');\n\n    titleRow = addRow(_this, titleRowName);\n\n    const onClickTitle = function(e) {\n      e.preventDefault();\n      _this.closed = !_this.closed;\n      return false;\n    };\n\n    dom.addClass(this.__ul, GUI.CLASS_CLOSED);\n\n    dom.addClass(titleRow, 'title');\n    dom.bind(titleRow, 'click', onClickTitle);\n\n    if (!params.closed) {\n      this.closed = false;\n    }\n  }\n\n  if (params.autoPlace) {\n    if (common.isUndefined(params.parent)) {\n      if (autoPlaceVirgin) {\n        autoPlaceContainer = document.createElement('div');\n        dom.addClass(autoPlaceContainer, CSS_NAMESPACE);\n        dom.addClass(autoPlaceContainer, GUI.CLASS_AUTO_PLACE_CONTAINER);\n        document.body.appendChild(autoPlaceContainer);\n        autoPlaceVirgin = false;\n      }\n\n      // Put it in the dom for you.\n      autoPlaceContainer.appendChild(this.domElement);\n\n      // Apply the auto styles\n      dom.addClass(this.domElement, GUI.CLASS_AUTO_PLACE);\n    }\n\n\n    // Make it not elastic.\n    if (!this.parent) {\n      setWidth(_this, params.width);\n    }\n  }\n\n  this.__resizeHandler = function() {\n    _this.onResizeDebounced();\n  };\n\n  dom.bind(window, 'resize', this.__resizeHandler);\n  dom.bind(this.__ul, 'webkitTransitionEnd', this.__resizeHandler);\n  dom.bind(this.__ul, 'transitionend', this.__resizeHandler);\n  dom.bind(this.__ul, 'oTransitionEnd', this.__resizeHandler);\n  this.onResize();\n\n  if (params.resizable) {\n    addResizeHandle(this);\n  }\n\n  saveToLocalStorage = function() {\n    if (SUPPORTS_LOCAL_STORAGE && localStorage.getItem(getLocalStorageHash(_this, 'isLocal')) === 'true') {\n      localStorage.setItem(getLocalStorageHash(_this, 'gui'), JSON.stringify(_this.getSaveObject()));\n    }\n  };\n\n  // expose this method publicly\n  this.saveToLocalStorageIfPossible = saveToLocalStorage;\n\n  function resetWidth() {\n    const root = _this.getRoot();\n    root.width += 1;\n    common.defer(function() {\n      root.width -= 1;\n    });\n  }\n\n  if (!params.parent) {\n    resetWidth();\n  }\n};\n\nGUI.toggleHide = function() {\n  hide = !hide;\n  common.each(hideableGuis, function(gui) {\n    gui.domElement.style.display = hide ? 'none' : '';\n  });\n};\n\nGUI.CLASS_AUTO_PLACE = 'a';\nGUI.CLASS_AUTO_PLACE_CONTAINER = 'ac';\nGUI.CLASS_MAIN = 'main';\nGUI.CLASS_CONTROLLER_ROW = 'cr';\nGUI.CLASS_TOO_TALL = 'taller-than-window';\nGUI.CLASS_CLOSED = 'closed';\nGUI.CLASS_CLOSE_BUTTON = 'close-button';\nGUI.CLASS_CLOSE_TOP = 'close-top';\nGUI.CLASS_CLOSE_BOTTOM = 'close-bottom';\nGUI.CLASS_DRAG = 'drag';\n\nGUI.DEFAULT_WIDTH = 245;\nGUI.TEXT_CLOSED = 'Close Controls';\nGUI.TEXT_OPEN = 'Open Controls';\n\nGUI._keydownHandler = function(e) {\n  if (document.activeElement.type !== 'text' &&\n    (e.which === HIDE_KEY_CODE || e.keyCode === HIDE_KEY_CODE)) {\n    GUI.toggleHide();\n  }\n};\ndom.bind(window, 'keydown', GUI._keydownHandler, false);\n\ncommon.extend(\n  GUI.prototype,\n\n  /** @lends GUI.prototype */\n  {\n\n    /**\n     * Adds a new {@link Controller} to the GUI. The type of controller created\n     * is inferred from the initial value of <code>object[property]</code>. For\n     * color properties, see {@link addColor}.\n     *\n     * @param {Object} object The object to be manipulated\n     * @param {String} property The name of the property to be manipulated\n     * @param {Number} [min] Minimum allowed value\n     * @param {Number} [max] Maximum allowed value\n     * @param {Number} [step] Increment by which to change value\n     * @returns {Controller} The controller that was added to the GUI.\n     * @instance\n     *\n     * @example\n     * // Add a string controller.\n     * var person = {name: 'Sam'};\n     * gui.add(person, 'name');\n     *\n     * @example\n     * // Add a number controller slider.\n     * var person = {age: 45};\n     * gui.add(person, 'age', 0, 100);\n     */\n    add: function(object, property) {\n      return add(\n        this,\n        object,\n        property,\n        {\n          factoryArgs: Array.prototype.slice.call(arguments, 2)\n        }\n      );\n    },\n\n    /**\n     * Adds a new color controller to the GUI.\n     *\n     * @param object\n     * @param property\n     * @returns {Controller} The controller that was added to the GUI.\n     * @instance\n     *\n     * @example\n     * var palette = {\n     *   color1: '#FF0000', // CSS string\n     *   color2: [ 0, 128, 255 ], // RGB array\n     *   color3: [ 0, 128, 255, 0.3 ], // RGB with alpha\n     *   color4: { h: 350, s: 0.9, v: 0.3 } // Hue, saturation, value\n     * };\n     * gui.addColor(palette, 'color1');\n     * gui.addColor(palette, 'color2');\n     * gui.addColor(palette, 'color3');\n     * gui.addColor(palette, 'color4');\n     */\n    addColor: function(object, property) {\n      return add(\n        this,\n        object,\n        property,\n        {\n          color: true\n        }\n      );\n    },\n\n    /**\n     * Removes the given controller from the GUI.\n     * @param {Controller} controller\n     * @instance\n     */\n    remove: function(controller) {\n      // TODO listening?\n      this.__ul.removeChild(controller.__li);\n      this.__controllers.splice(this.__controllers.indexOf(controller), 1);\n      const _this = this;\n      common.defer(function() {\n        _this.onResize();\n      });\n    },\n\n    /**\n     * Removes the root GUI from the document and unbinds all event listeners.\n     * For subfolders, use `gui.removeFolder(folder)` instead.\n     * @instance\n     */\n    destroy: function() {\n      if (this.parent) {\n        throw new Error(\n          'Only the root GUI should be removed with .destroy(). ' +\n          'For subfolders, use gui.removeFolder(folder) instead.'\n        );\n      }\n\n      if (this.autoPlace) {\n        autoPlaceContainer.removeChild(this.domElement);\n      }\n\n      const _this = this;\n      common.each(this.__folders, function(subfolder) {\n        _this.removeFolder(subfolder);\n      });\n\n      dom.unbind(window, 'keydown', GUI._keydownHandler, false);\n\n      removeListeners(this);\n    },\n\n    /**\n     * Creates a new subfolder GUI instance.\n     * @param name\n     * @returns {dat.gui.GUI} The new folder.\n     * @throws {Error} if this GUI already has a folder by the specified\n     * name\n     * @instance\n     */\n    addFolder: function(name) {\n      // We have to prevent collisions on names in order to have a key\n      // by which to remember saved values\n      if (this.__folders[name] !== undefined) {\n        throw new Error('You already have a folder in this GUI by the' +\n          ' name \"' + name + '\"');\n      }\n\n      const newGuiParams = { name: name, parent: this };\n\n      // We need to pass down the autoPlace trait so that we can\n      // attach event listeners to open/close folder actions to\n      // ensure that a scrollbar appears if the window is too short.\n      newGuiParams.autoPlace = this.autoPlace;\n\n      // Do we have saved appearance data for this folder?\n      if (this.load && // Anything loaded?\n        this.load.folders && // Was my parent a dead-end?\n        this.load.folders[name]) { // Did daddy remember me?\n        // Start me closed if I was closed\n        newGuiParams.closed = this.load.folders[name].closed;\n\n        // Pass down the loaded data\n        newGuiParams.load = this.load.folders[name];\n      }\n\n      const gui = new GUI(newGuiParams);\n      this.__folders[name] = gui;\n\n      const li = addRow(this, gui.domElement);\n      dom.addClass(li, 'folder');\n      return gui;\n    },\n\n    /**\n     * Removes a subfolder GUI instance.\n     * @param {dat.gui.GUI} folder The folder to remove.\n     * @instance\n     */\n    removeFolder: function(folder) {\n      this.__ul.removeChild(folder.domElement.parentElement);\n\n      delete this.__folders[folder.name];\n\n      // Do we have saved appearance data for this folder?\n      if (this.load && // Anything loaded?\n        this.load.folders && // Was my parent a dead-end?\n        this.load.folders[folder.name]) {\n        delete this.load.folders[folder.name];\n      }\n\n      removeListeners(folder);\n\n      const _this = this;\n\n      common.each(folder.__folders, function(subfolder) {\n        folder.removeFolder(subfolder);\n      });\n\n      common.defer(function() {\n        _this.onResize();\n      });\n    },\n\n    /**\n     * Opens the GUI.\n     */\n    open: function() {\n      this.closed = false;\n    },\n\n    /**\n     * Closes the GUI.\n     */\n    close: function() {\n      this.closed = true;\n    },\n\n    /**\n    * Hides the GUI.\n    */\n    hide: function() {\n      this.domElement.style.display = 'none';\n    },\n\n    /**\n    * Shows the GUI.\n    */\n    show: function() {\n      this.domElement.style.display = '';\n    },\n\n\n    onResize: function() {\n      // we debounce this function to prevent performance issues when rotating on tablet/mobile\n      const root = this.getRoot();\n      if (root.scrollable) {\n        const top = dom.getOffset(root.__ul).top;\n        let h = 0;\n\n        common.each(root.__ul.childNodes, function(node) {\n          if (!(root.autoPlace && node === root.__save_row)) {\n            h += dom.getHeight(node);\n          }\n        });\n\n        if (window.innerHeight - top - CLOSE_BUTTON_HEIGHT < h) {\n          dom.addClass(root.domElement, GUI.CLASS_TOO_TALL);\n          root.__ul.style.height = window.innerHeight - top - CLOSE_BUTTON_HEIGHT + 'px';\n        } else {\n          dom.removeClass(root.domElement, GUI.CLASS_TOO_TALL);\n          root.__ul.style.height = 'auto';\n        }\n      }\n\n      if (root.__resize_handle) {\n        common.defer(function() {\n          root.__resize_handle.style.height = root.__ul.offsetHeight + 'px';\n        });\n      }\n\n      if (root.__closeButton) {\n        root.__closeButton.style.width = root.width + 'px';\n      }\n    },\n\n    onResizeDebounced: common.debounce(function() { this.onResize(); }, 50),\n\n    /**\n     * Mark objects for saving. The order of these objects cannot change as\n     * the GUI grows. When remembering new objects, append them to the end\n     * of the list.\n     *\n     * @param {...Object} objects\n     * @throws {Error} if not called on a top level GUI.\n     * @instance\n     * @ignore\n     */\n    remember: function() {\n      if (common.isUndefined(SAVE_DIALOGUE)) {\n        SAVE_DIALOGUE = new CenteredDiv();\n        SAVE_DIALOGUE.domElement.innerHTML = saveDialogueContents;\n      }\n\n      if (this.parent) {\n        throw new Error('You can only call remember on a top level GUI.');\n      }\n\n      const _this = this;\n\n      common.each(Array.prototype.slice.call(arguments), function(object) {\n        if (_this.__rememberedObjects.length === 0) {\n          addSaveMenu(_this);\n        }\n        if (_this.__rememberedObjects.indexOf(object) === -1) {\n          _this.__rememberedObjects.push(object);\n        }\n      });\n\n      if (this.autoPlace) {\n        // Set save row width\n        setWidth(this, this.width);\n      }\n    },\n\n    /**\n     * @returns {dat.gui.GUI} the topmost parent GUI of a nested GUI.\n     * @instance\n     */\n    getRoot: function() {\n      let gui = this;\n      while (gui.parent) {\n        gui = gui.parent;\n      }\n      return gui;\n    },\n\n    /**\n     * @returns {Object} a JSON object representing the current state of\n     * this GUI as well as its remembered properties.\n     * @instance\n     */\n    getSaveObject: function() {\n      const toReturn = this.load;\n      toReturn.closed = this.closed;\n\n      // Am I remembering any values?\n      if (this.__rememberedObjects.length > 0) {\n        toReturn.preset = this.preset;\n\n        if (!toReturn.remembered) {\n          toReturn.remembered = {};\n        }\n\n        toReturn.remembered[this.preset] = getCurrentPreset(this);\n      }\n\n      toReturn.folders = {};\n      common.each(this.__folders, function(element, key) {\n        toReturn.folders[key] = element.getSaveObject();\n      });\n\n      return toReturn;\n    },\n\n    save: function() {\n      if (!this.load.remembered) {\n        this.load.remembered = {};\n      }\n\n      this.load.remembered[this.preset] = getCurrentPreset(this);\n      markPresetModified(this, false);\n      this.saveToLocalStorageIfPossible();\n    },\n\n    saveAs: function(presetName) {\n      if (!this.load.remembered) {\n        // Retain default values upon first save\n        this.load.remembered = {};\n        this.load.remembered[DEFAULT_DEFAULT_PRESET_NAME] = getCurrentPreset(this, true);\n      }\n\n      this.load.remembered[presetName] = getCurrentPreset(this);\n      this.preset = presetName;\n      addPresetOption(this, presetName, true);\n      this.saveToLocalStorageIfPossible();\n    },\n\n    revert: function(gui) {\n      common.each(this.__controllers, function(controller) {\n        // Make revert work on Default.\n        if (!this.getRoot().load.remembered) {\n          controller.setValue(controller.initialValue);\n        } else {\n          recallSavedValue(gui || this.getRoot(), controller);\n        }\n\n        // fire onFinishChange callback\n        if (controller.__onFinishChange) {\n          controller.__onFinishChange.call(controller, controller.getValue());\n        }\n      }, this);\n\n      common.each(this.__folders, function(folder) {\n        folder.revert(folder);\n      });\n\n      if (!gui) {\n        markPresetModified(this.getRoot(), false);\n      }\n    },\n\n    listen: function(controller) {\n      const init = this.__listening.length === 0;\n      this.__listening.push(controller);\n      if (init) {\n        updateDisplays(this.__listening);\n      }\n    },\n\n    updateDisplay: function() {\n      common.each(this.__controllers, function(controller) {\n        controller.updateDisplay();\n      });\n      common.each(this.__folders, function(folder) {\n        folder.updateDisplay();\n      });\n    }\n  }\n);\n\n/**\n * Add a row to the end of the GUI or before another row.\n *\n * @param gui\n * @param [newDom] If specified, inserts the dom content in the new row\n * @param [liBefore] If specified, places the new row before another row\n *\n * @ignore\n */\nfunction addRow(gui, newDom, liBefore) {\n  const li = document.createElement('li');\n  if (newDom) {\n    li.appendChild(newDom);\n  }\n\n  if (liBefore) {\n    gui.__ul.insertBefore(li, liBefore);\n  } else {\n    gui.__ul.appendChild(li);\n  }\n  gui.onResize();\n  return li;\n}\n\nfunction removeListeners(gui) {\n  dom.unbind(window, 'resize', gui.__resizeHandler);\n\n  if (gui.saveToLocalStorageIfPossible) {\n    dom.unbind(window, 'unload', gui.saveToLocalStorageIfPossible);\n  }\n}\n\nfunction markPresetModified(gui, modified) {\n  const opt = gui.__preset_select[gui.__preset_select.selectedIndex];\n\n  if (modified) {\n    opt.innerHTML = opt.value + '*';\n  } else {\n    opt.innerHTML = opt.value;\n  }\n}\n\nfunction augmentController(gui, li, controller) {\n  controller.__li = li;\n  controller.__gui = gui;\n\n  common.extend(controller, /** @lends Controller.prototype */ {\n    /**\n     * @param  {Array|Object} options\n     * @return {Controller}\n     */\n    options: function(options) {\n      if (arguments.length > 1) {\n        const nextSibling = controller.__li.nextElementSibling;\n        controller.remove();\n\n        return add(\n          gui,\n          controller.object,\n          controller.property,\n          {\n            before: nextSibling,\n            factoryArgs: [common.toArray(arguments)]\n          }\n        );\n      }\n\n      if (common.isArray(options) || common.isObject(options)) {\n        const nextSibling = controller.__li.nextElementSibling;\n        controller.remove();\n\n        return add(\n          gui,\n          controller.object,\n          controller.property,\n          {\n            before: nextSibling,\n            factoryArgs: [options]\n          }\n        );\n      }\n    },\n\n    /**\n     * Sets the name of the controller.\n     * @param  {string} name\n     * @return {Controller}\n     */\n    name: function(name) {\n      controller.__li.firstElementChild.firstElementChild.innerHTML = name;\n      return controller;\n    },\n\n    /**\n     * Sets controller to listen for changes on its underlying object.\n     * @return {Controller}\n     */\n    listen: function() {\n      controller.__gui.listen(controller);\n      return controller;\n    },\n\n    /**\n     * Removes the controller from its parent GUI.\n     * @return {Controller}\n     */\n    remove: function() {\n      controller.__gui.remove(controller);\n      return controller;\n    }\n  });\n\n  // All sliders should be accompanied by a box.\n  if (controller instanceof NumberControllerSlider) {\n    const box = new NumberControllerBox(controller.object, controller.property,\n      { min: controller.__min, max: controller.__max, step: controller.__step });\n\n    common.each(['updateDisplay', 'onChange', 'onFinishChange', 'step', 'min', 'max'], function(method) {\n      const pc = controller[method];\n      const pb = box[method];\n      controller[method] = box[method] = function() {\n        const args = Array.prototype.slice.call(arguments);\n        pb.apply(box, args);\n        return pc.apply(controller, args);\n      };\n    });\n\n    dom.addClass(li, 'has-slider');\n    controller.domElement.insertBefore(box.domElement, controller.domElement.firstElementChild);\n  } else if (controller instanceof NumberControllerBox) {\n    const r = function(returned) {\n      // Have we defined both boundaries?\n      if (common.isNumber(controller.__min) && common.isNumber(controller.__max)) {\n        // Well, then lets just replace this with a slider.\n\n        // lets remember if the old controller had a specific name or was listening\n        const oldName = controller.__li.firstElementChild.firstElementChild.innerHTML;\n        const wasListening = controller.__gui.__listening.indexOf(controller) > -1;\n\n        controller.remove();\n        const newController = add(\n          gui,\n          controller.object,\n          controller.property,\n          {\n            before: controller.__li.nextElementSibling,\n            factoryArgs: [controller.__min, controller.__max, controller.__step]\n          }\n        );\n\n        newController.name(oldName);\n        if (wasListening) newController.listen();\n\n        return newController;\n      }\n\n      return returned;\n    };\n\n    controller.min = common.compose(r, controller.min);\n    controller.max = common.compose(r, controller.max);\n  } else if (controller instanceof BooleanController) {\n    dom.bind(li, 'click', function() {\n      dom.fakeEvent(controller.__checkbox, 'click');\n    });\n\n    dom.bind(controller.__checkbox, 'click', function(e) {\n      e.stopPropagation(); // Prevents double-toggle\n    });\n  } else if (controller instanceof FunctionController) {\n    dom.bind(li, 'click', function() {\n      dom.fakeEvent(controller.__button, 'click');\n    });\n\n    dom.bind(li, 'mouseover', function() {\n      dom.addClass(controller.__button, 'hover');\n    });\n\n    dom.bind(li, 'mouseout', function() {\n      dom.removeClass(controller.__button, 'hover');\n    });\n  } else if (controller instanceof ColorController) {\n    dom.addClass(li, 'color');\n    controller.updateDisplay = common.compose(function(val) {\n      li.style.borderLeftColor = controller.__color.toString();\n      return val;\n    }, controller.updateDisplay);\n\n    controller.updateDisplay();\n  }\n\n  controller.setValue = common.compose(function(val) {\n    if (gui.getRoot().__preset_select && controller.isModified()) {\n      markPresetModified(gui.getRoot(), true);\n    }\n\n    return val;\n  }, controller.setValue);\n}\n\nfunction recallSavedValue(gui, controller) {\n  // Find the topmost GUI, that's where remembered objects live.\n  const root = gui.getRoot();\n\n  // Does the object we're controlling match anything we've been told to\n  // remember?\n  const matchedIndex = root.__rememberedObjects.indexOf(controller.object);\n\n  // Why yes, it does!\n  if (matchedIndex !== -1) {\n    // Let me fetch a map of controllers for thcommon.isObject.\n    let controllerMap = root.__rememberedObjectIndecesToControllers[matchedIndex];\n\n    // Ohp, I believe this is the first controller we've created for this\n    // object. Lets make the map fresh.\n    if (controllerMap === undefined) {\n      controllerMap = {};\n      root.__rememberedObjectIndecesToControllers[matchedIndex] =\n        controllerMap;\n    }\n\n    // Keep track of this controller\n    controllerMap[controller.property] = controller;\n\n    // Okay, now have we saved any values for this controller?\n    if (root.load && root.load.remembered) {\n      const presetMap = root.load.remembered;\n\n      // Which preset are we trying to load?\n      let preset;\n\n      if (presetMap[gui.preset]) {\n        preset = presetMap[gui.preset];\n      } else if (presetMap[DEFAULT_DEFAULT_PRESET_NAME]) {\n        // Uhh, you can have the default instead?\n        preset = presetMap[DEFAULT_DEFAULT_PRESET_NAME];\n      } else {\n        // Nada.\n        return;\n      }\n\n      // Did the loaded object remember thcommon.isObject? &&  Did we remember this particular property?\n      if (preset[matchedIndex] && preset[matchedIndex][controller.property] !== undefined) {\n        // We did remember something for this guy ...\n        const value = preset[matchedIndex][controller.property];\n\n        // And that's what it is.\n        controller.initialValue = value;\n        controller.setValue(value);\n      }\n    }\n  }\n}\n\nfunction add(gui, object, property, params) {\n  if (object[property] === undefined) {\n    throw new Error(`Object \"${object}\" has no property \"${property}\"`);\n  }\n\n  let controller;\n\n  if (params.color) {\n    controller = new ColorController(object, property);\n  } else {\n    const factoryArgs = [object, property].concat(params.factoryArgs);\n    controller = ControllerFactory.apply(gui, factoryArgs);\n  }\n\n  if (params.before instanceof Controller) {\n    params.before = params.before.__li;\n  }\n\n  recallSavedValue(gui, controller);\n\n  dom.addClass(controller.domElement, 'c');\n\n  const name = document.createElement('span');\n  dom.addClass(name, 'property-name');\n  name.innerHTML = controller.property;\n\n  const container = document.createElement('div');\n  container.appendChild(name);\n  container.appendChild(controller.domElement);\n\n  const li = addRow(gui, container, params.before);\n\n  dom.addClass(li, GUI.CLASS_CONTROLLER_ROW);\n  if (controller instanceof ColorController) {\n    dom.addClass(li, 'color');\n  } else {\n    dom.addClass(li, typeof controller.getValue());\n  }\n\n  augmentController(gui, li, controller);\n\n  gui.__controllers.push(controller);\n\n  return controller;\n}\n\nfunction getLocalStorageHash(gui, key) {\n  // TODO how does this deal with multiple GUI's?\n  return document.location.href + '.' + key;\n}\n\nfunction addPresetOption(gui, name, setSelected) {\n  const opt = document.createElement('option');\n  opt.innerHTML = name;\n  opt.value = name;\n  gui.__preset_select.appendChild(opt);\n  if (setSelected) {\n    gui.__preset_select.selectedIndex = gui.__preset_select.length - 1;\n  }\n}\n\nfunction showHideExplain(gui, explain) {\n  explain.style.display = gui.useLocalStorage ? 'block' : 'none';\n}\n\nfunction addSaveMenu(gui) {\n  const div = gui.__save_row = document.createElement('li');\n\n  dom.addClass(gui.domElement, 'has-save');\n\n  gui.__ul.insertBefore(div, gui.__ul.firstChild);\n\n  dom.addClass(div, 'save-row');\n\n  const gears = document.createElement('span');\n  gears.innerHTML = '&nbsp;';\n  dom.addClass(gears, 'button gears');\n\n  // TODO replace with FunctionController\n  const button = document.createElement('span');\n  button.innerHTML = 'Save';\n  dom.addClass(button, 'button');\n  dom.addClass(button, 'save');\n\n  const button2 = document.createElement('span');\n  button2.innerHTML = 'New';\n  dom.addClass(button2, 'button');\n  dom.addClass(button2, 'save-as');\n\n  const button3 = document.createElement('span');\n  button3.innerHTML = 'Revert';\n  dom.addClass(button3, 'button');\n  dom.addClass(button3, 'revert');\n\n  const select = gui.__preset_select = document.createElement('select');\n\n  if (gui.load && gui.load.remembered) {\n    common.each(gui.load.remembered, function(value, key) {\n      addPresetOption(gui, key, key === gui.preset);\n    });\n  } else {\n    addPresetOption(gui, DEFAULT_DEFAULT_PRESET_NAME, false);\n  }\n\n  dom.bind(select, 'change', function() {\n    for (let index = 0; index < gui.__preset_select.length; index++) {\n      gui.__preset_select[index].innerHTML = gui.__preset_select[index].value;\n    }\n\n    gui.preset = this.value;\n  });\n\n  div.appendChild(select);\n  div.appendChild(gears);\n  div.appendChild(button);\n  div.appendChild(button2);\n  div.appendChild(button3);\n\n  if (SUPPORTS_LOCAL_STORAGE) {\n    const explain = document.getElementById('dg-local-explain');\n    const localStorageCheckBox = document.getElementById('dg-local-storage');\n    const saveLocally = document.getElementById('dg-save-locally');\n\n    saveLocally.style.display = 'block';\n\n    if (localStorage.getItem(getLocalStorageHash(gui, 'isLocal')) === 'true') {\n      localStorageCheckBox.setAttribute('checked', 'checked');\n    }\n\n    showHideExplain(gui, explain);\n\n    // TODO: Use a boolean controller, fool!\n    dom.bind(localStorageCheckBox, 'change', function() {\n      gui.useLocalStorage = !gui.useLocalStorage;\n      showHideExplain(gui, explain);\n    });\n  }\n\n  const newConstructorTextArea = document.getElementById('dg-new-constructor');\n\n  dom.bind(newConstructorTextArea, 'keydown', function(e) {\n    if (e.metaKey && (e.which === 67 || e.keyCode === 67)) {\n      SAVE_DIALOGUE.hide();\n    }\n  });\n\n  dom.bind(gears, 'click', function() {\n    newConstructorTextArea.innerHTML = JSON.stringify(gui.getSaveObject(), undefined, 2);\n    SAVE_DIALOGUE.show();\n    newConstructorTextArea.focus();\n    newConstructorTextArea.select();\n  });\n\n  dom.bind(button, 'click', function() {\n    gui.save();\n  });\n\n  dom.bind(button2, 'click', function() {\n    const presetName = prompt('Enter a new preset name.');\n    if (presetName) {\n      gui.saveAs(presetName);\n    }\n  });\n\n  dom.bind(button3, 'click', function() {\n    gui.revert();\n  });\n\n  // div.appendChild(button2);\n}\n\nfunction addResizeHandle(gui) {\n  let pmouseX;\n\n  gui.__resize_handle = document.createElement('div');\n\n  common.extend(gui.__resize_handle.style, {\n\n    width: '6px',\n    marginLeft: '-3px',\n    height: '200px',\n    cursor: 'ew-resize',\n    position: 'absolute'\n    // border: '1px solid blue'\n\n  });\n\n  function drag(e) {\n    e.preventDefault();\n\n    gui.width += pmouseX - e.clientX;\n    gui.onResize();\n    pmouseX = e.clientX;\n\n    return false;\n  }\n\n  function dragStop() {\n    dom.removeClass(gui.__closeButton, GUI.CLASS_DRAG);\n    dom.unbind(window, 'mousemove', drag);\n    dom.unbind(window, 'mouseup', dragStop);\n  }\n\n  function dragStart(e) {\n    e.preventDefault();\n\n    pmouseX = e.clientX;\n\n    dom.addClass(gui.__closeButton, GUI.CLASS_DRAG);\n    dom.bind(window, 'mousemove', drag);\n    dom.bind(window, 'mouseup', dragStop);\n\n    return false;\n  }\n\n  dom.bind(gui.__resize_handle, 'mousedown', dragStart);\n  dom.bind(gui.__closeButton, 'mousedown', dragStart);\n\n  gui.domElement.insertBefore(gui.__resize_handle, gui.domElement.firstElementChild);\n}\n\nfunction setWidth(gui, w) {\n  gui.domElement.style.width = w + 'px';\n  // Auto placed save-rows are position fixed, so we have to\n  // set the width manually if we want it to bleed to the edge\n  if (gui.__save_row && gui.autoPlace) {\n    gui.__save_row.style.width = w + 'px';\n  }\n  if (gui.__closeButton) {\n    gui.__closeButton.style.width = w + 'px';\n  }\n}\n\nfunction getCurrentPreset(gui, useInitialValues) {\n  const toReturn = {};\n\n  // For each object I'm remembering\n  common.each(gui.__rememberedObjects, function(val, index) {\n    const savedValues = {};\n\n    // The controllers I've made for thcommon.isObject by property\n    const controllerMap =\n      gui.__rememberedObjectIndecesToControllers[index];\n\n    // Remember each value for each property\n    common.each(controllerMap, function(controller, property) {\n      savedValues[property] = useInitialValues ? controller.initialValue : controller.getValue();\n    });\n\n    // Save the values for thcommon.isObject\n    toReturn[index] = savedValues;\n  });\n\n  return toReturn;\n}\n\nfunction setPresetSelectIndex(gui) {\n  for (let index = 0; index < gui.__preset_select.length; index++) {\n    if (gui.__preset_select[index].value === gui.preset) {\n      gui.__preset_select.selectedIndex = index;\n    }\n  }\n}\n\nfunction updateDisplays(controllerArray) {\n  if (controllerArray.length !== 0) {\n    requestAnimationFrame.call(window, function() {\n      updateDisplays(controllerArray);\n    });\n  }\n\n  common.each(controllerArray, function(c) {\n    c.updateDisplay();\n  });\n}\n\nexport default GUI;\n", "/**\n * dat-gui JavaScript Controller Library\n * https://github.com/dataarts/dat.gui\n *\n * Copyright 2011 Data Arts Team, Google Creative Lab\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n */\n\nimport Color from './color/Color';\nimport math from './color/math';\nimport interpret from './color/interpret';\n\nimport Controller from './controllers/Controller';\nimport BooleanController from './controllers/BooleanController';\nimport OptionController from './controllers/OptionController';\nimport StringController from './controllers/StringController';\nimport NumberController from './controllers/NumberController';\nimport NumberControllerBox from './controllers/NumberControllerBox';\nimport NumberControllerSlider from './controllers/NumberControllerSlider';\nimport FunctionController from './controllers/FunctionController';\nimport ColorController from './controllers/ColorController';\n\nimport domImport from './dom/dom';\nimport GUIImport from './gui/GUI';\n\nexport const color = {\n  Color: Color,\n  math: math,\n  interpret: interpret\n};\n\nexport const controllers = {\n  Controller: Controller,\n  BooleanController: BooleanController,\n  OptionController: OptionController,\n  StringController: StringController,\n  NumberController: NumberController,\n  NumberControllerBox: NumberControllerBox,\n  NumberControllerSlider: NumberControllerSlider,\n  FunctionController: FunctionController,\n  ColorController: ColorController\n};\n\nexport const dom = { dom: domImport };\n\nexport const gui = { GUI: GUIImport };\n\nexport const GUI = GUIImport;\n\nexport default {\n  color,\n  controllers,\n  dom,\n  gui,\n  GUI\n};\n"], "mappings": ";;;;;;;;;;;;;;AAae,SAAA,cAASA,QAAOC,aAAa;MACpCC,cAAcF,OAAMG,QAAQC,eAAeC,SAA7B;MAEdC,IAAIC,KAAKC,MAAMR,OAAMM,CAAjB;MACJG,IAAIF,KAAKC,MAAMR,OAAMS,CAAjB;MACJC,IAAIH,KAAKC,MAAMR,OAAMU,CAAjB;MACJC,IAAIX,OAAMW;MACVC,IAAIL,KAAKC,MAAMR,OAAMY,CAAjB;MACJC,IAAIb,OAAMa,EAAEC,QAAQ,CAAhB;MACJC,IAAIf,OAAMe,EAAED,QAAQ,CAAhB;MAENb,eAAgBC,gBAAgB,oBAAsBA,gBAAgB,gBAAiB;QACrFc,MAAMhB,OAAMiB,IAAIZ,SAAS,EAAnB;WACHW,IAAIE,SAAS,GAAG;YACf,MAAMF;;WAEP,MAAMA;aACJd,gBAAgB,WAAW;WAC7B,SAASI,IAAI,MAAMG,IAAI,MAAMC,IAAI;aAC/BR,gBAAgB,YAAY;WAC9B,UAAUI,IAAI,MAAMG,IAAI,MAAMC,IAAI,MAAMC,IAAI;aAC1CT,gBAAgB,OAAO;WACzB,OAAOF,OAAMiB,IAAIZ,SAAS,EAAnB;aACLH,gBAAgB,aAAa;WAC/B,MAAMI,IAAI,MAAMG,IAAI,MAAMC,IAAI;aAC5BR,gBAAgB,cAAc;WAChC,MAAMI,IAAI,MAAMG,IAAI,MAAMC,IAAI,MAAMC,IAAI;aACtCT,gBAAgB,WAAW;WAC7B,QAAQI,IAAI,QAAQG,IAAI,QAAQC,IAAI;aAClCR,gBAAgB,YAAY;WAC9B,QAAQI,IAAI,QAAQG,IAAI,QAAQC,IAAI,QAAQC,IAAI;aAC9CT,gBAAgB,WAAW;WAC7B,QAAQU,IAAI,QAAQC,IAAI,QAAQE,IAAI;aAClCb,gBAAgB,YAAY;WAC9B,QAAQU,IAAI,QAAQC,IAAI,QAAQE,IAAI,QAAQJ,IAAI;;SAGlD;;ACrCT,IAAMQ,WAAWC,MAAMC,UAAUC;AACjC,IAAMC,YAAYH,MAAMC,UAAUG;AAQlC,IAAMC,SAAS;SACN,CAAA;UAEC,SAAA,OAASC,QAAQ;SAClBC,KAAKJ,UAAUK,KAAKC,WAAW,CAA1B,GAA8B,SAASC,KAAK;UAC9CC,OAAO,KAAKC,SAASF,GAAd,IAAqBG,OAAOF,KAAKD,GAAZ,IAAmB,CAAA;WAChDR,SAAQ,SAASY,KAAK;YACrB,CAAC,KAAKC,YAAYL,IAAII,GAAJ,CAAjB,GAA4B;iBACxBA,GAAP,IAAcJ,IAAII,GAAJ;;SAEhBE,KAAK,IAJM,CAAb;OAKC,IAPH;WASOV;;YAGC,SAAA,SAASA,QAAQ;SACpBC,KAAKJ,UAAUK,KAAKC,WAAW,CAA1B,GAA8B,SAASC,KAAK;UAC9CC,OAAO,KAAKC,SAASF,GAAd,IAAqBG,OAAOF,KAAKD,GAAZ,IAAmB,CAAA;WAChDR,SAAQ,SAASY,KAAK;YACrB,KAAKC,YAAYT,OAAOQ,GAAP,CAAjB,GAA+B;iBAC1BA,GAAP,IAAcJ,IAAII,GAAJ;;SAEhBE,KAAK,IAJM,CAAb;OAKC,IAPH;WASOV;;WAGA,SAAA,UAAW;QACZW,SAASd,UAAUK,KAAKC,SAAf;WACR,WAAW;UACZS,OAAOf,UAAUK,KAAKC,SAAf;eACFU,IAAIF,OAAOnB,SAAS,GAAGqB,KAAK,GAAGA,KAAK;eACpC,CAACF,OAAOE,CAAP,EAAUC,MAAM,MAAMF,IAAtB,CAAD;;aAEFA,KAAK,CAAL;;;QAIL,SAAA,KAASR,KAAKW,KAAKC,OAAO;QAC1B,CAACZ,KAAK;;;QAINX,YAAYW,IAAIR,WAAWQ,IAAIR,YAAYH,UAAU;UACnDG,QAAQmB,KAAKC,KAAjB;eACSZ,IAAIZ,WAAWY,IAAIZ,SAAS,GAAG;UACpCgB,MAAAA;UACAS,IAAAA;WACCT,MAAM,GAAGS,IAAIb,IAAIZ,QAAQgB,MAAMS,GAAGT,OAAO;YACxCA,OAAOJ,OAAOW,IAAIb,KAAKc,OAAOZ,IAAII,GAAJ,GAAUA,GAA1B,MAAmC,KAAKU,OAAO;;;;WAI9D;eACMV,QAAOJ,KAAK;YACjBW,IAAIb,KAAKc,OAAOZ,IAAII,IAAJ,GAAUA,IAA1B,MAAmC,KAAKU,OAAO;;;;;;SAOlD,SAAA,MAASC,KAAK;eACRA,KAAK,CAAhB;;YAIQ,SAAA,SAASC,MAAMC,WAAWC,iBAAiB;QAC/CC,UAAAA;WAEG,WAAW;UACVnB,MAAM;UACNQ,OAAOT;eACJqB,UAAU;kBACP;YACN,CAACF,gBAAiBF,MAAKN,MAAMV,KAAKQ,IAAhB;;UAGlBa,UAAUH,mBAAmB,CAACC;mBAEvBA,OAAb;gBACUG,WAAWF,SAASH,SAApB;UAENI,SAAS;aACNX,MAAMV,KAAKQ,IAAhB;;;;WAKG,SAAA,QAASR,KAAK;QACjBA,IAAIuB,QAAS,QAAOvB,IAAIuB,QAAJ;WACjB9B,UAAUK,KAAKE,GAAf;;eAGI,SAAA,YAASA,KAAK;WAClBA,QAAQwB;;UAGT,SAAA,OAASxB,KAAK;WACbA,QAAQ;;;;;;;;;;IAGV,SAASA,KAAK;WACZyB,MAAMzB,GAAN;GADT;WAISV,MAAMoC,WAAW,SAAS1B,KAAK;WAC/BA,IAAI2B,gBAAgBrC;;YAGnB,SAAA,SAASU,KAAK;WACfA,QAAQG,OAAOH,GAAP;;YAGP,SAAA,SAASA,KAAK;WACfA,QAAQA,MAAM;;YAGb,SAAA,SAASA,KAAK;WACfA,QAAQA,MAAM;;aAGZ,SAAA,UAASA,KAAK;WAChBA,QAAQ,SAASA,QAAQ;;cAGtB,SAAA,WAASA,KAAK;WACjBA,eAAe4B;;;ACvI1B,IAAMC,kBAAkB;EAEtB;YACUC,OAAOC;iBACF;sBACK;cACR,SAAA,KAASC,UAAU;cACjBC,OAAOD,SAASE,MAAM,oCAAf;cACTD,SAAS,MAAM;mBACV;;iBAGF;mBACE;iBACFE,SACH,OACAF,KAAK,CAAL,EAAQ1D,SAAR,IAAqB0D,KAAK,CAAL,EAAQ1D,SAAR,IACrB0D,KAAK,CAAL,EAAQ1D,SAAR,IAAqB0D,KAAK,CAAL,EAAQ1D,SAAR,IACrB0D,KAAK,CAAL,EAAQ1D,SAAR,IAAqB0D,KAAK,CAAL,EAAQ1D,SAAR,GAAoB,CAJtC;;;eASFA;;oBAGK;cACN,SAAA6D,MAASJ,UAAU;cACjBC,OAAOD,SAASE,MAAM,mBAAf;cACTD,SAAS,MAAM;mBACV;;iBAGF;mBACE;iBACFE,SAAS,OAAOF,KAAK,CAAL,EAAQ1D,SAAR,GAAoB,CAApC;;;eAIFA;;eAGA;cACD,SAAA6D,MAASJ,UAAU;cACjBC,OAAOD,SAASE,MAAM,6CAAf;cACTD,SAAS,MAAM;mBACV;;iBAGF;mBACE;eACJI,WAAWJ,KAAK,CAAL,CAAX;eACAI,WAAWJ,KAAK,CAAL,CAAX;eACAI,WAAWJ,KAAK,CAAL,CAAX;;;eAIA1D;;gBAGC;cACF,SAAA6D,MAASJ,UAAU;cACjBC,OAAOD,SAASE,MAAM,0DAAf;cACTD,SAAS,MAAM;mBACV;;iBAGF;mBACE;eACJI,WAAWJ,KAAK,CAAL,CAAX;eACAI,WAAWJ,KAAK,CAAL,CAAX;eACAI,WAAWJ,KAAK,CAAL,CAAX;eACAI,WAAWJ,KAAK,CAAL,CAAX;;;eAIA1D;;;;EAMb;YACUuD,OAAOQ;iBAEF;WAEN;cACG,SAAAF,MAASJ,UAAU;iBAChB;mBACE;iBACFA;4BACW;;;eAIb,SAAA,MAAS9D,QAAO;iBACdA,OAAMiB;;;;;EASrB;YACU2C,OAAOJ;iBACF;iBACA;cACH,SAAAU,MAASJ,UAAU;cACnBA,SAAS5C,WAAW,GAAG;mBAClB;;iBAGF;mBACE;eACJ4C,SAAS,CAAT;eACAA,SAAS,CAAT;eACAA,SAAS,CAAT;;;eAIA,SAAAO,OAASrE,QAAO;iBACd,CAACA,OAAMM,GAAGN,OAAMS,GAAGT,OAAMU,CAAzB;;;kBAIC;cACJ,SAAAwD,MAASJ,UAAU;cACnBA,SAAS5C,WAAW,EAAG,QAAO;iBAC3B;mBACE;eACJ4C,SAAS,CAAT;eACAA,SAAS,CAAT;eACAA,SAAS,CAAT;eACAA,SAAS,CAAT;;;eAIA,SAAAO,OAASrE,QAAO;iBACd,CAACA,OAAMM,GAAGN,OAAMS,GAAGT,OAAMU,GAAGV,OAAMW,CAAlC;;;;;EAOf;YACUiD,OAAO5B;iBACF;gBAED;cACF,SAAAkC,MAASJ,UAAU;cACnBF,OAAOQ,SAASN,SAASxD,CAAzB,KACFsD,OAAOQ,SAASN,SAASrD,CAAzB,KACAmD,OAAOQ,SAASN,SAASpD,CAAzB,KACAkD,OAAOQ,SAASN,SAASnD,CAAzB,GAA6B;mBACtB;qBACE;iBACJmD,SAASxD;iBACTwD,SAASrD;iBACTqD,SAASpD;iBACToD,SAASnD;;;iBAGT;;eAGF,SAAA0D,OAASrE,QAAO;iBACd;eACFA,OAAMM;eACNN,OAAMS;eACNT,OAAMU;eACNV,OAAMW;;;;eAKN;cACD,SAAAuD,MAASJ,UAAU;cACnBF,OAAOQ,SAASN,SAASxD,CAAzB,KACFsD,OAAOQ,SAASN,SAASrD,CAAzB,KACAmD,OAAOQ,SAASN,SAASpD,CAAzB,GAA6B;mBACtB;qBACE;iBACJoD,SAASxD;iBACTwD,SAASrD;iBACTqD,SAASpD;;;iBAGT;;eAGF,SAAA2D,OAASrE,QAAO;iBACd;eACFA,OAAMM;eACNN,OAAMS;eACNT,OAAMU;;;;gBAKL;cACF,SAAAwD,OAASJ,UAAU;cACnBF,OAAOQ,SAASN,SAASlD,CAAzB,KACFgD,OAAOQ,SAASN,SAASjD,CAAzB,KACA+C,OAAOQ,SAASN,SAAS/C,CAAzB,KACA6C,OAAOQ,SAASN,SAASnD,CAAzB,GAA6B;mBACtB;qBACE;iBACJmD,SAASlD;iBACTkD,SAASjD;iBACTiD,SAAS/C;iBACT+C,SAASnD;;;iBAGT;;eAGF,SAAA0D,OAASrE,QAAO;iBACd;eACFA,OAAMY;eACNZ,OAAMa;eACNb,OAAMe;eACNf,OAAMW;;;;eAKN;cACD,SAAAuD,OAASJ,UAAU;cACnBF,OAAOQ,SAASN,SAASlD,CAAzB,KACFgD,OAAOQ,SAASN,SAASjD,CAAzB,KACA+C,OAAOQ,SAASN,SAAS/C,CAAzB,GAA6B;mBACtB;qBACE;iBACJ+C,SAASlD;iBACTkD,SAASjD;iBACTiD,SAAS/C;;;iBAGT;;eAGF,SAAAsD,OAASrE,QAAO;iBACd;eACFA,OAAMY;eACNZ,OAAMa;eACNb,OAAMe;;;;;;AAzPG;AAiQxB,IAAIuD,SAAAA;AACJ,IAAIC,WAAAA;AAEJ,IAAMC,YAAY,SAAZA,aAAuB;aAChB;MAELV,WAAWjC,UAAUX,SAAS,IAAI0C,OAAOP,QAAQxB,SAAf,IAA4BA,UAAU,CAAV;SAC7DF,KAAKgC,iBAAiB,SAASc,QAAQ;QACxCA,OAAOC,OAAOZ,QAAd,GAAyB;aACpBnC,KAAK8C,OAAOE,aAAa,SAASC,YAAYxE,gBAAgB;iBAC1DwE,WAAWV,KAAKJ,QAAhB;YAELS,aAAa,SAASD,WAAW,OAAO;qBAC/BA;iBACJlE,iBAAiBA;iBACjBwE,aAAaA;iBACbhB,OAAOhB;;OAPlB;aAWOgB,OAAOhB;;GAblB;SAiBO2B;;AC5RT,IAAIM,eAAAA;AAEJ,IAAMC,YAAY;cACJ,SAAA,WAASlE,GAAGC,GAAGE,GAAG;QACtBgE,KAAKxE,KAAKyE,MAAMpE,IAAI,EAAf,IAAqB;QAE1BqE,IAAIrE,IAAI,KAAKL,KAAKyE,MAAMpE,IAAI,EAAf;QACbsE,IAAInE,KAAK,IAAMF;QACfsE,IAAIpE,KAAK,IAAOkE,IAAIpE;QACpBuE,IAAIrE,KAAK,KAAQ,IAAMkE,KAAKpE;QAE5BwE,IAAI,CACR,CAACtE,GAAGqE,GAAGF,CAAP,GACA,CAACC,GAAGpE,GAAGmE,CAAP,GACA,CAACA,GAAGnE,GAAGqE,CAAP,GACA,CAACF,GAAGC,GAAGpE,CAAP,GACA,CAACqE,GAAGF,GAAGnE,CAAP,GACA,CAACA,GAAGmE,GAAGC,CAAP,CANQ,EAORJ,EAPQ;WASH;SACFM,EAAE,CAAF,IAAO;SACPA,EAAE,CAAF,IAAO;SACPA,EAAE,CAAF,IAAO;;;cAIF,SAAA,WAAS/E,GAAGG,GAAGC,GAAG;QACtB4E,MAAM/E,KAAK+E,IAAIhF,GAAGG,GAAGC,CAAf;QACN6E,MAAMhF,KAAKgF,IAAIjF,GAAGG,GAAGC,CAAf;QACN8E,QAAQD,MAAMD;QAChB1E,IAAAA;QACAC,IAAAA;QAEA0E,QAAQ,GAAG;UACTC,QAAQD;WACP;aACE;WACFE;WACA;WACA;;;QAIHnF,MAAMiF,KAAK;WACR9E,IAAIC,KAAK8E;eACL/E,MAAM8E,KAAK;UAChB,KAAK7E,IAAIJ,KAAKkF;WACb;UACD,KAAKlF,IAAIG,KAAK+E;;SAEf;QACD5E,IAAI,GAAG;WACJ;;WAGA;SACFA,IAAI;;SAEJ2E,MAAM;;;cAID,SAAA,WAASjF,GAAGG,GAAGC,GAAG;QACxBO,MAAM,KAAKyE,mBAAmB,GAAG,GAAGpF,CAA9B;UACJ,KAAKoF,mBAAmBzE,KAAK,GAAGR,CAAhC;UACA,KAAKiF,mBAAmBzE,KAAK,GAAGP,CAAhC;WACCO;;sBAGW,SAAA,mBAASA,KAAK0E,gBAAgB;WACxC1E,OAAQ0E,iBAAiB,IAAM;;sBAGrB,SAAA,mBAAS1E,KAAK0E,gBAAgBC,OAAO;WAChDA,UAAUf,eAAec,iBAAiB,KAAM1E,MAAM,EAAE,OAAQ4D;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ICtErEgB,QAAAA,WAAAA;oBACU;;SACP1F,UAAUqE,UAAUhC,MAAM,MAAMX,SAAtB;QAEX,KAAK1B,YAAY,OAAO;YACpB,IAAI2F,MAAM,qCAAV;;SAGH3F,QAAQQ,IAAI,KAAKR,QAAQQ,KAAK;;;;+BAG1B;aACFoF,cAAc,IAAd;;;;kCAGK;aACLA,cAAc,MAAM,IAApB;;;;iCAGI;aACJ,KAAK5F,QAAQyE,WAAWP,MAAM,IAA9B;;;;;AAIX,SAAS2B,mBAAmBtE,QAAQuE,WAAWC,mBAAmB;SACzDC,eAAezE,QAAQuE,WAAW;SAClC,SAAAG,UAAW;UACV,KAAKjG,QAAQkG,UAAU,OAAO;eACzB,KAAKlG,QAAQ8F,SAAb;;YAGHK,eAAe,MAAML,WAAWC,iBAAtC;aAEO,KAAK/F,QAAQ8F,SAAb;;SAGJ,SAAAM,QAASxF,GAAG;UACX,KAAKZ,QAAQkG,UAAU,OAAO;cAC1BC,eAAe,MAAML,WAAWC,iBAAtC;aACK/F,QAAQkG,QAAQ;;WAGlBlG,QAAQ8F,SAAb,IAA0BlF;;GAjB9B;;AAsBF,SAASyF,mBAAmB9E,QAAQuE,WAAW;SACtCE,eAAezE,QAAQuE,WAAW;SAClC,SAAAG,UAAW;UACV,KAAKjG,QAAQkG,UAAU,OAAO;eACzB,KAAKlG,QAAQ8F,SAAb;;YAGHQ,eAAe,IAArB;aAEO,KAAKtG,QAAQ8F,SAAb;;SAGJ,SAAAM,QAASxF,GAAG;UACX,KAAKZ,QAAQkG,UAAU,OAAO;cAC1BI,eAAe,IAArB;aACKtG,QAAQkG,QAAQ;;WAGlBlG,QAAQ8F,SAAb,IAA0BlF;;GAjB9B;;AAuBF8E,MAAMS,iBAAiB,SAAStG,QAAOiG,WAAWC,mBAAmB;MAC/DlG,OAAMG,QAAQkG,UAAU,OAAO;WAC3BlG,QAAQ8F,SAAd,IAA2BS,UAAKC,mBAAmB3G,OAAMG,QAAQc,KAAKiF,iBAA3C;aAClBlG,OAAMG,QAAQkG,UAAU,OAAO;WACjCO,OAAO5G,OAAMG,SAASuG,UAAKG,WAAW7G,OAAMG,QAAQS,GAAGZ,OAAMG,QAAQU,GAAGb,OAAMG,QAAQY,CAAhE,CAA7B;SACK;UACC,IAAI+E,MAAM,uBAAV;;;AAIVD,MAAMY,iBAAiB,SAASzG,QAAO;MAC/BsE,UAASoC,UAAKI,WAAW9G,OAAMM,GAAGN,OAAMS,GAAGT,OAAMU,CAAxC;SAERkG,OAAO5G,OAAMG,SAClB;OACKmE,QAAOzD;OACPyD,QAAOvD;GAHd;MAMI,CAAC6C,OAAOL,MAAMe,QAAO1D,CAApB,GAAwB;WACrBT,QAAQS,IAAI0D,QAAO1D;aAChBgD,OAAOzB,YAAYnC,OAAMG,QAAQS,CAAjC,GAAqC;WACxCT,QAAQS,IAAI;;;AAItBiF,MAAMkB,aAAa,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,OAAO,GAAtC;AAEnBf,mBAAmBH,MAAMxE,WAAW,KAAK,CAAzC;AACA2E,mBAAmBH,MAAMxE,WAAW,KAAK,CAAzC;AACA2E,mBAAmBH,MAAMxE,WAAW,KAAK,CAAzC;AAEAmF,mBAAmBX,MAAMxE,WAAW,GAApC;AACAmF,mBAAmBX,MAAMxE,WAAW,GAApC;AACAmF,mBAAmBX,MAAMxE,WAAW,GAApC;AAEAY,OAAOkE,eAAeN,MAAMxE,WAAW,KAAK;OACrC,SAAA,SAAW;WACP,KAAKlB,QAAQQ;;OAGjB,SAAA,OAASI,GAAG;SACVZ,QAAQQ,IAAII;;CANrB;AAUAkB,OAAOkE,eAAeN,MAAMxE,WAAW,OAAO;OACvC,SAAA+E,UAAW;QACV,KAAKjG,QAAQkG,UAAU,OAAO;WAC3BlG,QAAQc,MAAMyF,UAAKM,WAAW,KAAK1G,GAAG,KAAKG,GAAG,KAAKC,CAArC;WACdP,QAAQkG,QAAQ;;WAGhB,KAAKlG,QAAQc;;OAGjB,SAAAsF,QAASxF,GAAG;SACVZ,QAAQkG,QAAQ;SAChBlG,QAAQc,MAAMF;;CAZvB;ICpHMkG,aAAAA,WAAAA;uBACQC,QAAQC,UAAU;;SACvBC,eAAeF,OAAOC,QAAP;SAMfE,aAAaC,SAASC,cAAc,KAAvB;SAMbL,SAASA;SAMTC,WAAWA;SAOXK,aAAalE;SAObmE,mBAAmBnE;;;;6BAWjBT,KAAK;WACP2E,aAAa3E;aACX;;;;mCAYMA,KAAK;WACb4E,mBAAmB5E;aACjB;;;;6BAQA6E,UAAU;WACZR,OAAO,KAAKC,QAAjB,IAA6BO;UACzB,KAAKF,YAAY;aACdA,WAAW5F,KAAK,MAAM8F,QAA3B;;WAGGC,cAAL;aACO;;;;+BAQE;aACF,KAAKT,OAAO,KAAKC,QAAjB;;;;qCAQO;aACP;;;;iCAMI;aACJ,KAAKC,iBAAiB,KAAKQ,SAAL;;;;;ACzGjC,IAAMC,YAAY;cACJ,CAAC,QAAD;eACC,CAAC,SAAS,aAAa,aAAa,WAAW,WAA/C;kBACG,CAAC,SAAD;;AAGlB,IAAMC,gBAAgB,CAAA;AACtBlE,OAAOjC,KAAKkG,WAAW,SAAS9G,GAAGgH,GAAG;SAC7BpG,KAAKZ,GAAG,SAASiH,GAAG;kBACXA,CAAd,IAAmBD;GADrB;CADF;AAMA,IAAME,mBAAmB;AAEzB,SAASC,iBAAiBC,KAAK;MACzBA,QAAQ,OAAOvE,OAAOzB,YAAYgG,GAAnB,GAAyB;WACnC;;MAGHnE,QAAQmE,IAAInE,MAAMiE,gBAAV;MAEV,CAACrE,OAAOwE,OAAOpE,KAAd,GAAsB;WAClBG,WAAWH,MAAM,CAAN,CAAX;;SAKF;;AAOT,IAAMqE,MAAM;kBAOM,SAAA,eAASC,MAAMC,YAAY;QACrCD,SAAShF,UAAagF,KAAKE,UAAUlF,OAAW;SAE/CmF,gBAAgBF,aAAa,WAAW;aACpC;QACL,WAAW;IAAA;SAGVC,MAAME,gBAAgBH,aAAa,SAAS;SAC5CC,MAAMG,kBAAkBJ,aAAa,SAAS;SAC9CK,eAAeL,aAAa,OAAO;;kBAS1B,SAAA,eAASD,MAAMO,KAAKC,MAAM;QACpCC,WAAWD;QACXE,aAAaH;QAEbjF,OAAOzB,YAAY6G,UAAnB,GAAgC;mBACrB;;QAGXpF,OAAOzB,YAAY4G,QAAnB,GAA8B;iBACrB;;SAGRP,MAAMS,WAAW;QAElBD,YAAY;WACTR,MAAMU,OAAO;WACbV,MAAMW,QAAQ;;QAEjBJ,UAAU;WACPP,MAAMY,MAAM;WACZZ,MAAMa,SAAS;;;aAUb,SAAA,UAASf,MAAMgB,WAAWC,MAAMC,KAAK;QACxCC,SAASF,QAAQ,CAAA;QACjBG,YAAY5B,cAAcwB,SAAd;QACd,CAACI,WAAW;YACR,IAAI5D,MAAM,gBAAgBwD,YAAY,iBAAtC;;QAEFK,MAAMrC,SAASsC,YAAYF,SAArB;YACJA,WAAR;WACO;YAEGG,UAAUJ,OAAOK,KAAKL,OAAOI,WAAW;YACxCE,UAAUN,OAAOO,KAAKP,OAAOM,WAAW;YAC1CE;UAAeX;UAAWG,OAAOS,WAAW;UAC9CT,OAAOU,cAAc;UAAMC;UAAQX,OAAOY,cAAc;UACxD;;;;;UAIO;UAAO;UAAO;UAAO;UAAG;QANjC;;;WASG;YAEGC,OAAOX,IAAIY,qBAAqBZ,IAAIa;eACnCC,SAAShB,QAAQ;sBACV;mBACH;kBACD;oBACE;mBACD;mBACAnG;oBACCA;SAPZ;aASKgG,WAAWG,OAAOS,WAAW,OAChCT,OAAOU,YAAYC,QACnBX,OAAOiB,SAASjB,OAAOkB,QACvBlB,OAAOmB,UAAUnB,OAAOoB,SACxBpB,OAAOqB,SAASrB,OAAOsB,QAJzB;;;;YASIC,UAAU1B,WAAWG,OAAOS,WAAW,OAAOT,OAAOU,cAAc,IAAvE;;;;WAIGM,SAASd,KAAKH,GAArB;SACKyB,cAActB,GAAnB;;QAUI,SAAA,KAASrB,MAAM4C,OAAOpI,MAAMqI,SAAS;QACnCC,OAAOD,WAAW;QACpB7C,KAAK+C,kBAAkB;WACpBA,iBAAiBH,OAAOpI,MAAMsI,IAAnC;eACS9C,KAAKgD,aAAa;WACtBA,YAAY,OAAOJ,OAAOpI,IAA/B;;WAEKuF;;UAUD,SAAA,OAASC,MAAM4C,OAAOpI,MAAMqI,SAAS;QACrCC,OAAOD,WAAW;QACpB7C,KAAKiD,qBAAqB;WACvBA,oBAAoBL,OAAOpI,MAAMsI,IAAtC;eACS9C,KAAKkD,aAAa;WACtBA,YAAY,OAAON,OAAOpI,IAA/B;;WAEKuF;;YAQC,SAAA,SAASC,MAAMoB,WAAW;QAC9BpB,KAAKoB,cAAcpG,QAAW;WAC3BoG,YAAYA;eACRpB,KAAKoB,cAAcA,WAAW;UACjC+B,UAAUnD,KAAKoB,UAAUgC,MAAM,IAArB;UACZD,QAAQE,QAAQjC,SAAhB,MAA+B,IAAI;gBAC7BkC,KAAKlC,SAAb;aACKA,YAAY+B,QAAQI,KAAK,GAAb,EAAkBC,QAAQ,QAAQ,EAAlC,EAAsCA,QAAQ,QAAQ,EAAtD;;;WAGdzD;;eAQI,SAAA,YAASC,MAAMoB,WAAW;QACjCA,WAAW;UACTpB,KAAKoB,cAAcA,WAAW;aAC3BqC,gBAAgB,OAArB;aACK;YACCN,UAAUnD,KAAKoB,UAAUgC,MAAM,IAArB;YACVM,SAAQP,QAAQE,QAAQjC,SAAhB;YACVsC,WAAU,IAAI;kBACRC,OAAOD,QAAO,CAAtB;eACKtC,YAAY+B,QAAQI,KAAK,GAAb;;;WAGhB;WACAnC,YAAYpG;;WAEZ+E;;YAGC,SAAA,SAASC,MAAMoB,WAAW;WAC3B,IAAIwC,OAAO,eAAexC,YAAY,YAAtC,EAAoD3F,KAAKuE,KAAKoB,SAA9D,KAA4E;;YAO3E,SAAA,SAASpB,MAAM;QACjBE,QAAQ2D,iBAAiB7D,IAAjB;WAEPJ,iBAAiBM,MAAM,mBAAN,CAAjB,IACLN,iBAAiBM,MAAM,oBAAN,CAAjB,IACAN,iBAAiBM,MAAM,cAAN,CAAjB,IACAN,iBAAiBM,MAAM,eAAN,CAAjB,IACAN,iBAAiBM,MAAM4D,KAAvB;;aAOO,SAAA,UAAS9D,MAAM;QAClBE,QAAQ2D,iBAAiB7D,IAAjB;WAEPJ,iBAAiBM,MAAM,kBAAN,CAAjB,IACLN,iBAAiBM,MAAM,qBAAN,CAAjB,IACAN,iBAAiBM,MAAM,aAAN,CAAjB,IACAN,iBAAiBM,MAAM,gBAAN,CAAjB,IACAN,iBAAiBM,MAAM6D,MAAvB;;aAOO,SAAA,UAASC,IAAI;QAClBhE,OAAOgE;QACLC,SAAS,EAAErD,MAAM,GAAGE,KAAK,EAAhB;QACXd,KAAKkE,cAAc;SAClB;eACMtD,QAAQZ,KAAKmE;eACbrD,OAAOd,KAAKoE;eACZpE,KAAKkE;eACLlE;;WAEJiE;;YAQC,SAAA,SAASjE,MAAM;WAChBA,SAAShB,SAASqF,kBAAkBrE,KAAKsE,QAAQtE,KAAKuE;;;ICtQ3DC,oBAAAA,SAAAA,aAAAA;;8BACQ5F,QAAQC,UAAU;;wIACtBD,QAAQC,QADc,CAAA;QAGtB4F,QAAAA;WACDC,SAAS,OAAKpF,SAAL;WAETqF,aAAa3F,SAASC,cAAc,OAAvB;WACb0F,WAAWC,aAAa,QAAQ,UAArC;aAESC,WAAW;YACZC,SAAS,CAACL,MAAMC,MAAtB;;QAGE5K,KAAK,OAAK6K,YAAY,UAAUE,UAAU,KAA9C;WAEK9F,WAAWgG,YAAY,OAAKJ,UAAjC;WAGKtF,cAAL;;;;;6BAGO5G,GAAG;UACJwD,YAAAA,IAAAA,mBAAAA,UAAAA,aAAAA,OAAAA,eAAAA,mBAAAA,SAAAA,GAAAA,YAAAA,IAAAA,EAAAA,KAAAA,MAA0BxD,CAA1B;UACF,KAAK0G,kBAAkB;aACpBA,iBAAiB7F,KAAK,MAAM,KAAKgG,SAAL,CAAjC;;WAEGoF,SAAS,KAAKpF,SAAL;aACPrD;;;;qCAGO;UACV,KAAKqD,SAAL,MAAoB,MAAM;aACvBqF,WAAWC,aAAa,WAAW,SAAxC;aACKD,WAAWK,UAAU;aACrBN,SAAS;aACT;aACAC,WAAWK,UAAU;aACrBN,SAAS;;;;;;EAtCY/F,UAAAA;ICI1BsG,mBAAAA,SAAAA,aAAAA;;6BACQrG,QAAQC,UAAUqG,MAAM;;sIAC5BtG,QAAQC,QADoB,CAAA;QAG9BsG,UAAUD;QAERT,QAAAA;WAMDW,WAAWpG,SAASC,cAAc,QAAvB;QAEZ3D,OAAOJ,QAAQiK,OAAf,GAAyB;UACrBE,OAAM,CAAA;aACLhM,KAAK8L,SAAS,SAASG,SAAS;aACjCA,OAAJ,IAAeA;OADjB;gBAGUD;;WAGLhM,KAAK8L,SAAS,SAAS7H,OAAO1D,KAAK;UAClC2L,MAAMvG,SAASC,cAAc,QAAvB;UACRuG,YAAY5L;UACZgL,aAAa,SAAStH,KAA1B;YACM8H,SAASL,YAAYQ,GAA3B;KAJF;WAQKlG,cAAL;QAEIvF,KAAK,OAAKsL,UAAU,UAAU,WAAW;UACrCK,eAAe,KAAKN,QAAQ,KAAKO,aAAlB,EAAiCpI;YAChDwH,SAASW,YAAf;KAFF;WAKK1G,WAAWgG,YAAY,OAAKK,QAAjC;;;;;6BAGO3M,GAAG;UACJwD,YAAAA,IAAAA,kBAAAA,UAAAA,aAAAA,OAAAA,eAAAA,kBAAAA,SAAAA,GAAAA,YAAAA,IAAAA,EAAAA,KAAAA,MAA0BxD,CAA1B;UAEF,KAAK0G,kBAAkB;aACpBA,iBAAiB7F,KAAK,MAAM,KAAKgG,SAAL,CAAjC;;aAEKrD;;;;qCAGO;UACV8D,IAAI4F,SAAS,KAAKP,QAAlB,EAA6B,QAAO;WACnCA,SAAS9H,QAAQ,KAAKgC,SAAL;;;;;EAnDKX,UAAAA;ICJzBiH,mBAAAA,SAAAA,aAAAA;;6BACQhH,QAAQC,UAAU;;sIACtBD,QAAQC,QADc,CAAA;QAGtB4F,QAAAA;aAEGI,WAAW;YACZC,SAASL,MAAMoB,QAAQvI,KAA7B;;aAGOwI,SAAS;UACZrB,MAAMtF,kBAAkB;cACpBA,iBAAiB7F,KAAKmL,OAAOA,MAAMnF,SAAN,CAAnC;;;WAICuG,UAAU7G,SAASC,cAAc,OAAvB;WACV4G,QAAQjB,aAAa,QAAQ,MAAlC;QAEI9K,KAAK,OAAK+L,SAAS,SAAShB,QAAhC;QACI/K,KAAK,OAAK+L,SAAS,UAAUhB,QAAjC;QACI/K,KAAK,OAAK+L,SAAS,QAAQC,MAA/B;QACIhM,KAAK,OAAK+L,SAAS,WAAW,SAASnG,GAAG;UACxCA,EAAE8C,YAAY,IAAI;aACfuD,KAAL;;KAFJ;WAMK1G,cAAL;WAEKN,WAAWgG,YAAY,OAAKc,OAAjC;;;;;qCAGc;UAGV,CAAC9F,IAAI4F,SAAS,KAAKE,OAAlB,GAA4B;aAC1BA,QAAQvI,QAAQ,KAAKgC,SAAL;;;;;;EArCIX,UAAAA;ACR/B,SAASqH,YAAYxE,GAAG;MAChByE,KAAKzE,EAAEzJ,SAAF;MACPkO,GAAG5C,QAAQ,GAAX,IAAkB,IAAI;WACjB4C,GAAGrN,SAASqN,GAAG5C,QAAQ,GAAX,IAAkB;;SAGhC;;IAeH6C,mBAAAA,SAAAA,aAAAA;;6BACQtH,QAAQC,UAAUsC,QAAQ;;qIAC9BvC,QAAQC,QADsB,CAAA;QAG9BsH,UAAUhF,UAAU,CAAA;UAErBiF,QAAQD,QAAQnJ;UAChBqJ,QAAQF,QAAQlJ;UAChBqJ,SAASH,QAAQI;QAElBjL,OAAOzB,YAAY,MAAKyM,MAAxB,GAAiC;UAC/B,MAAKxH,iBAAiB,GAAG;cACtB0H,gBAAgB;aAChB;cAEAA,gBAAgBvO,KAAKwO,IAAI,IAAIxO,KAAKyE,MAAMzE,KAAKyO,IAAIzO,KAAK0O,IAAI,MAAK7H,YAAd,CAAT,IAAwC7G,KAAK2O,IAAxD,CAAb,IAA8E;;WAEhG;YACAJ,gBAAgB,MAAKF;;UAGvBO,cAAcb,YAAY,MAAKQ,aAAjB;;;;;6BAGZ/N,GAAG;UACNqO,KAAKrO;UAEL,KAAK2N,UAAUpL,UAAa8L,KAAK,KAAKV,OAAO;aAC1C,KAAKA;iBACD,KAAKC,UAAUrL,UAAa8L,KAAK,KAAKT,OAAO;aACjD,KAAKA;;UAGR,KAAKC,WAAWtL,UAAa8L,KAAK,KAAKR,WAAW,GAAG;aAClDrO,KAAKC,MAAM4O,KAAK,KAAKR,MAArB,IAA+B,KAAKA;;2IAGrBQ,EAAtB;;;;wBAUEC,UAAU;WACPX,QAAQW;aACN;;;;wBAULC,UAAU;WACPX,QAAQW;aACN;;;;yBAaJC,WAAW;WACTX,SAASW;WACTT,gBAAgBS;WAChBJ,cAAcb,YAAYiB,SAAZ;aACZ;;;;EA9EoBtI,UAAAA;ACpB/B,SAASuI,eAAe5J,OAAO6J,UAAU;MACjCC,QAAQnP,KAAKwO,IAAI,IAAIU,QAAb;SACPlP,KAAKC,MAAMoF,QAAQ8J,KAAnB,IAA4BA;;IAiB/BC,sBAAAA,SAAAA,mBAAAA;;gCACQzI,QAAQC,UAAUsC,QAAQ;;4IAC9BvC,QAAQC,UAAUsC,MADY,CAAA;WAG/BmG,wBAAwB;QAEvB7C,QAAAA;QAMF8C,QAAAA;aAEK1C,WAAW;UACZ2C,YAAY3L,WAAW4I,MAAMoB,QAAQvI,KAAzB;UACd,CAAChC,OAAOL,MAAMuM,SAAb,GAAyB;cACtB1C,SAAS0C,SAAf;;;aAIKC,WAAW;UACdhD,MAAMtF,kBAAkB;cACpBA,iBAAiB7F,KAAKmL,OAAOA,MAAMnF,SAAN,CAAnC;;;aAIKwG,SAAS;;;aAIT4B,YAAYhI,GAAG;UAChBiI,OAAOJ,QAAQ7H,EAAE+B;YACjBqD,SAASL,MAAMnF,SAAN,IAAmBqI,OAAOlD,MAAM+B,aAA/C;cAEQ9G,EAAE+B;;aAGHmG,YAAY;UACfC,OAAO/F,QAAQ,aAAa4F,WAAhC;UACIG,OAAO/F,QAAQ,WAAW8F,SAA9B;;;aAIOE,YAAYpI,GAAG;UAClB5F,KAAKgI,QAAQ,aAAa4F,WAA9B;UACI5N,KAAKgI,QAAQ,WAAW8F,SAA5B;cACQlI,EAAE+B;;WAGPoE,UAAU7G,SAASC,cAAc,OAAvB;WACV4G,QAAQjB,aAAa,QAAQ,MAAlC;QAII9K,KAAK,OAAK+L,SAAS,UAAUhB,QAAjC;QACI/K,KAAK,OAAK+L,SAAS,QAAQC,MAA/B;QACIhM,KAAK,OAAK+L,SAAS,aAAaiC,WAApC;QACIhO,KAAK,OAAK+L,SAAS,WAAW,SAASnG,GAAG;UAExCA,EAAE8C,YAAY,IAAI;cACd8E,wBAAwB;aACzBvB,KAAL;cACMuB,wBAAwB;;;KALlC;WAUKjI,cAAL;WAEKN,WAAWgG,YAAY,OAAKc,OAAjC;;;;;qCAGc;WACTA,QAAQvI,QAAQ,KAAKgK,wBAAwB,KAAKhI,SAAL,IAAkB4H,eAAe,KAAK5H,SAAL,GAAiB,KAAKuH,WAArC;;;;;EA1EtCX,gBAAAA;ACpBlC,SAASb,IAAI5M,GAAGsP,IAAIC,IAAIC,IAAIC,IAAI;SACvBD,MAAMC,KAAKD,QAAQxP,IAAIsP,OAAOC,KAAKD;;IAmBtCI,yBAAAA,SAAAA,mBAAAA;;mCACQvJ,QAAQC,UAAU7B,KAAKC,KAAKsJ,MAAM;;kJACtC3H,QAAQC,UAAU,EAAE7B,KAAUC,KAAUsJ,KAAtB,CADoB,CAAA;QAGtC9B,QAAAA;WAED2D,eAAepJ,SAASC,cAAc,KAAvB;WACfoJ,eAAerJ,SAASC,cAAc,KAAvB;QAEhBnF,KAAK,OAAKsO,cAAc,aAAaN,WAAzC;QACIhO,KAAK,OAAKsO,cAAc,cAAcE,YAA1C;QAEIC,SAAS,OAAKH,cAAc,QAAhC;QACIG,SAAS,OAAKF,cAAc,WAAhC;aAESP,YAAYpI,GAAG;eACb2E,cAAc0B,KAAvB;UAEIjM,KAAKgI,QAAQ,aAAa4F,WAA9B;UACI5N,KAAKgI,QAAQ,WAAW8F,SAA5B;kBAEYlI,CAAZ;;aAGOgI,YAAYhI,GAAG;QACpB8I,eAAF;UAEMC,SAAShE,MAAM2D,aAAaM,sBAAnB;YAET5D,SACJO,IAAI3F,EAAE6B,SAASkH,OAAO7H,MAAM6H,OAAO5H,OAAO4D,MAAM2B,OAAO3B,MAAM4B,KAA7D,CADF;aAIO;;aAGAuB,YAAY;UACfC,OAAO/F,QAAQ,aAAa4F,WAAhC;UACIG,OAAO/F,QAAQ,WAAW8F,SAA9B;UACInD,MAAMtF,kBAAkB;cACpBA,iBAAiB7F,KAAKmL,OAAOA,MAAMnF,SAAN,CAAnC;;;aAIKgJ,aAAa5I,GAAG;UACnBA,EAAEiJ,QAAQ/P,WAAW,GAAG;;;UACxBkB,KAAKgI,QAAQ,aAAa8G,WAA9B;UACI9O,KAAKgI,QAAQ,YAAY+G,UAA7B;kBACYnJ,CAAZ;;aAGOkJ,YAAYlJ,GAAG;UAChB6B,UAAU7B,EAAEiJ,QAAQ,CAAV,EAAapH;UACvBkH,SAAShE,MAAM2D,aAAaM,sBAAnB;YAET5D,SACJO,IAAI9D,SAASkH,OAAO7H,MAAM6H,OAAO5H,OAAO4D,MAAM2B,OAAO3B,MAAM4B,KAA3D,CADF;;aAKOwC,aAAa;UAChBhB,OAAO/F,QAAQ,aAAa8G,WAAhC;UACIf,OAAO/F,QAAQ,YAAY+G,UAA/B;UACIpE,MAAMtF,kBAAkB;cACpBA,iBAAiB7F,KAAKmL,OAAOA,MAAMnF,SAAN,CAAnC;;;WAICD,cAAL;WAEK+I,aAAarD,YAAY,OAAKsD,YAAnC;WACKtJ,WAAWgG,YAAY,OAAKqD,YAAjC;;;;;qCAGc;UACRU,OAAO,KAAKxJ,SAAL,IAAkB,KAAK8G,UAAU,KAAKC,QAAQ,KAAKD;WAC3DiC,aAAanI,MAAM4D,QAAQgF,MAAM,MAAM;;;;;EA5EX5C,gBAAAA;ICZ/B6C,qBAAAA,SAAAA,aAAAA;;+BACQnK,QAAQC,UAAUmK,MAAM;;0IAC5BpK,QAAQC,QADoB,CAAA;QAG5B4F,QAAAA;WAEDwE,WAAWjK,SAASC,cAAc,KAAvB;WACXgK,SAASzD,YAAYwD,SAAShO,SAAY,SAASgO;QAEpDlP,KAAK,OAAKmP,UAAU,SAAS,SAASvJ,GAAG;QACzC8I,eAAF;YACMU,KAAN;aACO;KAHT;QAMIX,SAAS,OAAKU,UAAU,QAA5B;WAEKlK,WAAWgG,YAAY,OAAKkE,QAAjC;;;;;2BAGK;UACD,KAAK/J,YAAY;aACdA,WAAW5F,KAAK,IAArB;;WAEGgG,SAAL,EAAgBhG,KAAK,KAAKsF,MAA1B;UACI,KAAKO,kBAAkB;aACpBA,iBAAiB7F,KAAK,MAAM,KAAKgG,SAAL,CAAjC;;;;;EA1B2BX,UAAAA;ICA3BwK,kBAAAA,SAAAA,aAAAA;;4BACQvK,QAAQC,UAAU;;oIACtBD,QAAQC,QADc,CAAA;WAGvBuK,UAAU,IAAI7L,MAAM,OAAK+B,SAAL,CAAV;WACV+J,SAAS,IAAI9L,MAAM,CAAV;QAERkH,QAAAA;WAED1F,aAAaC,SAASC,cAAc,KAAvB;QAEdqK,eAAe,OAAKvK,YAAY,KAApC;WAEKwK,aAAavK,SAASC,cAAc,KAAvB;WACbsK,WAAWnI,YAAY;WAEvBoI,qBAAqBxK,SAASC,cAAc,KAAvB;WACrBuK,mBAAmBpI,YAAY;WAE/BqI,eAAezK,SAASC,cAAc,KAAvB;WACfwK,aAAarI,YAAY;WACzBsI,sBAAsB;WAEtBC,aAAa3K,SAASC,cAAc,KAAvB;WACb0K,WAAWvI,YAAY;WAEvBwI,cAAc5K,SAASC,cAAc,KAAvB;WACd2K,YAAYxI,YAAY;WAExByE,UAAU7G,SAASC,cAAc,OAAvB;WACV4G,QAAQvB,OAAO;WACfuF,qBAAqB;QAEtB/P,KAAK,OAAK+L,SAAS,WAAW,SAASnG,GAAG;UACxCA,EAAE8C,YAAY,IAAI;eACblJ,KAAK,IAAZ;;KAFJ;QAMIQ,KAAK,OAAK+L,SAAS,QAAQC,MAA/B;QAEIhM,KAAK,OAAKyP,YAAY,aAAa,WAAkB;UAEpDhB,SAAS,MAAM,MADlB,EAEGzO,KAAKgI,QAAQ,WAAW,WAAkB;YACrCgI,YAAYrF,MAAM8E,YAAY,MAAlC;OAHJ;KADF;QAQIzP,KAAK,OAAKyP,YAAY,cAAc,WAAkB;UAErDhB,SAAS,MAAM,MADlB,EAEGzO,KAAKgI,QAAQ,YAAY,WAAkB;YACtCgI,YAAYrF,MAAM8E,YAAY,MAAlC;OAHJ;KADF;QAQMQ,aAAa/K,SAASC,cAAc,KAAvB;WAEZX,OAAO,OAAKiL,WAAWrJ,OAAO;aAC5B;cACC;eACC;uBACQ;iBACN;KALb;WAQO5B,OAAO,OAAKmL,aAAavJ,OAAO;gBAC3B;aACH;cACC;cACA,OAAKwJ,uBAAuB,OAAKN,QAAQ3Q,IAAI,MAAM,SAAS;iBACzD;oBACG;cACN;KAPV;WAUO6F,OAAO,OAAKqL,WAAWzJ,OAAO;gBACzB;aACH;cACC;mBACK;cACL;KALV;WAQO5B,OAAO,OAAKkL,mBAAmBtJ,OAAO;aACpC;cACC;cACA;mBACK;eACJ;cACD;KANV;WASO5B,OAAOyL,WAAW7J,OAAO;aACvB;cACC;kBACI;KAHd;mBAMe6J,YAAY,OAAO,iBAAiB,MAAnD;WAEOzL,OAAO,OAAKsL,YAAY1J,OAAO;aAC7B;cACC;cACA;cACA;gBACE;WACL;aACE;KAPT;gBAUY,OAAK0J,WAAjB;WAEOtL,OAAO,OAAKuH,QAAQ3F,OAAO;eACvB;iBAEE;aAGJ;cACC;kBACI;kBACA,OAAK2J,qBAAqB;KATxC;QAYI/P,KAAK,OAAK0P,oBAAoB,aAAaQ,SAA/C;QACIlQ,KAAK,OAAK0P,oBAAoB,cAAcQ,SAAhD;QAEIlQ,KAAK,OAAK2P,cAAc,aAAaO,SAAzC;QACIlQ,KAAK,OAAK2P,cAAc,cAAcO,SAA1C;QAEIlQ,KAAK,OAAK8P,aAAa,aAAaK,UAAxC;QACInQ,KAAK,OAAK8P,aAAa,cAAcK,UAAzC;aAESD,UAAUtK,GAAG;YACdA,CAAN;UACI5F,KAAKgI,QAAQ,aAAaoI,KAA9B;UACIpQ,KAAKgI,QAAQ,aAAaoI,KAA9B;UACIpQ,KAAKgI,QAAQ,WAAWqI,SAA5B;UACIrQ,KAAKgI,QAAQ,YAAYqI,SAA7B;;aAGOF,WAAWvK,GAAG;WAChBA,CAAL;UACI5F,KAAKgI,QAAQ,aAAasI,IAA9B;UACItQ,KAAKgI,QAAQ,aAAasI,IAA9B;UACItQ,KAAKgI,QAAQ,WAAWuI,QAA5B;UACIvQ,KAAKgI,QAAQ,YAAYuI,QAA7B;;aAGOF,YAAY;UACftC,OAAO/F,QAAQ,aAAaoI,KAAhC;UACIrC,OAAO/F,QAAQ,aAAaoI,KAAhC;UACIrC,OAAO/F,QAAQ,WAAWqI,SAA9B;UACItC,OAAO/F,QAAQ,YAAYqI,SAA/B;;;aAIOE,WAAW;UACdxC,OAAO/F,QAAQ,aAAasI,IAAhC;UACIvC,OAAO/F,QAAQ,aAAasI,IAAhC;UACIvC,OAAO/F,QAAQ,WAAWuI,QAA9B;UACIxC,OAAO/F,QAAQ,YAAYuI,QAA/B;;;aAIOvE,SAAS;UACV7L,IAAIiC,UAAU,KAAKoB,KAAf;UACNrD,MAAM,OAAO;cACTmP,QAAQvR,UAAUoC;cAClB6K,SAASL,MAAM2E,QAAQkB,WAAd,CAAf;aACK;aACAhN,QAAQmH,MAAM2E,QAAQrR,SAAd;;;aAIR0P,WAAW;UACdhD,MAAMtF,kBAAkB;cACpBA,iBAAiB7F,KAAKmL,OAAOA,MAAM2E,QAAQkB,WAAd,CAAnC;;;WAICd,mBAAmBzE,YAAYgF,UAApC;WACKR,WAAWxE,YAAY,OAAK0E,YAAjC;WACKF,WAAWxE,YAAY,OAAKyE,kBAAjC;WACKD,WAAWxE,YAAY,OAAK6E,WAAjC;WACKA,YAAY7E,YAAY,OAAK4E,UAAlC;WAEK5K,WAAWgG,YAAY,OAAKc,OAAjC;WACK9G,WAAWgG,YAAY,OAAKwE,UAAjC;WAEKlK,cAAL;aAES6K,MAAMxK,GAAG;UACZA,EAAE4E,KAAKjB,QAAQ,OAAf,MAA4B,IAAI;UAAImF,eAAF;;UAEhC+B,YAAY9F,MAAM+E,mBAAmBd,sBAAzB;iBACYhJ,EAAEiJ,WAAWjJ,EAAEiJ,QAAQ,CAAV,KAAiBjJ,GAApD6B,UAJQ,KAIRA,SAASE,UAJD,KAICA;UACblJ,KAAKgJ,UAAUgJ,UAAU3J,SAAS2J,UAAU1J,QAAQ0J,UAAU3J;UAC9DnI,IAAI,KAAKgJ,UAAU8I,UAAUzJ,QAAQyJ,UAAUxJ,SAASwJ,UAAUzJ;UAElErI,IAAI,GAAG;YACL;iBACKA,IAAI,GAAG;YACZ;;UAGFF,IAAI,GAAG;YACL;iBACKA,IAAI,GAAG;YACZ;;YAGA6Q,QAAQ3Q,IAAIA;YACZ2Q,QAAQ7Q,IAAIA;YAEZuM,SAASL,MAAM2E,QAAQkB,WAAd,CAAf;aAGO;;aAGAF,KAAK1K,GAAG;UACXA,EAAE4E,KAAKjB,QAAQ,OAAf,MAA4B,IAAI;UAAImF,eAAF;;UAEhC+B,YAAY9F,MAAMmF,YAAYlB,sBAAlB;kBACGhJ,EAAEiJ,WAAWjJ,EAAEiJ,QAAQ,CAAV,KAAiBjJ,GAA3C+B,UAJO,MAIPA;UACJnJ,IAAI,KAAKmJ,UAAU8I,UAAUzJ,QAAQyJ,UAAUxJ,SAASwJ,UAAUzJ;UAElExI,IAAI,GAAG;YACL;iBACKA,IAAI,GAAG;YACZ;;YAGA8Q,QAAQ9Q,IAAIA,IAAI;YAEhBwM,SAASL,MAAM2E,QAAQkB,WAAd,CAAf;aAEO;;;;;;qCAIK;UACRrQ,IAAIiC,UAAU,KAAKoD,SAAL,CAAV;UAENrF,MAAM,OAAO;YACXuQ,WAAW;eAIRnR,KAAKkE,MAAMkB,YAAY,SAASd,WAAW;cAC5C,CAACrC,OAAOzB,YAAYI,EAAE0D,SAAF,CAAnB,KAAoC,CAACrC,OAAOzB,YAAY,KAAKuP,QAAQvR,QAAQ8F,SAArB,CAAnB,KACxC1D,EAAE0D,SAAF,MAAiB,KAAKyL,QAAQvR,QAAQ8F,SAArB,GAAiC;uBACvC;mBACJ,CAAA;;WAER,IANH;YAUI6M,UAAU;iBACLlM,OAAO,KAAK8K,QAAQvR,SAASoC,CAApC;;;aAIGqE,OAAO,KAAK+K,OAAOxR,SAAS,KAAKuR,QAAQvR,OAAhD;WAEKwR,OAAOhR,IAAI;UAEVoS,OAAQ,KAAKrB,QAAQ3Q,IAAI,OAAO,KAAK2Q,QAAQ7Q,IAAI,MAAO,MAAM;UAC9DmS,QAAQ,MAAMD;aAEbnM,OAAO,KAAKmL,aAAavJ,OAAO;oBACzB,MAAM,KAAKkJ,QAAQ7Q,IAAI,IAAI;mBAC5B,OAAO,IAAI,KAAK6Q,QAAQ3Q,KAAK,IAAI;yBAC3B,KAAK4Q,OAAOsB,YAAZ;gBACT,KAAKjB,sBAAsB,SAASe,OAAO,MAAMA,OAAO,MAAMA,OAAO;OAJ/E;WAOKd,WAAWzJ,MAAM0K,aAAa,IAAI,KAAKxB,QAAQ9Q,IAAI,OAAO,MAAM;WAEhE+Q,OAAO9Q,IAAI;WACX8Q,OAAO5Q,IAAI;qBAED,KAAK+Q,oBAAoB,QAAQ,QAAQ,KAAKH,OAAOsB,YAAZ,CAAxD;WAEK9E,QAAQvI,QAAQ,KAAK8L,QAAQrR,SAAb;aAEduG,OAAO,KAAKuH,QAAQ3F,OAAO;yBACf,KAAKkJ,QAAQuB,YAAb;eACV,SAASF,OAAO,MAAMA,OAAO,MAAMA,OAAO;oBACrC,KAAKZ,qBAAqB,UAAUa,QAAQ,MAAMA,QAAQ,MAAMA,QAAQ;OAHtF;;;;EAlS0B/L,UAAAA;AA0S9B,IAAMkM,UAAU,CAAC,SAAS,OAAO,YAAY,QAAQ,EAArC;AAEhB,SAASC,eAAe9K,MAAMwB,GAAGnJ,GAAGD,GAAG;OAChC8H,MAAM6K,aAAa;SACjB1R,KAAKwR,SAAS,SAASG,QAAQ;SAC/B9K,MAAM+K,WAAW,iBAAiBD,SAAS,qBAAqBxJ,IAAI,OAAOnJ,IAAI,UAAUD,IAAI;GADpG;;AAKF,SAAS8S,YAAYlL,MAAM;OACpBE,MAAM6K,aAAa;OACnB7K,MAAM+K,WAAW;OACjB/K,MAAM+K,WAAW;OACjB/K,MAAM+K,WAAW;OACjB/K,MAAM+K,WAAW;OACjB/K,MAAM+K,WAAW;;ACpUxB,IAAME,MAAM;QACJ,SAAA,KAASC,KAAKC,OAAO;QACnBC,MAAMD,SAASrM;QACfuM,OAAOD,IAAIrM,cAAc,MAAlB;SACRqF,OAAO;SACPkH,MAAM;SACNjH,OAAO6G;QACRK,qBAAqB,MAAzB,EAAiC,CAAjC,EAAoC1G,YAAYwG,IAAhD;;UAGM,SAAA,OAASG,YAAYL,OAAO;QAC5BC,MAAMD,SAASrM;QACf2M,WAAW3M,SAASC,cAAc,OAAvB;aACRqF,OAAO;aACPkB,YAAYkG;QACfE,OAAON,IAAIG,qBAAqB,MAAzB,EAAiC,CAAjC;QACT;WACG1G,YAAY4G,QAAjB;aACOjM,GAAG;;;;AC/BhB,IAAMmM,qBAAAA;;;;;;;;;;;;;;;;;;;;;ACqBN,IAAMC,oBAAoB,SAApBA,mBAA6BlN,QAAQC,UAAU;MAC7CC,eAAeF,OAAOC,QAAP;MAGjBvD,OAAOJ,QAAQ3B,UAAU,CAAV,CAAf,KAAgC+B,OAAO5B,SAASH,UAAU,CAAV,CAAhB,GAA+B;WAC1D,IAAI0L,iBAAiBrG,QAAQC,UAAUtF,UAAU,CAAV,CAAvC;;MAIL+B,OAAOQ,SAASgD,YAAhB,GAA+B;QAE7BxD,OAAOQ,SAASvC,UAAU,CAAV,CAAhB,KAAiC+B,OAAOQ,SAASvC,UAAU,CAAV,CAAhB,GAA+B;UAE9D+B,OAAOQ,SAASvC,UAAU,CAAV,CAAhB,GAA+B;eAC1B,IAAI4O,uBAAuBvJ,QAAQC,UACxCtF,UAAU,CAAV,GAAcA,UAAU,CAAV,GAAcA,UAAU,CAAV,CADvB;;aAIF,IAAI4O,uBAAuBvJ,QAAQC,UAAUtF,UAAU,CAAV,GAAcA,UAAU,CAAV,CAA3D;;QAIL+B,OAAOQ,SAASvC,UAAU,CAAV,CAAhB,GAA+B;aAC1B,IAAI8N,oBAAoBzI,QAAQC,UACrC,EAAE7B,KAAKzD,UAAU,CAAV,GAAc0D,KAAK1D,UAAU,CAAV,GAAcgN,MAAMhN,UAAU,CAAV,EAA9C,CADK;;WAGF,IAAI8N,oBAAoBzI,QAAQC,UAAU,EAAE7B,KAAKzD,UAAU,CAAV,GAAc0D,KAAK1D,UAAU,CAAV,EAA1B,CAA1C;;MAGL+B,OAAOC,SAASuD,YAAhB,GAA+B;WAC1B,IAAI8G,iBAAiBhH,QAAQC,QAA7B;;MAGLvD,OAAOyQ,WAAWjN,YAAlB,GAAiC;WAC5B,IAAIiK,mBAAmBnK,QAAQC,UAAU,EAAzC;;MAGLvD,OAAO0Q,UAAUlN,YAAjB,GAAgC;WAC3B,IAAI0F,kBAAkB5F,QAAQC,QAA9B;;SAGF;;ACjDT,SAASoN,sBAAsBC,UAAU;aAC5BA,UAAU,MAAO,EAA5B;;AAGF,IAAA,0BAAepK,OAAOmK,yBAClBnK,OAAOqK,+BACPrK,OAAOsK,4BACPtK,OAAOuK,0BACPvK,OAAOwK,2BACPL;ICNEM,cAAAA,WAAAA;0BACU;;SACPC,oBAAoBxN,SAASC,cAAc,KAAvB;WAClBX,OAAO,KAAKkO,kBAAkBtM,OAAO;uBACzB;WACZ;YACC;eACG;cACD;eACC;wBACS;kBACN;KARd;QAWIuM,eAAe,KAAKD,iBAAxB;SACKA,kBAAkBtM,MAAMS,WAAW;SAEnC5B,aAAaC,SAASC,cAAc,KAAvB;WACXX,OAAO,KAAKS,WAAWmB,OAAO;gBACzB;eACD;cACD;eACC;wBACS;kBACN;KANd;aAUSwM,KAAK3H,YAAY,KAAKyH,iBAA/B;aACSE,KAAK3H,YAAY,KAAKhG,UAA/B;QAEM0F,QAAQ;QACV3K,KAAK,KAAK0S,mBAAmB,SAAS,WAAW;YAC7CG,KAAN;KADF;;;;4BAKK;UACClI,QAAQ;WAET+H,kBAAkBtM,MAAM0M,UAAU;WAElC7N,WAAWmB,MAAM0M,UAAU;WAC3B7N,WAAWmB,MAAM2M,UAAU;WAE3B9N,WAAWmB,MAAM4M,kBAAkB;WAEnCC,OAAL;aAEOC,MAAM,WAAW;cAChBR,kBAAkBtM,MAAM2M,UAAU;cAClC9N,WAAWmB,MAAM2M,UAAU;cAC3B9N,WAAWmB,MAAM4M,kBAAkB;OAH3C;;;;4BAUK;UACCrI,QAAQ;UAERkI,QAAO,SAAPA,QAAkB;cAChB5N,WAAWmB,MAAM0M,UAAU;cAC3BJ,kBAAkBtM,MAAM0M,UAAU;YAEpC/E,OAAOpD,MAAM1F,YAAY,uBAAuB4N,KAApD;YACI9E,OAAOpD,MAAM1F,YAAY,iBAAiB4N,KAA9C;YACI9E,OAAOpD,MAAM1F,YAAY,kBAAkB4N,KAA/C;;UAGE7S,KAAK,KAAKiF,YAAY,uBAAuB4N,KAAjD;UACI7S,KAAK,KAAKiF,YAAY,iBAAiB4N,KAA3C;UACI7S,KAAK,KAAKiF,YAAY,kBAAkB4N,KAA5C;WAEKH,kBAAkBtM,MAAM2M,UAAU;WAElC9N,WAAWmB,MAAM2M,UAAU;WAC3B9N,WAAWmB,MAAM4M,kBAAkB;;;;6BAGjC;WACF/N,WAAWmB,MAAMU,OAAOkB,OAAOmL,aAAa,IAAIlN,IAAImN,SAAS,KAAKnO,UAAlB,IAAgC,IAAI;WACpFA,WAAWmB,MAAMY,MAAMgB,OAAOqL,cAAc,IAAIpN,IAAIqN,UAAU,KAAKrO,UAAnB,IAAiC,IAAI;;;;;;ACtE9FoM,IAAIkC,OAAOC,UAAX;AAGA,IAAMC,gBAAgB;AAEtB,IAAMC,gBAAgB;AAGtB,IAAMC,sBAAsB;AAE5B,IAAMC,8BAA8B;AAEpC,IAAMC,yBAA0B,WAAW;MACrC;WACK,CAAC,CAAC7L,OAAO8L;WACTlO,GAAG;WACH;;EAJqB;AAQhC,IAAImO,gBAAAA;AAGJ,IAAIC,kBAAkB;AAGtB,IAAIC,qBAAAA;AAGJ,IAAIpB,OAAO;AAGX,IAAMqB,eAAe,CAAA;AA2BrB,IAAMC,MAAM,SAANA,KAAehN,MAAM;MACnBwD,QAAQ;MAEVtD,SAASF,QAAQ,CAAA;OAMhBlC,aAAaC,SAASC,cAAc,KAAvB;OACbiP,OAAOlP,SAASC,cAAc,IAAvB;OACPF,WAAWgG,YAAY,KAAKmJ,IAAjC;MAEI3F,SAAS,KAAKxJ,YAAYwO,aAA9B;OAMKY,YAAY,CAAA;OAEZC,gBAAgB,CAAA;OAMhBC,sBAAsB,CAAA;OAoBtBC,yCAAyC,CAAA;OAEzCC,cAAc,CAAA;WAGVjT,OAAO6G,SAAShB,QAAQ;gBACnB;eACD;WACJ8M,KAAIO;GAHJ;WAMAlT,OAAO6G,SAAShB,QAAQ;eACpBA,OAAOsN;cACRtN,OAAOsN;GAFV;MAKL,CAACnT,OAAOzB,YAAYsH,OAAOuN,IAA1B,GAAiC;QAEhCvN,OAAOwN,QAAQ;aACVD,KAAKC,SAASxN,OAAOwN;;SAEzB;WACED,OAAO,EAAEC,QAAQjB,4BAAV;;MAGZpS,OAAOzB,YAAYsH,OAAOyN,MAA1B,KAAqCzN,OAAO0N,UAAU;iBAC3CvL,KAAK,IAAlB;;SAIKwL,YAAYxT,OAAOzB,YAAYsH,OAAOyN,MAA1B,KAAqCzN,OAAO2N;MAE3D3N,OAAOsN,aAAanT,OAAOzB,YAAYsH,OAAO4N,UAA1B,GAAuC;WACtDA,aAAa;;MAMlBC,kBACFrB,0BACAC,aAAaqB,QAAQC,oBAAoB,MAAM,SAA1B,CAArB,MAA+D;MAE7DC,qBAAAA;MACAC,WAAAA;SAEGC;IAAiB;;cAOZ;aACD,SAAAvR,UAAW;iBACPqD,OAAOyN;;;kBAIN;aACL,SAAA9Q,UAAW;iBACPqD,OAAO4N;;;iBAQP;aACJ,SAAAjR,UAAW;iBACPqD,OAAOsN;;;kBAQN;aACL,SAAA3Q,UAAW;iBACPqD,OAAOmO;;;cAQV;aACD,SAAAxR,UAAW;cACV2G,MAAMmK,QAAQ;mBACTnK,MAAM8K,QAAN,EAAgBZ;;iBAGlBxN,OAAOuN,KAAKC;;aAGhB,SAAA1Q,QAASxF,GAAG;cACXgM,MAAMmK,QAAQ;kBACVW,QAAN,EAAgBZ,SAASlW;iBACpB;mBACEiW,KAAKC,SAASlW;;+BAEF,IAArB;gBACM+W,OAAN;;;aAQG;aACA,SAAA1R,UAAW;iBACPqD,OAAO2C;;aAEX,SAAA7F,QAASxF,GAAG;iBACRqL,QAAQrL;mBACNgM,OAAOhM,CAAhB;;;YASE;aACC,SAAAqF,UAAW;iBACPqD,OAAOsO;;aAEX,SAAAxR,QAASxF,GAAG;iBAERgX,OAAOhX;cACV2W,UAAU;qBACH5J,YAAYrE,OAAOsO;;;;cAS1B;aACD,SAAA3R,UAAW;iBACPqD,OAAOuO;;aAEX,SAAAzR,QAASxF,GAAG;iBACRiX,SAASjX;cACZ0I,OAAOuO,QAAQ;gBACbnH,SAAS9D,MAAMyJ,MAAMD,KAAI0B,YAA7B;iBACK;gBACD7F,YAAYrF,MAAMyJ,MAAMD,KAAI0B,YAAhC;;eAKGC,SAAL;cAEInL,MAAMoL,eAAe;kBACjBA,cAAcrK,YAAY/M,IAAIwV,KAAI6B,YAAY7B,KAAI8B;;;;YASxD;aACC,SAAAjS,UAAW;iBACPqD,OAAOuN;;;uBASD;aAEV,SAAA5Q,UAAW;iBACPkR;;aAEJ,SAAA/Q,QAAS6E,MAAM;cACd6K,wBAAwB;8BACR7K;gBACdA,MAAM;kBACJhJ,KAAKgI,QAAQ,UAAUqN,kBAA3B;mBACK;kBACDtH,OAAO/F,QAAQ,UAAUqN,kBAA7B;;yBAEWa,QAAQd,oBAAoBzK,OAAO,SAA3B,GAAuC3B,IAA5D;;;;;EArJV;MA4JIxH,OAAOzB,YAAYsH,OAAOyN,MAA1B,GAAmC;SAChCc,SAASvO,OAAOuO,UAAU;QAE3BnH,SAAS,KAAKxJ,YAAYkP,KAAIgC,UAAlC;QACI3G,eAAe,KAAKvK,YAAY,KAApC;QAGI4O,wBAAwB;UACtBqB,iBAAiB;cACbA,kBAAkB;YAElBkB,WAAWtC,aAAaqB,QAAQC,oBAAoB,MAAM,KAA1B,CAArB;YAEbgB,UAAU;iBACLxB,OAAOyB,KAAKC,MAAMF,QAAX;;;;SAKfL,gBAAgB7Q,SAASC,cAAc,KAAvB;SAChB4Q,cAAcrK,YAAYyI,KAAI8B;QAC/BxH,SAAS,KAAKsH,eAAe5B,KAAIoC,kBAArC;QACIlP,OAAOmO,YAAY;UACjB/G,SAAS,KAAKsH,eAAe5B,KAAIqC,eAArC;WACKvR,WAAWwR,aAAa,KAAKV,eAAe,KAAK9Q,WAAWyR,WAAW,CAA3B,CAAjD;WACK;UACDjI,SAAS,KAAKsH,eAAe5B,KAAIwC,kBAArC;WACK1R,WAAWgG,YAAY,KAAK8K,aAAjC;;QAGE/V,KAAK,KAAK+V,eAAe,SAAS,WAAW;YACzCH,SAAS,CAACjL,MAAMiL;KADxB;SAIK;QACDvO,OAAOuO,WAAW1U,QAAW;aACxB0U,SAAS;;QAGZgB,eAAe1R,SAAS2R,eAAexP,OAAOsO,IAA/B;QACjBlH,SAASmI,cAAc,iBAA3B;eAEWE,OAAOnM,OAAOiM,YAAd;QAELG,eAAe,SAAfA,cAAwBnR,GAAG;QAC7B8I,eAAF;YACMkH,SAAS,CAACjL,MAAMiL;aACf;;QAGLnH,SAAS,KAAK2F,MAAMD,KAAI0B,YAA5B;QAEIpH,SAAS6G,UAAU,OAAvB;QACItV,KAAKsV,UAAU,SAASyB,YAA5B;QAEI,CAAC1P,OAAOuO,QAAQ;WACbA,SAAS;;;MAIdvO,OAAOsN,WAAW;QAChBnT,OAAOzB,YAAYsH,OAAOyN,MAA1B,GAAmC;UACjCd,iBAAiB;6BACE9O,SAASC,cAAc,KAAvB;YACjBsJ,SAASwF,oBAAoBR,aAAjC;YACIhF,SAASwF,oBAAoBE,KAAI6C,0BAArC;iBACSpE,KAAK3H,YAAYgJ,kBAA1B;0BACkB;;yBAIDhJ,YAAY,KAAKhG,UAApC;UAGIwJ,SAAS,KAAKxJ,YAAYkP,KAAI8C,gBAAlC;;QAKE,CAAC,KAAKnC,QAAQ;eACPnK,OAAOtD,OAAO2C,KAAvB;;;OAICkN,kBAAkB,WAAW;UAC1BC,kBAAN;;MAGEnX,KAAKgI,QAAQ,UAAU,KAAKkP,eAAhC;MACIlX,KAAK,KAAKoU,MAAM,uBAAuB,KAAK8C,eAAhD;MACIlX,KAAK,KAAKoU,MAAM,iBAAiB,KAAK8C,eAA1C;MACIlX,KAAK,KAAKoU,MAAM,kBAAkB,KAAK8C,eAA3C;OACKpB,SAAL;MAEIzO,OAAO2N,WAAW;oBACJ,IAAhB;;uBAGmB,SAAAK,sBAAW;QAC1BxB,0BAA0BC,aAAaqB,QAAQC,oBAAoBzK,OAAO,SAA3B,CAArB,MAAgE,QAAQ;mBACvFuL,QAAQd,oBAAoBzK,OAAO,KAA3B,GAAmC0L,KAAKe,UAAUzM,MAAM0M,cAAN,CAAf,CAAxD;;;OAKCC,+BAA+BjC;WAE3BkC,aAAa;QACdC,OAAO7M,MAAM8K,QAAN;SACRzL,SAAS;WACPkJ,MAAM,WAAW;WACjBlJ,SAAS;KADhB;;MAKE,CAAC3C,OAAOyN,QAAQ;;;;AAKtBX,IAAIsD,aAAa,WAAW;SACnB,CAAC5E;SACDtT,KAAK2U,cAAc,SAASwD,MAAK;SAClCzS,WAAWmB,MAAM0M,UAAUD,OAAO,SAAS;GADjD;;AAKFsB,IAAI8C,mBAAmB;AACvB9C,IAAI6C,6BAA6B;AACjC7C,IAAIgC,aAAa;AACjBhC,IAAIwD,uBAAuB;AAC3BxD,IAAIyD,iBAAiB;AACrBzD,IAAI0B,eAAe;AACnB1B,IAAIoC,qBAAqB;AACzBpC,IAAIqC,kBAAkB;AACtBrC,IAAIwC,qBAAqB;AACzBxC,IAAI0D,aAAa;AAEjB1D,IAAIO,gBAAgB;AACpBP,IAAI8B,cAAc;AAClB9B,IAAI6B,YAAY;AAEhB7B,IAAI2D,kBAAkB,SAASlS,GAAG;MAC5BV,SAASqF,cAAcC,SAAS,WACjC5E,EAAEmS,UAAUrE,iBAAiB9N,EAAE8C,YAAYgL,gBAAgB;QACxD+D,WAAJ;;;AAGJxR,IAAIjG,KAAKgI,QAAQ,WAAWmM,IAAI2D,iBAAiB,KAAjD;AAEAtW,OAAOgD;EACL2P,IAAIlV;EAGJ;SAyBO,SAAA,IAAS6F,QAAQC,UAAU;aACvBiT,KACL,MACAlT,QACAC,UACA;qBACe/F,MAAMC,UAAUG,MAAMI,KAAKC,WAAW,CAAtC;OALV;;cA8BC,SAAA,SAASqF,QAAQC,UAAU;aAC5BiT,KACL,MACAlT,QACAC,UACA;eACS;OALJ;;YAeD,SAAA,OAASkT,YAAY;WAEtB7D,KAAK8D,YAAYD,WAAWE,IAAjC;WACK7D,cAAczK,OAAO,KAAKyK,cAAc/K,QAAQ0O,UAA3B,GAAwC,CAAlE;UACMtN,QAAQ;aACPuI,MAAM,WAAW;cAChB4C,SAAN;OADF;;aAUO,SAAA,UAAW;UACd,KAAKhB,QAAQ;cACT,IAAIpR,MACR,4GADI;;UAMJ,KAAKiR,WAAW;2BACCuD,YAAY,KAAKjT,UAApC;;UAGI0F,QAAQ;aACPpL,KAAK,KAAK8U,WAAW,SAAS+D,WAAW;cACxCC,aAAaD,SAAnB;OADF;UAIIrK,OAAO/F,QAAQ,WAAWmM,IAAI2D,iBAAiB,KAAnD;sBAEgB,IAAhB;;eAWS,SAAA,UAASnC,MAAM;UAGpB,KAAKtB,UAAUsB,IAAf,MAAyBzU,QAAW;cAChC,IAAIwC,MAAM,wDACFiS,OAAO,GADf;;UAIF2C,eAAe,EAAE3C,MAAYb,QAAQ,KAAtB;mBAKRH,YAAY,KAAKA;UAG1B,KAAKC,aACFA,KAAK2D,gBACL3D,KAAK2D,QAAQ5C,IAAlB,GAAyB;qBAEZC,SAAS,KAAKhB,KAAK2D,QAAQ5C,IAAlB,EAAwBC;qBAGjChB,OAAO,KAAKA,KAAK2D,QAAQ5C,IAAlB;;UAGhB+B,OAAM,IAAIvD,IAAImE,YAAR;WACPjE,UAAUsB,IAAf,IAAuB+B;UAEjBc,KAAK1B,OAAO,MAAMY,KAAIzS,UAAjB;UACPwJ,SAAS+J,IAAI,QAAjB;aACOd;;kBAQK,SAAA,aAASe,QAAQ;WACxBrE,KAAK8D,YAAYO,OAAOxT,WAAWyT,aAAxC;aAEO,KAAKrE,UAAUoE,OAAO9C,IAAtB;UAGH,KAAKf,aACFA,KAAK2D,gBACL3D,KAAK2D,QAAQE,OAAO9C,IAAzB,GAAgC;eACzB,KAAKf,KAAK2D,QAAQE,OAAO9C,IAAzB;;sBAGO8C,MAAhB;UAEM9N,QAAQ;aAEPpL,KAAKkZ,OAAOpE,WAAW,SAAS+D,WAAW;eACzCC,aAAaD,SAApB;OADF;aAIOlF,MAAM,WAAW;cAChB4C,SAAN;OADF;;UAQI,SAAA,OAAW;WACVF,SAAS;;WAMT,SAAA,QAAW;WACXA,SAAS;;UAMV,SAAA/C,QAAW;WACV5N,WAAWmB,MAAM0M,UAAU;;UAM5B,SAAA,OAAW;WACV7N,WAAWmB,MAAM0M,UAAU;;cAIxB,SAAA,WAAW;UAEb0E,OAAO,KAAK/B,QAAL;UACT+B,KAAKvC,YAAY;YACbjO,MAAMf,IAAI0S,UAAUnB,KAAKpD,IAAnB,EAAyBpN;YACjCxI,IAAI;eAEDe,KAAKiY,KAAKpD,KAAKsC,YAAY,SAASkC,MAAM;cAC3C,EAAEpB,KAAK7C,aAAaiE,SAASpB,KAAKqB,aAAa;iBAC5C5S,IAAIqN,UAAUsF,IAAd;;SAFT;YAMI5Q,OAAOqL,cAAcrM,MAAM2M,sBAAsBnV,GAAG;cAClDiQ,SAAS+I,KAAKvS,YAAYkP,IAAIyD,cAAlC;eACKxD,KAAKhO,MAAM6D,SAASjC,OAAOqL,cAAcrM,MAAM2M,sBAAsB;eACrE;cACD3D,YAAYwH,KAAKvS,YAAYkP,IAAIyD,cAArC;eACKxD,KAAKhO,MAAM6D,SAAS;;;UAIzBuN,KAAKsB,iBAAiB;eACjB5F,MAAM,WAAW;eACjB4F,gBAAgB1S,MAAM6D,SAASuN,KAAKpD,KAAK2E,eAAe;SAD/D;;UAKEvB,KAAKzB,eAAe;aACjBA,cAAc3P,MAAM4D,QAAQwN,KAAKxN,QAAQ;;;uBAI/BxI,OAAOwX,SAAS,WAAW;WAAOlD,SAAL;OAAoB,EAAjD;cAYT,SAAA,WAAW;UACftU,OAAOzB,YAAYgU,aAAnB,GAAmC;wBACrB,IAAItB,YAAJ;sBACFxN,WAAWyG,YAAYuN;;UAGnC,KAAKnE,QAAQ;cACT,IAAIpR,MAAM,gDAAV;;UAGFiH,QAAQ;aAEPpL,KAAKP,MAAMC,UAAUG,MAAMI,KAAKC,SAA3B,GAAuC,SAASqF,QAAQ;YAC9D6F,MAAM4J,oBAAoBzV,WAAW,GAAG;sBAC9B6L,KAAZ;;YAEEA,MAAM4J,oBAAoBhL,QAAQzE,MAAlC,MAA8C,IAAI;gBAC9CyP,oBAAoB/K,KAAK1E,MAA/B;;OALJ;UASI,KAAK6P,WAAW;iBAET,MAAM,KAAK3K,KAApB;;;aAQK,SAAA,UAAW;UACd0N,OAAM;aACHA,KAAI5C,QAAQ;eACX4C,KAAI5C;;aAEL4C;;mBAQM,SAAA,gBAAW;UAClBvV,YAAW,KAAKyS;gBACbgB,SAAS,KAAKA;UAGnB,KAAKrB,oBAAoBzV,SAAS,GAAG;kBAC9B+V,SAAS,KAAKA;YAEnB,CAAC1S,UAAS+W,YAAY;oBACfA,aAAa,CAAA;;kBAGfA,WAAW,KAAKrE,MAAzB,IAAmCsE,iBAAiB,IAAjB;;gBAG5BZ,UAAU,CAAA;aACZhZ,KAAK,KAAK8U,WAAW,SAAS7I,SAAS1L,KAAK;kBACxCyY,QAAQzY,GAAjB,IAAwB0L,QAAQ6L,cAAR;OAD1B;aAIOlV;;UAGH,SAAA,OAAW;UACX,CAAC,KAAKyS,KAAKsE,YAAY;aACpBtE,KAAKsE,aAAa,CAAA;;WAGpBtE,KAAKsE,WAAW,KAAKrE,MAA1B,IAAoCsE,iBAAiB,IAAjB;yBACjB,MAAM,KAAzB;WACK7B,6BAAL;;YAGM,SAAA,OAAS8B,YAAY;UACvB,CAAC,KAAKxE,KAAKsE,YAAY;aAEpBtE,KAAKsE,aAAa,CAAA;aAClBtE,KAAKsE,WAAWtF,2BAArB,IAAoDuF,iBAAiB,MAAM,IAAvB;;WAGjDvE,KAAKsE,WAAWE,UAArB,IAAmCD,iBAAiB,IAAjB;WAC9BtE,SAASuE;sBACE,MAAMA,YAAY,IAAlC;WACK9B,6BAAL;;YAGM,SAAA,OAASI,MAAK;aACbnY,KAAK,KAAK+U,eAAe,SAAS2D,YAAY;YAE/C,CAAC,KAAKxC,QAAL,EAAeb,KAAKsE,YAAY;qBACxBlO,SAASiN,WAAWjT,YAA/B;eACK;2BACY0S,QAAO,KAAKjC,QAAL,GAAgBwC,UAAxC;;YAIEA,WAAW5S,kBAAkB;qBACpBA,iBAAiB7F,KAAKyY,YAAYA,WAAWzS,SAAX,CAA7C;;SAED,IAZH;aAcOjG,KAAK,KAAK8U,WAAW,SAASoE,QAAQ;eACpC/C,OAAO+C,MAAd;OADF;UAII,CAACf,MAAK;2BACW,KAAKjC,QAAL,GAAgB,KAAnC;;;YAII,SAAA,OAASwC,YAAY;UACrB/P,OAAO,KAAKuM,YAAY3V,WAAW;WACpC2V,YAAYjL,KAAKyO,UAAtB;UACI/P,MAAM;uBACO,KAAKuM,WAApB;;;mBAIW,SAAA,gBAAW;aACjBlV,KAAK,KAAK+U,eAAe,SAAS2D,YAAY;mBACxC1S,cAAX;OADF;aAGOhG,KAAK,KAAK8U,WAAW,SAASoE,QAAQ;eACpClT,cAAP;OADF;;;AAjYN;AAiZA,SAASuR,OAAOY,MAAK2B,QAAQC,UAAU;MAC/Bd,KAAKtT,SAASC,cAAc,IAAvB;MACPkU,QAAQ;OACPpO,YAAYoO,MAAf;;MAGEC,UAAU;SACRlF,KAAKqC,aAAa+B,IAAIc,QAA1B;SACK;SACDlF,KAAKnJ,YAAYuN,EAArB;;OAEE1C,SAAJ;SACO0C;;AAGT,SAASe,gBAAgB7B,MAAK;MACxB3J,OAAO/F,QAAQ,UAAU0P,KAAIR,eAAjC;MAEIQ,KAAIJ,8BAA8B;QAChCvJ,OAAO/F,QAAQ,UAAU0P,KAAIJ,4BAAjC;;;AAIJ,SAASkC,mBAAmB9B,MAAK+B,UAAU;MACnChO,MAAMiM,KAAIgC,gBAAgBhC,KAAIgC,gBAAgB9N,aAAxC;MAER6N,UAAU;QACR/N,YAAYD,IAAIjI,QAAQ;SACvB;QACDkI,YAAYD,IAAIjI;;;AAIxB,SAASmW,kBAAkBjC,MAAKc,IAAIP,YAAY;aACnCE,OAAOK;aACPoB,QAAQlC;SAEZlT,OAAOyT,YAA+C;aAKlD,SAAA,QAAS5M,UAAS;UACrB5L,UAAUX,SAAS,GAAG;YAClB+a,cAAc5B,WAAWE,KAAK2B;mBACzBC,OAAX;eAEO/B,KACLN,MACAO,WAAWnT,QACXmT,WAAWlT,UACX;kBACU8U;uBACK,CAACrY,OAAOP,QAAQxB,SAAf,CAAD;SANV;;UAWL+B,OAAOJ,QAAQiK,QAAf,KAA2B7J,OAAO5B,SAASyL,QAAhB,GAA0B;YACjDwO,eAAc5B,WAAWE,KAAK2B;mBACzBC,OAAX;eAEO/B,KACLN,MACAO,WAAWnT,QACXmT,WAAWlT,UACX;kBACU8U;uBACK,CAACxO,QAAD;SANV;;;UAiBL,SAAA,KAASsK,OAAM;iBACRwC,KAAK6B,kBAAkBA,kBAAkBtO,YAAYiK;aACzDsC;;YAOD,SAAAgC,UAAW;iBACNL,MAAMK,OAAOhC,UAAxB;aACOA;;YAOD,SAAA8B,UAAW;iBACNH,MAAMG,OAAO9B,UAAxB;aACOA;;GA9DX;MAmEIA,sBAAsB5J,wBAAwB;QAC1C6L,MAAM,IAAI3M,oBAAoB0K,WAAWnT,QAAQmT,WAAWlT,UAChE,EAAE7B,KAAK+U,WAAW3L,OAAOnJ,KAAK8U,WAAW1L,OAAOE,MAAMwL,WAAWzL,OAAjE,CADU;WAGLjN,KAAK,CAAC,iBAAiB,YAAY,kBAAkB,QAAQ,OAAO,KAA/D,GAAuE,SAAS4a,QAAQ;UAC5FC,KAAKnC,WAAWkC,MAAX;UACLE,KAAKH,IAAIC,MAAJ;iBACAA,MAAX,IAAqBD,IAAIC,MAAJ,IAAc,WAAW;YACtCja,OAAOlB,MAAMC,UAAUG,MAAMI,KAAKC,SAA3B;WACVW,MAAM8Z,KAAKha,IAAd;eACOka,GAAGha,MAAM6X,YAAY/X,IAArB;;KANX;QAUIuO,SAAS+J,IAAI,YAAjB;eACWvT,WAAWwR,aAAayD,IAAIjV,YAAYgT,WAAWhT,WAAW+U,iBAAzE;aACS/B,sBAAsB1K,qBAAqB;QAC9CrP,IAAI,SAAJA,GAAaoc,UAAU;UAEvB9Y,OAAOQ,SAASiW,WAAW3L,KAA3B,KAAqC9K,OAAOQ,SAASiW,WAAW1L,KAA3B,GAAmC;YAIpEgO,UAAUtC,WAAWE,KAAK6B,kBAAkBA,kBAAkBtO;YAC9D8O,eAAevC,WAAW2B,MAAMnF,YAAYlL,QAAQ0O,UAArC,IAAmD;mBAE7D8B,OAAX;YACMU,gBAAgBzC,KACpBN,MACAO,WAAWnT,QACXmT,WAAWlT,UACX;kBACUkT,WAAWE,KAAK2B;uBACX,CAAC7B,WAAW3L,OAAO2L,WAAW1L,OAAO0L,WAAWzL,MAAhD;SANK;sBAURmJ,KAAK4E,OAAnB;YACIC,aAAcC,eAAcR,OAAd;eAEXQ;;aAGFH;;eAGEpX,MAAM1B,OAAOkZ,QAAQxc,GAAG+Z,WAAW/U,GAA7B;eACNC,MAAM3B,OAAOkZ,QAAQxc,GAAG+Z,WAAW9U,GAA7B;aACR8U,sBAAsBvN,mBAAmB;QAC9C1K,KAAKwY,IAAI,SAAS,WAAW;UAC3BmC,UAAU1C,WAAWpN,YAAY,OAArC;KADF;QAII7K,KAAKiY,WAAWpN,YAAY,SAAS,SAASjF,GAAG;QACjDgV,gBAAF;KADF;aAGS3C,sBAAsBhJ,oBAAoB;QAC/CjP,KAAKwY,IAAI,SAAS,WAAW;UAC3BmC,UAAU1C,WAAW9I,UAAU,OAAnC;KADF;QAIInP,KAAKwY,IAAI,aAAa,WAAW;UAC/B/J,SAASwJ,WAAW9I,UAAU,OAAlC;KADF;QAIInP,KAAKwY,IAAI,YAAY,WAAW;UAC9BxI,YAAYiI,WAAW9I,UAAU,OAArC;KADF;aAGS8I,sBAAsB5I,iBAAiB;QAC5CZ,SAAS+J,IAAI,OAAjB;eACWjT,gBAAgB/D,OAAOkZ,QAAQ,SAAS3U,KAAK;SACnDK,MAAMyU,kBAAkB5C,WAAW3I,QAAQrR,SAAnB;aACpB8H;OACNkS,WAAW1S,aAHa;eAKhBA,cAAX;;aAGSyF,WAAWxJ,OAAOkZ,QAAQ,SAAS3U,KAAK;QAC7C2R,KAAIjC,QAAJ,EAAciE,mBAAmBzB,WAAW6C,WAAX,GAAyB;yBACzCpD,KAAIjC,QAAJ,GAAe,IAAlC;;WAGK1P;KACNkS,WAAWjN,QANQ;;AASxB,SAAS+P,iBAAiBrD,MAAKO,YAAY;MAEnCT,OAAOE,KAAIjC,QAAJ;MAIPuF,eAAexD,KAAKjD,oBAAoBhL,QAAQ0O,WAAWnT,MAA5C;MAGjBkW,iBAAiB,IAAI;QAEnBC,gBAAgBzD,KAAKhD,uCAAuCwG,YAA5C;QAIhBC,kBAAkB/Z,QAAW;sBACf,CAAA;WACXsT,uCAAuCwG,YAA5C,IACEC;;kBAIUhD,WAAWlT,QAAzB,IAAqCkT;QAGjCT,KAAK5C,QAAQ4C,KAAK5C,KAAKsE,YAAY;UAC/BgC,YAAY1D,KAAK5C,KAAKsE;UAGxBrE,SAAAA;UAEAqG,UAAUxD,KAAI7C,MAAd,GAAuB;iBAChBqG,UAAUxD,KAAI7C,MAAd;iBACAqG,UAAUtH,2BAAV,GAAwC;iBAExCsH,UAAUtH,2BAAV;aACJ;;;UAMHiB,OAAOmG,YAAP,KAAwBnG,OAAOmG,YAAP,EAAqB/C,WAAWlT,QAAhC,MAA8C7D,QAAW;YAE7EsC,QAAQqR,OAAOmG,YAAP,EAAqB/C,WAAWlT,QAAhC;mBAGHC,eAAexB;mBACfwH,SAASxH,KAApB;;;;;AAMR,SAASwU,KAAIN,MAAK5S,QAAQC,UAAUsC,QAAQ;MACtCvC,OAAOC,QAAP,MAAqB7D,QAAW;UAC5B,IAAIwC,MAAJ,aAAqBoB,SAArB,wBAAiDC,WAAjD,GAAA;;MAGJkT,aAAAA;MAEA5Q,OAAOzJ,OAAO;iBACH,IAAIyR,gBAAgBvK,QAAQC,QAA5B;SACR;QACCoW,cAAc,CAACrW,QAAQC,QAAT,EAAmBqW,OAAO/T,OAAO8T,WAAjC;iBACPnJ,kBAAkB5R,MAAMsX,MAAKyD,WAA7B;;MAGX9T,OAAOgU,kBAAkBxW,YAAY;WAChCwW,SAAShU,OAAOgU,OAAOlD;;mBAGfT,MAAKO,UAAtB;MAEIxJ,SAASwJ,WAAWhT,YAAY,GAApC;MAEM0Q,OAAOzQ,SAASC,cAAc,MAAvB;MACTsJ,SAASkH,MAAM,eAAnB;OACKjK,YAAYuM,WAAWlT;MAEtBuW,YAAYpW,SAASC,cAAc,KAAvB;YACR8F,YAAY0K,IAAtB;YACU1K,YAAYgN,WAAWhT,UAAjC;MAEMuT,KAAK1B,OAAOY,MAAK4D,WAAWjU,OAAOgU,MAA9B;MAEP5M,SAAS+J,IAAIrE,IAAIwD,oBAArB;MACIM,sBAAsB5I,iBAAiB;QACrCZ,SAAS+J,IAAI,OAAjB;SACK;QACD/J,SAAS+J,IAAb,QAAwBP,WAAWzS,SAAX,CAAxB,CAAA;;oBAGgBkS,MAAKc,IAAIP,UAA3B;OAEI3D,cAAc9K,KAAKyO,UAAvB;SAEOA;;AAGT,SAAS7C,oBAAoBsC,MAAK5X,KAAK;SAE9BoF,SAASqW,SAAS9Q,OAAO,MAAM3K;;AAGxC,SAAS0b,gBAAgB9D,MAAK/B,MAAM8F,aAAa;MACzChQ,MAAMvG,SAASC,cAAc,QAAvB;MACRuG,YAAYiK;MACZnS,QAAQmS;OACR+D,gBAAgBzO,YAAYQ,GAAhC;MACIgQ,aAAa;SACX/B,gBAAgB9N,gBAAgB8L,KAAIgC,gBAAgB5a,SAAS;;;AAIrE,SAAS4c,gBAAgBhE,MAAKiE,SAAS;UAC7BvV,MAAM0M,UAAU4E,KAAIxC,kBAAkB,UAAU;;AAG1D,SAAS0G,YAAYlE,MAAK;MAClBmE,MAAMnE,KAAImB,aAAa3T,SAASC,cAAc,IAAvB;MAEzBsJ,SAASiJ,KAAIzS,YAAY,UAA7B;OAEImP,KAAKqC,aAAaoF,KAAKnE,KAAItD,KAAK0H,UAApC;MAEIrN,SAASoN,KAAK,UAAlB;MAEME,QAAQ7W,SAASC,cAAc,MAAvB;QACRuG,YAAY;MACd+C,SAASsN,OAAO,cAApB;MAGMC,SAAS9W,SAASC,cAAc,MAAvB;SACRuG,YAAY;MACf+C,SAASuN,QAAQ,QAArB;MACIvN,SAASuN,QAAQ,MAArB;MAEMC,UAAU/W,SAASC,cAAc,MAAvB;UACRuG,YAAY;MAChB+C,SAASwN,SAAS,QAAtB;MACIxN,SAASwN,SAAS,SAAtB;MAEMC,UAAUhX,SAASC,cAAc,MAAvB;UACRuG,YAAY;MAChB+C,SAASyN,SAAS,QAAtB;MACIzN,SAASyN,SAAS,QAAtB;MAEMC,SAASzE,KAAIgC,kBAAkBxU,SAASC,cAAc,QAAvB;MAEjCuS,KAAI9C,QAAQ8C,KAAI9C,KAAKsE,YAAY;WAC5B3Z,KAAKmY,KAAI9C,KAAKsE,YAAY,SAAS1V,OAAO1D,KAAK;sBACpC4X,MAAK5X,KAAKA,QAAQ4X,KAAI7C,MAAtC;KADF;SAGK;oBACW6C,MAAK9D,6BAA6B,KAAlD;;MAGE5T,KAAKmc,QAAQ,UAAU,WAAW;aAC3BvS,SAAQ,GAAGA,SAAQ8N,KAAIgC,gBAAgB5a,QAAQ8K,UAAS;WAC3D8P,gBAAgB9P,MAApB,EAA2B8B,YAAYgM,KAAIgC,gBAAgB9P,MAApB,EAA2BpG;;SAGhEqR,SAAS,KAAKrR;GALpB;MAQIyH,YAAYkR,MAAhB;MACIlR,YAAY8Q,KAAhB;MACI9Q,YAAY+Q,MAAhB;MACI/Q,YAAYgR,OAAhB;MACIhR,YAAYiR,OAAhB;MAEIrI,wBAAwB;QACpB8H,UAAUzW,SAASkX,eAAe,kBAAxB;QACVC,uBAAuBnX,SAASkX,eAAe,kBAAxB;QACvBE,cAAcpX,SAASkX,eAAe,iBAAxB;gBAERhW,MAAM0M,UAAU;QAExBgB,aAAaqB,QAAQC,oBAAoBsC,MAAK,SAAzB,CAArB,MAA8D,QAAQ;2BACnD5M,aAAa,WAAW,SAA7C;;oBAGc4M,MAAKiE,OAArB;QAGI3b,KAAKqc,sBAAsB,UAAU,WAAW;WAC9CnH,kBAAkB,CAACwC,KAAIxC;sBACXwC,MAAKiE,OAArB;KAFF;;MAMIY,yBAAyBrX,SAASkX,eAAe,oBAAxB;MAE3Bpc,KAAKuc,wBAAwB,WAAW,SAAS3W,GAAG;QAClDA,EAAE6C,YAAY7C,EAAEmS,UAAU,MAAMnS,EAAE8C,YAAY,KAAK;oBACvCmK,KAAd;;GAFJ;MAMI7S,KAAK+b,OAAO,SAAS,WAAW;2BACXrQ,YAAY2K,KAAKe,UAAUM,KAAIL,cAAJ,GAAqBnW,QAAW,CAA/C;kBACrBsb,KAAd;2BACuBC,MAAvB;2BACuBN,OAAvB;GAJF;MAOInc,KAAKgc,QAAQ,SAAS,WAAW;SAC/BU,KAAJ;GADF;MAII1c,KAAKic,SAAS,SAAS,WAAW;QAC9B7C,aAAauD,OAAO,0BAAP;QACfvD,YAAY;WACVwD,OAAOxD,UAAX;;GAHJ;MAOIpZ,KAAKkc,SAAS,SAAS,WAAW;SAChCxG,OAAJ;GADF;;AAOF,SAASmH,gBAAgBnF,MAAK;MACxBoF,UAAAA;OAEAhE,kBAAkB5T,SAASC,cAAc,KAAvB;SAEfX,OAAOkT,KAAIoB,gBAAgB1S,OAAO;WAEhC;gBACK;YACJ;YACA;cACE;GANZ;WAWS2W,KAAKnX,GAAG;MACb8I,eAAF;SAEI1E,SAAS8S,UAAUlX,EAAE6B;SACrBqO,SAAJ;cACUlQ,EAAE6B;WAEL;;WAGAuV,WAAW;QACdhN,YAAY0H,KAAI3B,eAAe5B,IAAI0D,UAAvC;QACI9J,OAAO/F,QAAQ,aAAa+U,IAAhC;QACIhP,OAAO/F,QAAQ,WAAWgV,QAA9B;;WAGOC,UAAUrX,GAAG;MAClB8I,eAAF;cAEU9I,EAAE6B;QAERgH,SAASiJ,KAAI3B,eAAe5B,IAAI0D,UAApC;QACI7X,KAAKgI,QAAQ,aAAa+U,IAA9B;QACI/c,KAAKgI,QAAQ,WAAWgV,QAA5B;WAEO;;MAGLhd,KAAK0X,KAAIoB,iBAAiB,aAAamE,SAA3C;MACIjd,KAAK0X,KAAI3B,eAAe,aAAakH,SAAzC;OAEIhY,WAAWwR,aAAaiB,KAAIoB,iBAAiBpB,KAAIzS,WAAW+U,iBAAhE;;AAGF,SAASkD,SAASxF,MAAKyF,GAAG;OACpBlY,WAAWmB,MAAM4D,QAAQmT,IAAI;MAG7BzF,KAAImB,cAAcnB,KAAI/C,WAAW;SAC/BkE,WAAWzS,MAAM4D,QAAQmT,IAAI;;MAE/BzF,KAAI3B,eAAe;SACjBA,cAAc3P,MAAM4D,QAAQmT,IAAI;;;AAIxC,SAAShE,iBAAiBzB,MAAK0F,kBAAkB;MACzCjb,YAAW,CAAA;SAGV5C,KAAKmY,KAAInD,qBAAqB,SAASxO,KAAK6D,QAAO;QAClDyT,cAAc,CAAA;QAGdpC,gBACJvD,KAAIlD,uCAAuC5K,MAA3C;WAGKrK,KAAK0b,eAAe,SAAShD,YAAYlT,UAAU;kBAC5CA,QAAZ,IAAwBqY,mBAAmBnF,WAAWjT,eAAeiT,WAAWzS,SAAX;KADvE;cAKSoE,MAAT,IAAkByT;GAbpB;SAgBOlb;;AAGT,SAASmb,qBAAqB5F,MAAK;WACxB9N,SAAQ,GAAGA,SAAQ8N,KAAIgC,gBAAgB5a,QAAQ8K,UAAS;QAC3D8N,KAAIgC,gBAAgB9P,MAApB,EAA2BpG,UAAUkU,KAAI7C,QAAQ;WAC/C6E,gBAAgB9N,gBAAgBhC;;;;AAK1C,SAAS2T,eAAeC,iBAAiB;MACnCA,gBAAgB1e,WAAW,GAAG;4BACVU,KAAKwI,QAAQ,WAAW;qBAC7BwV,eAAf;KADF;;SAKKje,KAAKie,iBAAiB,SAASva,GAAG;MACrCsC,cAAF;GADF;;AC91CK,IAAM3H,QAAQ;;QAEb0G;;;AAID,IAAMmZ,cAAc;;;;;;;;;;;AAYpB,IAAMxX,QAAM,EAAEA,IAAF;AAEZ,IAAMyR,MAAM,EAAEvD,IAAF;AAEZ,IAAMA,QAAMuJ;AAEnB,IAAA,QAAe;;;;;;;", "names": ["color", "forceCSSHex", "colorFormat", "__state", "conversionName", "toString", "r", "Math", "round", "g", "b", "a", "h", "s", "toFixed", "v", "str", "hex", "length", "ARR_EACH", "Array", "prototype", "for<PERSON>ach", "ARR_SLICE", "slice", "Common", "target", "each", "call", "arguments", "obj", "keys", "isObject", "Object", "key", "isUndefined", "bind", "toCall", "args", "i", "apply", "itr", "scope", "l", "BREAK", "fnc", "func", "threshold", "callImmediately", "timeout", "delayed", "callNow", "setTimeout", "toArray", "undefined", "isNaN", "isArray", "constructor", "Function", "INTERPRETATIONS", "common", "isString", "original", "test", "match", "parseInt", "read", "parseFloat", "isNumber", "write", "result", "toReturn", "interpret", "family", "litmus", "conversions", "conversion", "tmpComponent", "ColorMath", "hi", "floor", "f", "p", "q", "t", "c", "min", "max", "delta", "NaN", "hex_with_component", "componentIndex", "value", "Color", "Error", "colorToString", "defineRGBComponent", "component", "componentHexIndex", "defineProperty", "get$$1", "space", "recalculateRGB", "set$$1", "defineHSVComponent", "recalculateHSV", "math", "component_from_hex", "extend", "hsv_to_rgb", "rgb_to_hsv", "COMPONENTS", "rgb_to_hex", "Controller", "object", "property", "initialValue", "dom<PERSON>lement", "document", "createElement", "__on<PERSON><PERSON>e", "__onFinishChange", "newValue", "updateDisplay", "getValue", "EVENT_MAP", "EVENT_MAP_INV", "k", "e", "CSS_VALUE_PIXELS", "cssValueToPixels", "val", "isNull", "dom", "elem", "selectable", "style", "onselectstart", "MozUserSelect", "KhtmlUserSelect", "unselectable", "hor", "vert", "vertical", "horizontal", "position", "left", "right", "top", "bottom", "eventType", "pars", "aux", "params", "className", "evt", "createEvent", "clientX", "x", "clientY", "y", "initMouseEvent", "bubbles", "cancelable", "window", "clickCount", "init", "initKeyboardEvent", "initKeyEvent", "defaults", "ctrl<PERSON>ey", "altKey", "shift<PERSON>ey", "metaKey", "keyCode", "charCode", "initEvent", "dispatchEvent", "event", "newBool", "bool", "addEventListener", "attachEvent", "removeEventListener", "detachEvent", "classes", "split", "indexOf", "push", "join", "replace", "removeAttribute", "index", "splice", "RegExp", "getComputedStyle", "width", "height", "el", "offset", "offsetParent", "offsetLeft", "offsetTop", "activeElement", "type", "href", "BooleanController", "_this", "__prev", "__checkbox", "setAttribute", "onChange", "setValue", "append<PERSON><PERSON><PERSON>", "checked", "OptionController", "opts", "options", "__select", "map", "element", "opt", "innerHTML", "desiredValue", "selectedIndex", "isActive", "StringController", "__input", "onBlur", "blur", "numDecimals", "_x", "NumberController", "_params", "__min", "__max", "__step", "step", "__impliedStep", "pow", "log", "abs", "LN10", "__precision", "_v", "minValue", "maxValue", "<PERSON><PERSON><PERSON><PERSON>", "roundToDecimal", "decimals", "tenTo", "NumberControllerBox", "__truncationSuspended", "prevY", "attempted", "onFinish", "onMouseDrag", "diff", "onMouseUp", "unbind", "onMouseDown", "i1", "i2", "o1", "o2", "NumberControllerSlider", "__background", "__foreground", "onTouchStart", "addClass", "preventDefault", "bgRect", "getBoundingClientRect", "touches", "onTouchMove", "onTouchEnd", "pct", "FunctionController", "text", "__button", "fire", "ColorController", "__color", "__temp", "makeSelectable", "__selector", "__saturation_field", "__field_knob", "__field_knob_border", "__hue_knob", "__hue_field", "__input_textShadow", "removeClass", "valueField", "fieldDown", "fieldDownH", "setSV", "fieldUpSV", "setH", "fieldUpH", "toOriginal", "fieldRect", "mismatch", "flip", "_flip", "toHexString", "marginTop", "vendors", "linearGradient", "background", "vendor", "cssText", "hue<PERSON><PERSON><PERSON>", "css", "url", "indoc", "doc", "link", "rel", "getElementsByTagName", "cssContent", "injected", "head", "saveDialogContents", "ControllerFactory", "isFunction", "isBoolean", "requestAnimationFrame", "callback", "webkitRequestAnimationFrame", "mozRequestAnimationFrame", "oRequestAnimationFrame", "msRequestAnimationFrame", "CenteredDiv", "backgroundElement", "makeFullscreen", "body", "hide", "display", "opacity", "webkitTransform", "layout", "defer", "innerWidth", "getWidth", "innerHeight", "getHeight", "inject", "styleSheet", "CSS_NAMESPACE", "HIDE_KEY_CODE", "CLOSE_BUTTON_HEIGHT", "DEFAULT_DEFAULT_PRESET_NAME", "SUPPORTS_LOCAL_STORAGE", "localStorage", "SAVE_DIALOGUE", "autoPlaceVirgin", "autoPlaceContainer", "hideable<PERSON><PERSON><PERSON>", "GUI", "__ul", "__folders", "__controllers", "__rememberedObjects", "__rememberedObjectIndecesToControllers", "__listening", "DEFAULT_WIDTH", "autoPlace", "load", "preset", "parent", "hideable", "resizable", "scrollable", "useLocalStorage", "getItem", "getLocalStorageHash", "saveToLocalStorage", "titleRow", "defineProperties", "closeOnTop", "getRoot", "revert", "name", "closed", "CLASS_CLOSED", "onResize", "__<PERSON><PERSON><PERSON><PERSON>", "TEXT_OPEN", "TEXT_CLOSED", "setItem", "CLASS_MAIN", "savedGui", "JSON", "parse", "CLASS_CLOSE_BUTTON", "CLASS_CLOSE_TOP", "insertBefore", "childNodes", "CLASS_CLOSE_BOTTOM", "titleRowName", "createTextNode", "addRow", "onClickTitle", "CLASS_AUTO_PLACE_CONTAINER", "CLASS_AUTO_PLACE", "__resi<PERSON><PERSON><PERSON><PERSON>", "onResizeDebounced", "stringify", "getSaveObject", "saveToLocalStorageIfPossible", "resetWidth", "root", "toggleHide", "gui", "CLASS_CONTROLLER_ROW", "CLASS_TOO_TALL", "CLASS_DRAG", "_keydownHandler", "which", "add", "controller", "<PERSON><PERSON><PERSON><PERSON>", "__li", "subfolder", "removeFolder", "newGuiParams", "folders", "li", "folder", "parentElement", "getOffset", "node", "__save_row", "__resize_handle", "offsetHeight", "debounce", "saveDialogueContents", "remembered", "getCurrentPreset", "presetName", "newDom", "liBefore", "removeListeners", "markPresetModified", "modified", "__preset_select", "augmentController", "__gui", "nextS<PERSON>ling", "nextElement<PERSON><PERSON>ling", "remove", "first<PERSON><PERSON><PERSON><PERSON><PERSON>", "listen", "box", "method", "pc", "pb", "returned", "old<PERSON>ame", "wasListening", "newController", "compose", "fakeEvent", "stopPropagation", "borderLeftColor", "isModified", "recallSavedValue", "matchedIndex", "controllerMap", "presetMap", "factoryArgs", "concat", "before", "container", "location", "addPresetOption", "setSelected", "showHideExplain", "explain", "addSaveMenu", "div", "<PERSON><PERSON><PERSON><PERSON>", "gears", "button", "button2", "button3", "select", "getElementById", "localStorageCheckBox", "saveLocally", "newConstructorTextArea", "show", "focus", "save", "prompt", "saveAs", "addResizeHandle", "pmouseX", "drag", "dragStop", "dragStart", "<PERSON><PERSON><PERSON><PERSON>", "w", "useInitialValues", "savedValues", "setPresetSelectIndex", "updateDisplays", "controllerArray", "controllers", "GUIImport"]}
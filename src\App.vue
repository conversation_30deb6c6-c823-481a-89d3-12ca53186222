<template>
  <div class="app">
    <!-- 3D场景容器 -->
    <div ref="sceneContainer" class="scene-container"></div>
    
    <!-- 顶部导航栏 -->
    <AppHeader 
      @search="handleSearch"
      @toggle-layer="handleToggleLayer"
      @reset-view="handleResetView"
    />
    
    <!-- 侧边控制面板 -->
    <ControlPanel 
      v-model:visible="controlPanelVisible"
      :settings="earthSettings"
      @update-settings="handleUpdateSettings"
    />
    
    <!-- 底部信息栏 -->
    <InfoBar 
      :coordinates="currentCoordinates"
      :altitude="currentAltitude"
      :performance="performanceStats"
    />
    
    <!-- 搜索结果面板 -->
    <SearchResults 
      v-if="searchResults.length > 0"
      :results="searchResults"
      @select-location="handleSelectLocation"
      @close="searchResults = []"
    />
    
    <!-- 位置详情面板 -->
    <LocationDetails 
      v-if="selectedLocation"
      :location="selectedLocation"
      @close="selectedLocation = null"
      @navigate="handleNavigateToLocation"
    />
    
    <!-- 快捷键帮助 -->
    <HelpModal 
      v-model:visible="helpModalVisible"
    />
    
    <!-- 设置面板 -->
    <SettingsModal 
      v-model:visible="settingsModalVisible"
      :settings="appSettings"
      @update-settings="handleUpdateAppSettings"
    />
    
    <!-- 通知系统 -->
    <NotificationSystem ref="notifications" />
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, nextTick } from 'vue'
import { useWindowSize, useKeyModifier } from '@vueuse/core'
import AppHeader from './components/AppHeader.vue'
import ControlPanel from './components/ControlPanel.vue'
import InfoBar from './components/InfoBar.vue'
import SearchResults from './components/SearchResults.vue'
import LocationDetails from './components/LocationDetails.vue'
import HelpModal from './components/HelpModal.vue'
import SettingsModal from './components/SettingsModal.vue'
import NotificationSystem from './components/NotificationSystem.vue'
import { EarthEngine } from './core/EarthEngine.js'

// 响应式数据
const sceneContainer = ref(null)
const earthEngine = ref(null)
const controlPanelVisible = ref(false)
const helpModalVisible = ref(false)
const settingsModalVisible = ref(false)
const searchResults = ref([])
const selectedLocation = ref(null)
const currentCoordinates = ref({ lat: 0, lng: 0 })
const currentAltitude = ref(0)
const performanceStats = ref({
  fps: 0,
  drawCalls: 0,
  triangles: 0
})

// 设置
const earthSettings = ref({
  showAtmosphere: true,
  showClouds: true,
  showStars: true,
  showLabels: true,
  showGrid: false,
  earthRotation: 0.001,
  cloudSpeed: 0.0005,
  atmosphereIntensity: 1.0,
  starDensity: 1.0
})

const appSettings = ref({
  quality: 'high',
  enableShadows: true,
  enablePostProcessing: true,
  autoRotate: false,
  showPerformanceStats: false,
  language: 'zh-CN'
})

// 窗口大小监听
const { width, height } = useWindowSize()

// 键盘修饰键
const ctrlPressed = useKeyModifier('Control')
const shiftPressed = useKeyModifier('Shift')

// 生命周期
onMounted(async () => {
  await nextTick()
  await initEarthEngine()
  setupEventListeners()
  hideLoadingScreen()
})

onUnmounted(() => {
  if (earthEngine.value) {
    earthEngine.value.dispose()
  }
  removeEventListeners()
})

// 初始化地球引擎
async function initEarthEngine() {
  try {
    earthEngine.value = new EarthEngine(sceneContainer.value)
    await earthEngine.value.init()
    
    // 设置回调
    earthEngine.value.onLocationUpdate = (coords, altitude) => {
      currentCoordinates.value = coords
      currentAltitude.value = altitude
    }
    
    earthEngine.value.onPerformanceUpdate = (stats) => {
      performanceStats.value = stats
    }
    
    earthEngine.value.onLocationClick = (location) => {
      selectedLocation.value = location
    }
    
  } catch (error) {
    console.error('地球引擎初始化失败:', error)
    showNotification('地球引擎初始化失败，请刷新页面重试', 'error')
  }
}

// 事件处理
function handleSearch(query) {
  if (!earthEngine.value) return
  
  const results = earthEngine.value.searchLocations(query)
  searchResults.value = results
  
  if (results.length === 0) {
    showNotification(`未找到"${query}"相关的位置`, 'warning')
  }
}

function handleToggleLayer(layerName) {
  if (!earthEngine.value) return
  
  earthSettings.value[layerName] = !earthSettings.value[layerName]
  earthEngine.value.updateSettings(earthSettings.value)
}

function handleResetView() {
  if (!earthEngine.value) return
  
  earthEngine.value.resetView()
  showNotification('视图已重置', 'success')
}

function handleUpdateSettings(newSettings) {
  earthSettings.value = { ...earthSettings.value, ...newSettings }
  if (earthEngine.value) {
    earthEngine.value.updateSettings(earthSettings.value)
  }
}

function handleUpdateAppSettings(newSettings) {
  appSettings.value = { ...appSettings.value, ...newSettings }
  if (earthEngine.value) {
    earthEngine.value.updateAppSettings(appSettings.value)
  }
}

function handleSelectLocation(location) {
  if (!earthEngine.value) return
  
  earthEngine.value.flyToLocation(location)
  searchResults.value = []
  selectedLocation.value = location
}

function handleNavigateToLocation(location) {
  if (!earthEngine.value) return
  
  earthEngine.value.flyToLocation(location)
}

// 事件监听
function setupEventListeners() {
  window.addEventListener('keydown', handleKeyDown)
  window.addEventListener('resize', handleResize)
}

function removeEventListeners() {
  window.removeEventListener('keydown', handleKeyDown)
  window.removeEventListener('resize', handleResize)
}

function handleKeyDown(event) {
  // 防止在输入框中触发快捷键
  if (event.target.tagName === 'INPUT' || event.target.tagName === 'TEXTAREA') {
    return
  }
  
  switch (event.code) {
    case 'KeyH':
      helpModalVisible.value = !helpModalVisible.value
      break
    case 'KeyS':
      if (ctrlPressed.value) {
        event.preventDefault()
        settingsModalVisible.value = !settingsModalVisible.value
      }
      break
    case 'KeyC':
      controlPanelVisible.value = !controlPanelVisible.value
      break
    case 'KeyR':
      if (!ctrlPressed.value) {
        handleResetView()
      }
      break
    case 'Space':
      event.preventDefault()
      earthSettings.value.earthRotation = earthSettings.value.earthRotation > 0 ? 0 : 0.001
      handleUpdateSettings({ earthRotation: earthSettings.value.earthRotation })
      break
    case 'Digit1':
      handleToggleLayer('showAtmosphere')
      break
    case 'Digit2':
      handleToggleLayer('showClouds')
      break
    case 'Digit3':
      handleToggleLayer('showStars')
      break
    case 'Digit4':
      handleToggleLayer('showLabels')
      break
    case 'Escape':
      // 关闭所有模态框
      helpModalVisible.value = false
      settingsModalVisible.value = false
      controlPanelVisible.value = false
      selectedLocation.value = null
      searchResults.value = []
      break
  }
}

function handleResize() {
  if (earthEngine.value) {
    earthEngine.value.handleResize(width.value, height.value)
  }
}

// 工具函数
function hideLoadingScreen() {
  const loadingOverlay = document.getElementById('loading-overlay')
  if (loadingOverlay) {
    loadingOverlay.classList.add('hidden')
    setTimeout(() => {
      loadingOverlay.remove()
    }, 800)
  }
}

function showNotification(message, type = 'info') {
  if (notifications.value) {
    notifications.value.show(message, type)
  }
}

// 暴露给模板的引用
const notifications = ref(null)
</script>

<style lang="scss" scoped>
.app {
  width: 100vw;
  height: 100vh;
  position: relative;
  overflow: hidden;
}

.scene-container {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1;
}
</style>

<template>
  <div class="info-bar">
    <div class="info-section">
      <div class="info-item">
        <div class="info-label">坐标</div>
        <div class="info-value">
          {{ formatCoordinate(coordinates.lat, 'lat') }}, {{ formatCoordinate(coordinates.lng, 'lng') }}
        </div>
      </div>

      <div class="info-item">
        <div class="info-label">高度</div>
        <div class="info-value">{{ formatAltitude(altitude) }}</div>
      </div>

      <div class="info-item" v-if="performance">
        <div class="info-label">性能</div>
        <div class="info-value">
          {{ performance.fps }}fps | {{ performance.drawCalls }}dc | {{ formatNumber(performance.triangles) }}tri
        </div>
      </div>
    </div>

    <div class="controls-section">
      <button class="info-button" title="复制坐标" @click="copyCoordinates">
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
          <rect x="9" y="9" width="13" height="13" rx="2" ry="2" stroke="currentColor" stroke-width="2"/>
          <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1" stroke="currentColor" stroke-width="2"/>
        </svg>
      </button>

      <button class="info-button" title="分享位置" @click="shareLocation">
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
          <circle cx="18" cy="5" r="3" stroke="currentColor" stroke-width="2"/>
          <circle cx="6" cy="12" r="3" stroke="currentColor" stroke-width="2"/>
          <circle cx="18" cy="19" r="3" stroke="currentColor" stroke-width="2"/>
          <line x1="8.59" y1="13.51" x2="15.42" y2="17.49" stroke="currentColor" stroke-width="2"/>
          <line x1="15.41" y1="6.51" x2="8.59" y2="10.49" stroke="currentColor" stroke-width="2"/>
        </svg>
      </button>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps(['coordinates', 'altitude', 'performance'])
const emit = defineEmits(['copy-coordinates', 'share-location'])

function formatCoordinate(value, type) {
  if (typeof value !== 'number') return '0°'

  const abs = Math.abs(value)
  const degrees = Math.floor(abs)
  const minutes = Math.floor((abs - degrees) * 60)
  const seconds = Math.floor(((abs - degrees) * 60 - minutes) * 60)

  const direction = type === 'lat'
    ? (value >= 0 ? 'N' : 'S')
    : (value >= 0 ? 'E' : 'W')

  return `${degrees}°${minutes}'${seconds}"${direction}`
}

function formatAltitude(altitude) {
  if (typeof altitude !== 'number') return '0 km'

  if (altitude < 1) {
    return `${Math.round(altitude * 1000)} m`
  } else {
    return `${altitude.toFixed(1)} km`
  }
}

function formatNumber(num) {
  if (typeof num !== 'number') return '0'

  if (num >= 1000000) {
    return `${(num / 1000000).toFixed(1)}M`
  } else if (num >= 1000) {
    return `${(num / 1000).toFixed(1)}K`
  }
  return num.toString()
}

function copyCoordinates() {
  const coordText = `${props.coordinates.lat.toFixed(6)}, ${props.coordinates.lng.toFixed(6)}`
  navigator.clipboard.writeText(coordText).then(() => {
    emit('copy-coordinates', coordText)
  })
}

function shareLocation() {
  emit('share-location', {
    coordinates: props.coordinates,
    altitude: props.altitude
  })
}
</script>

<style lang="scss" scoped>
.info-bar {
  position: fixed;
  bottom: 20px;
  left: 20px;
  right: 20px;
  z-index: 50;
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(20px);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
  padding: 12px 20px;
  pointer-events: auto;
  transition: all var(--transition-normal);

  &:hover {
    background: rgba(0, 0, 0, 0.9);
    border-color: var(--border-hover);
  }
}

.info-section {
  display: flex;
  align-items: center;
  gap: 24px;
  flex: 1;
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: 2px;

  .info-label {
    font-size: 11px;
    color: var(--text-tertiary);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-weight: 500;
  }

  .info-value {
    font-size: 13px;
    color: var(--text-primary);
    font-weight: 500;
    font-family: 'Monaco', 'Menlo', monospace;
  }
}

.controls-section {
  display: flex;
  align-items: center;
  gap: 8px;
}

.info-button {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: transparent;
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  color: var(--text-secondary);
  cursor: pointer;
  transition: all var(--transition-fast);

  &:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: var(--border-hover);
    color: var(--text-primary);
    transform: translateY(-1px);
  }

  &:active {
    transform: translateY(0);
  }
}

// 响应式设计
@media (max-width: 768px) {
  .info-bar {
    bottom: 16px;
    left: 16px;
    right: 16px;
    padding: 10px 16px;
    flex-direction: column;
    gap: 12px;
  }

  .info-section {
    gap: 16px;
    width: 100%;
    justify-content: space-between;
  }

  .info-item {
    .info-label {
      font-size: 10px;
    }

    .info-value {
      font-size: 12px;
    }
  }
}

@media (max-width: 480px) {
  .info-section {
    flex-direction: column;
    gap: 8px;
    align-items: flex-start;
  }

  .info-item {
    flex-direction: row;
    gap: 8px;
    align-items: center;

    .info-label {
      min-width: 40px;
    }
  }
}
</style>

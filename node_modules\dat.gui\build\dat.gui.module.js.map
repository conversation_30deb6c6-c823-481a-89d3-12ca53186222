{"version": 3, "file": "dat.gui.module.js", "sources": ["../src/dat/color/toString.js", "../src/dat/utils/common.js", "../src/dat/color/interpret.js", "../src/dat/color/math.js", "../src/dat/color/Color.js", "../src/dat/controllers/Controller.js", "../src/dat/dom/dom.js", "../src/dat/controllers/BooleanController.js", "../src/dat/controllers/OptionController.js", "../src/dat/controllers/StringController.js", "../src/dat/controllers/NumberController.js", "../src/dat/controllers/NumberControllerBox.js", "../src/dat/controllers/NumberControllerSlider.js", "../src/dat/controllers/FunctionController.js", "../src/dat/controllers/ColorController.js", "../src/dat/utils/css.js", "../src/dat/gui/saveDialogue.html.js", "../src/dat/controllers/ControllerFactory.js", "../src/dat/utils/requestAnimationFrame.js", "../src/dat/dom/CenteredDiv.js", "../src/dat/gui/GUI.js", "../src/dat/index.js"], "sourcesContent": ["/**\n * dat-gui JavaScript Controller Library\n * https://github.com/dataarts/dat.gui\n *\n * Copyright 2011 Data Arts Team, Google Creative Lab\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n */\n\nexport default function(color, forceCSSHex) {\n  const colorFormat = color.__state.conversionName.toString();\n\n  const r = Math.round(color.r);\n  const g = Math.round(color.g);\n  const b = Math.round(color.b);\n  const a = color.a;\n  const h = Math.round(color.h);\n  const s = color.s.toFixed(1);\n  const v = color.v.toFixed(1);\n\n  if (forceCSSHex || (colorFormat === 'THREE_CHAR_HEX') || (colorFormat === 'SIX_CHAR_HEX')) {\n    let str = color.hex.toString(16);\n    while (str.length < 6) {\n      str = '0' + str;\n    }\n    return '#' + str;\n  } else if (colorFormat === 'CSS_RGB') {\n    return 'rgb(' + r + ',' + g + ',' + b + ')';\n  } else if (colorFormat === 'CSS_RGBA') {\n    return 'rgba(' + r + ',' + g + ',' + b + ',' + a + ')';\n  } else if (colorFormat === 'HEX') {\n    return '0x' + color.hex.toString(16);\n  } else if (colorFormat === 'RGB_ARRAY') {\n    return '[' + r + ',' + g + ',' + b + ']';\n  } else if (colorFormat === 'RGBA_ARRAY') {\n    return '[' + r + ',' + g + ',' + b + ',' + a + ']';\n  } else if (colorFormat === 'RGB_OBJ') {\n    return '{r:' + r + ',g:' + g + ',b:' + b + '}';\n  } else if (colorFormat === 'RGBA_OBJ') {\n    return '{r:' + r + ',g:' + g + ',b:' + b + ',a:' + a + '}';\n  } else if (colorFormat === 'HSV_OBJ') {\n    return '{h:' + h + ',s:' + s + ',v:' + v + '}';\n  } else if (colorFormat === 'HSVA_OBJ') {\n    return '{h:' + h + ',s:' + s + ',v:' + v + ',a:' + a + '}';\n  }\n\n  return 'unknown format';\n}\n", "/**\n * dat-gui JavaScript Controller Library\n * https://github.com/dataarts/dat.gui\n *\n * Copyright 2011 Data Arts Team, Google Creative Lab\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n */\n\nconst ARR_EACH = Array.prototype.forEach;\nconst ARR_SLICE = Array.prototype.slice;\n\n/**\n * Band-aid methods for things that should be a lot easier in JavaScript.\n * Implementation and structure inspired by underscore.js\n * http://documentcloud.github.com/underscore/\n */\n\nconst Common = {\n  BREAK: {},\n\n  extend: function(target) {\n    this.each(ARR_SLICE.call(arguments, 1), function(obj) {\n      const keys = this.isObject(obj) ? Object.keys(obj) : [];\n      keys.forEach(function(key) {\n        if (!this.isUndefined(obj[key])) {\n          target[key] = obj[key];\n        }\n      }.bind(this));\n    }, this);\n\n    return target;\n  },\n\n  defaults: function(target) {\n    this.each(ARR_SLICE.call(arguments, 1), function(obj) {\n      const keys = this.isObject(obj) ? Object.keys(obj) : [];\n      keys.forEach(function(key) {\n        if (this.isUndefined(target[key])) {\n          target[key] = obj[key];\n        }\n      }.bind(this));\n    }, this);\n\n    return target;\n  },\n\n  compose: function() {\n    const toCall = ARR_SLICE.call(arguments);\n    return function() {\n      let args = ARR_SLICE.call(arguments);\n      for (let i = toCall.length - 1; i >= 0; i--) {\n        args = [toCall[i].apply(this, args)];\n      }\n      return args[0];\n    };\n  },\n\n  each: function(obj, itr, scope) {\n    if (!obj) {\n      return;\n    }\n\n    if (ARR_EACH && obj.forEach && obj.forEach === ARR_EACH) {\n      obj.forEach(itr, scope);\n    } else if (obj.length === obj.length + 0) { // Is number but not NaN\n      let key;\n      let l;\n      for (key = 0, l = obj.length; key < l; key++) {\n        if (key in obj && itr.call(scope, obj[key], key) === this.BREAK) {\n          return;\n        }\n      }\n    } else {\n      for (const key in obj) {\n        if (itr.call(scope, obj[key], key) === this.BREAK) {\n          return;\n        }\n      }\n    }\n  },\n\n  defer: function(fnc) {\n    setTimeout(fnc, 0);\n  },\n\n  // if the function is called repeatedly, wait until threshold passes until we execute the function\n  debounce: function(func, threshold, callImmediately) {\n    let timeout;\n\n    return function() {\n      const obj = this;\n      const args = arguments;\n      function delayed() {\n        timeout = null;\n        if (!callImmediately) func.apply(obj, args);\n      }\n\n      const callNow = callImmediately || !timeout;\n\n      clearTimeout(timeout);\n      timeout = setTimeout(delayed, threshold);\n\n      if (callNow) {\n        func.apply(obj, args);\n      }\n    };\n  },\n\n  toArray: function(obj) {\n    if (obj.toArray) return obj.toArray();\n    return ARR_SLICE.call(obj);\n  },\n\n  isUndefined: function(obj) {\n    return obj === undefined;\n  },\n\n  isNull: function(obj) {\n    return obj === null;\n  },\n\n  isNaN: function(obj) {\n    return isNaN(obj);\n  },\n\n  isArray: Array.isArray || function(obj) {\n    return obj.constructor === Array;\n  },\n\n  isObject: function(obj) {\n    return obj === Object(obj);\n  },\n\n  isNumber: function(obj) {\n    return obj === obj + 0;\n  },\n\n  isString: function(obj) {\n    return obj === obj + '';\n  },\n\n  isBoolean: function(obj) {\n    return obj === false || obj === true;\n  },\n\n  isFunction: function(obj) {\n    return obj instanceof Function;\n  }\n\n};\n\nexport default Common;\n", "/**\n * dat-gui JavaScript Controller Library\n * https://github.com/dataarts/dat.gui\n *\n * Copyright 2011 Data Arts Team, Google Creative Lab\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n */\n\nimport toString from './toString';\nimport common from '../utils/common';\n\nconst INTERPRETATIONS = [\n  // Strings\n  {\n    litmus: common.isString,\n    conversions: {\n      THREE_CHAR_HEX: {\n        read: function(original) {\n          const test = original.match(/^#([A-F0-9])([A-F0-9])([A-F0-9])$/i);\n          if (test === null) {\n            return false;\n          }\n\n          return {\n            space: 'HEX',\n            hex: parseInt(\n              '0x' +\n              test[1].toString() + test[1].toString() +\n              test[2].toString() + test[2].toString() +\n              test[3].toString() + test[3].toString(), 0\n            )\n          };\n        },\n\n        write: toString\n      },\n\n      SIX_CHAR_HEX: {\n        read: function(original) {\n          const test = original.match(/^#([A-F0-9]{6})$/i);\n          if (test === null) {\n            return false;\n          }\n\n          return {\n            space: 'HEX',\n            hex: parseInt('0x' + test[1].toString(), 0)\n          };\n        },\n\n        write: toString\n      },\n\n      CSS_RGB: {\n        read: function(original) {\n          const test = original.match(/^rgb\\(\\s*(\\S+)\\s*,\\s*(\\S+)\\s*,\\s*(\\S+)\\s*\\)/);\n          if (test === null) {\n            return false;\n          }\n\n          return {\n            space: 'RGB',\n            r: parseFloat(test[1]),\n            g: parseFloat(test[2]),\n            b: parseFloat(test[3])\n          };\n        },\n\n        write: toString\n      },\n\n      CSS_RGBA: {\n        read: function(original) {\n          const test = original.match(/^rgba\\(\\s*(\\S+)\\s*,\\s*(\\S+)\\s*,\\s*(\\S+)\\s*,\\s*(\\S+)\\s*\\)/);\n          if (test === null) {\n            return false;\n          }\n\n          return {\n            space: 'RGB',\n            r: parseFloat(test[1]),\n            g: parseFloat(test[2]),\n            b: parseFloat(test[3]),\n            a: parseFloat(test[4])\n          };\n        },\n\n        write: toString\n      }\n    }\n  },\n\n  // Numbers\n  {\n    litmus: common.isNumber,\n\n    conversions: {\n\n      HEX: {\n        read: function(original) {\n          return {\n            space: 'HEX',\n            hex: original,\n            conversionName: 'HEX'\n          };\n        },\n\n        write: function(color) {\n          return color.hex;\n        }\n      }\n\n    }\n\n  },\n\n  // Arrays\n  {\n    litmus: common.isArray,\n    conversions: {\n      RGB_ARRAY: {\n        read: function(original) {\n          if (original.length !== 3) {\n            return false;\n          }\n\n          return {\n            space: 'RGB',\n            r: original[0],\n            g: original[1],\n            b: original[2]\n          };\n        },\n\n        write: function(color) {\n          return [color.r, color.g, color.b];\n        }\n      },\n\n      RGBA_ARRAY: {\n        read: function(original) {\n          if (original.length !== 4) return false;\n          return {\n            space: 'RGB',\n            r: original[0],\n            g: original[1],\n            b: original[2],\n            a: original[3]\n          };\n        },\n\n        write: function(color) {\n          return [color.r, color.g, color.b, color.a];\n        }\n      }\n    }\n  },\n\n  // Objects\n  {\n    litmus: common.isObject,\n    conversions: {\n\n      RGBA_OBJ: {\n        read: function(original) {\n          if (common.isNumber(original.r) &&\n            common.isNumber(original.g) &&\n            common.isNumber(original.b) &&\n            common.isNumber(original.a)) {\n            return {\n              space: 'RGB',\n              r: original.r,\n              g: original.g,\n              b: original.b,\n              a: original.a\n            };\n          }\n          return false;\n        },\n\n        write: function(color) {\n          return {\n            r: color.r,\n            g: color.g,\n            b: color.b,\n            a: color.a\n          };\n        }\n      },\n\n      RGB_OBJ: {\n        read: function(original) {\n          if (common.isNumber(original.r) &&\n            common.isNumber(original.g) &&\n            common.isNumber(original.b)) {\n            return {\n              space: 'RGB',\n              r: original.r,\n              g: original.g,\n              b: original.b\n            };\n          }\n          return false;\n        },\n\n        write: function(color) {\n          return {\n            r: color.r,\n            g: color.g,\n            b: color.b\n          };\n        }\n      },\n\n      HSVA_OBJ: {\n        read: function(original) {\n          if (common.isNumber(original.h) &&\n            common.isNumber(original.s) &&\n            common.isNumber(original.v) &&\n            common.isNumber(original.a)) {\n            return {\n              space: 'HSV',\n              h: original.h,\n              s: original.s,\n              v: original.v,\n              a: original.a\n            };\n          }\n          return false;\n        },\n\n        write: function(color) {\n          return {\n            h: color.h,\n            s: color.s,\n            v: color.v,\n            a: color.a\n          };\n        }\n      },\n\n      HSV_OBJ: {\n        read: function(original) {\n          if (common.isNumber(original.h) &&\n            common.isNumber(original.s) &&\n            common.isNumber(original.v)) {\n            return {\n              space: 'HSV',\n              h: original.h,\n              s: original.s,\n              v: original.v\n            };\n          }\n          return false;\n        },\n\n        write: function(color) {\n          return {\n            h: color.h,\n            s: color.s,\n            v: color.v\n          };\n        }\n      }\n    }\n  }\n];\n\nlet result;\nlet toReturn;\n\nconst interpret = function() {\n  toReturn = false;\n\n  const original = arguments.length > 1 ? common.toArray(arguments) : arguments[0];\n  common.each(INTERPRETATIONS, function(family) {\n    if (family.litmus(original)) {\n      common.each(family.conversions, function(conversion, conversionName) {\n        result = conversion.read(original);\n\n        if (toReturn === false && result !== false) {\n          toReturn = result;\n          result.conversionName = conversionName;\n          result.conversion = conversion;\n          return common.BREAK;\n        }\n      });\n\n      return common.BREAK;\n    }\n  });\n\n  return toReturn;\n};\n\nexport default interpret;\n", "/**\n * dat-gui JavaScript Controller Library\n * https://github.com/dataarts/dat.gui\n *\n * Copyright 2011 Data Arts Team, Google Creative Lab\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n */\n\nlet tmpComponent;\n\nconst ColorMath = {\n  hsv_to_rgb: function(h, s, v) {\n    const hi = Math.floor(h / 60) % 6;\n\n    const f = h / 60 - Math.floor(h / 60);\n    const p = v * (1.0 - s);\n    const q = v * (1.0 - (f * s));\n    const t = v * (1.0 - ((1.0 - f) * s));\n\n    const c = [\n      [v, t, p],\n      [q, v, p],\n      [p, v, t],\n      [p, q, v],\n      [t, p, v],\n      [v, p, q]\n    ][hi];\n\n    return {\n      r: c[0] * 255,\n      g: c[1] * 255,\n      b: c[2] * 255\n    };\n  },\n\n  rgb_to_hsv: function(r, g, b) {\n    const min = Math.min(r, g, b);\n    const max = Math.max(r, g, b);\n    const delta = max - min;\n    let h;\n    let s;\n\n    if (max !== 0) {\n      s = delta / max;\n    } else {\n      return {\n        h: NaN,\n        s: 0,\n        v: 0\n      };\n    }\n\n    if (r === max) {\n      h = (g - b) / delta;\n    } else if (g === max) {\n      h = 2 + (b - r) / delta;\n    } else {\n      h = 4 + (r - g) / delta;\n    }\n    h /= 6;\n    if (h < 0) {\n      h += 1;\n    }\n\n    return {\n      h: h * 360,\n      s: s,\n      v: max / 255\n    };\n  },\n\n  rgb_to_hex: function(r, g, b) {\n    let hex = this.hex_with_component(0, 2, r);\n    hex = this.hex_with_component(hex, 1, g);\n    hex = this.hex_with_component(hex, 0, b);\n    return hex;\n  },\n\n  component_from_hex: function(hex, componentIndex) {\n    return (hex >> (componentIndex * 8)) & 0xFF;\n  },\n\n  hex_with_component: function(hex, componentIndex, value) {\n    return value << (tmpComponent = componentIndex * 8) | (hex & ~(0xFF << tmpComponent));\n  }\n};\n\nexport default ColorMath;\n", "/**\n * dat-gui JavaScript Controller Library\n * https://github.com/dataarts/dat.gui\n *\n * Copyright 2011 Data Arts Team, Google Creative Lab\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n */\n\nimport interpret from './interpret';\nimport math from './math';\nimport colorToString from './toString';\nimport common from '../utils/common';\n\nclass Color {\n  constructor() {\n    this.__state = interpret.apply(this, arguments);\n\n    if (this.__state === false) {\n      throw new Error('Failed to interpret color arguments');\n    }\n\n    this.__state.a = this.__state.a || 1;\n  }\n\n  toString() {\n    return colorToString(this);\n  }\n\n  toHexString() {\n    return colorToString(this, true);\n  }\n\n  toOriginal() {\n    return this.__state.conversion.write(this);\n  }\n}\n\nfunction defineRGBComponent(target, component, componentHexIndex) {\n  Object.defineProperty(target, component, {\n    get: function() {\n      if (this.__state.space === 'RGB') {\n        return this.__state[component];\n      }\n\n      Color.recalculateRGB(this, component, componentHexIndex);\n\n      return this.__state[component];\n    },\n\n    set: function(v) {\n      if (this.__state.space !== 'RGB') {\n        Color.recalculateRGB(this, component, componentHexIndex);\n        this.__state.space = 'RGB';\n      }\n\n      this.__state[component] = v;\n    }\n  });\n}\n\nfunction defineHSVComponent(target, component) {\n  Object.defineProperty(target, component, {\n    get: function() {\n      if (this.__state.space === 'HSV') {\n        return this.__state[component];\n      }\n\n      Color.recalculateHSV(this);\n\n      return this.__state[component];\n    },\n\n    set: function(v) {\n      if (this.__state.space !== 'HSV') {\n        Color.recalculateHSV(this);\n        this.__state.space = 'HSV';\n      }\n\n      this.__state[component] = v;\n    }\n  });\n}\n\n\nColor.recalculateRGB = function(color, component, componentHexIndex) {\n  if (color.__state.space === 'HEX') {\n    color.__state[component] = math.component_from_hex(color.__state.hex, componentHexIndex);\n  } else if (color.__state.space === 'HSV') {\n    common.extend(color.__state, math.hsv_to_rgb(color.__state.h, color.__state.s, color.__state.v));\n  } else {\n    throw new Error('Corrupted color state');\n  }\n};\n\nColor.recalculateHSV = function(color) {\n  const result = math.rgb_to_hsv(color.r, color.g, color.b);\n\n  common.extend(color.__state,\n    {\n      s: result.s,\n      v: result.v\n    });\n\n  if (!common.isNaN(result.h)) {\n    color.__state.h = result.h;\n  } else if (common.isUndefined(color.__state.h)) {\n    color.__state.h = 0;\n  }\n};\n\nColor.COMPONENTS = ['r', 'g', 'b', 'h', 's', 'v', 'hex', 'a'];\n\ndefineRGBComponent(Color.prototype, 'r', 2);\ndefineRGBComponent(Color.prototype, 'g', 1);\ndefineRGBComponent(Color.prototype, 'b', 0);\n\ndefineHSVComponent(Color.prototype, 'h');\ndefineHSVComponent(Color.prototype, 's');\ndefineHSVComponent(Color.prototype, 'v');\n\nObject.defineProperty(Color.prototype, 'a', {\n  get: function() {\n    return this.__state.a;\n  },\n\n  set: function(v) {\n    this.__state.a = v;\n  }\n});\n\nObject.defineProperty(Color.prototype, 'hex', {\n  get: function() {\n    if (this.__state.space !== 'HEX') {\n      this.__state.hex = math.rgb_to_hex(this.r, this.g, this.b);\n      this.__state.space = 'HEX';\n    }\n\n    return this.__state.hex;\n  },\n\n  set: function(v) {\n    this.__state.space = 'HEX';\n    this.__state.hex = v;\n  }\n});\n\nexport default Color;\n", "/**\n * dat-gui JavaScript Controller Library\n * https://github.com/dataarts/dat.gui\n *\n * Copyright 2011 Data Arts Team, Google Creative Lab\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n */\n\n/**\n * @class An \"abstract\" class that represents a given property of an object.\n *\n * @param {Object} object The object to be manipulated\n * @param {string} property The name of the property to be manipulated\n */\nclass Controller {\n  constructor(object, property) {\n    this.initialValue = object[property];\n\n    /**\n     * Those who extend this class will put their DOM elements in here.\n     * @type {DOMElement}\n     */\n    this.domElement = document.createElement('div');\n\n    /**\n     * The object to manipulate\n     * @type {Object}\n     */\n    this.object = object;\n\n    /**\n     * The name of the property to manipulate\n     * @type {String}\n     */\n    this.property = property;\n\n    /**\n     * The function to be called on change.\n     * @type {Function}\n     * @ignore\n     */\n    this.__onChange = undefined;\n\n    /**\n     * The function to be called on finishing change.\n     * @type {Function}\n     * @ignore\n     */\n    this.__onFinishChange = undefined;\n  }\n\n  /**\n   * Specify that a function fire every time someone changes the value with\n   * this Controller.\n   *\n   * @param {Function} fnc This function will be called whenever the value\n   * is modified via this Controller.\n   * @returns {Controller} this\n   */\n  onChange(fnc) {\n    this.__onChange = fnc;\n    return this;\n  }\n\n  /**\n   * Specify that a function fire every time someone \"finishes\" changing\n   * the value wih this Controller. Useful for values that change\n   * incrementally like numbers or strings.\n   *\n   * @param {Function} fnc This function will be called whenever\n   * someone \"finishes\" changing the value via this Controller.\n   * @returns {Controller} this\n   */\n  onFinishChange(fnc) {\n    this.__onFinishChange = fnc;\n    return this;\n  }\n\n  /**\n   * Change the value of <code>object[property]</code>\n   *\n   * @param {Object} newValue The new value of <code>object[property]</code>\n   */\n  setValue(newValue) {\n    this.object[this.property] = newValue;\n    if (this.__onChange) {\n      this.__onChange.call(this, newValue);\n    }\n\n    this.updateDisplay();\n    return this;\n  }\n\n  /**\n   * Gets the value of <code>object[property]</code>\n   *\n   * @returns {Object} The current value of <code>object[property]</code>\n   */\n  getValue() {\n    return this.object[this.property];\n  }\n\n  /**\n   * Refreshes the visual display of a Controller in order to keep sync\n   * with the object's current value.\n   * @returns {Controller} this\n   */\n  updateDisplay() {\n    return this;\n  }\n\n  /**\n   * @returns {boolean} true if the value has deviated from initialValue\n   */\n  isModified() {\n    return this.initialValue !== this.getValue();\n  }\n}\n\nexport default Controller;\n", "/**\n * dat-gui JavaScript Controller Library\n * https://github.com/dataarts/dat.gui\n *\n * Copyright 2011 Data Arts Team, Google Creative Lab\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n */\n\nimport common from '../utils/common';\n\nconst EVENT_MAP = {\n  HTMLEvents: ['change'],\n  MouseEvents: ['click', 'mousemove', 'mousedown', 'mouseup', 'mouseover'],\n  KeyboardEvents: ['keydown']\n};\n\nconst EVENT_MAP_INV = {};\ncommon.each(EVENT_MAP, function(v, k) {\n  common.each(v, function(e) {\n    EVENT_MAP_INV[e] = k;\n  });\n});\n\nconst CSS_VALUE_PIXELS = /(\\d+(\\.\\d+)?)px/;\n\nfunction cssValueToPixels(val) {\n  if (val === '0' || common.isUndefined(val)) {\n    return 0;\n  }\n\n  const match = val.match(CSS_VALUE_PIXELS);\n\n  if (!common.isNull(match)) {\n    return parseFloat(match[1]);\n  }\n\n  // TODO ...ems? %?\n\n  return 0;\n}\n\n/**\n * @namespace\n * @member dat.dom\n */\nconst dom = {\n\n  /**\n   *\n   * @param elem\n   * @param selectable\n   */\n  makeSelectable: function(elem, selectable) {\n    if (elem === undefined || elem.style === undefined) return;\n\n    elem.onselectstart = selectable ? function() {\n      return false;\n    } : function() {\n    };\n\n    elem.style.MozUserSelect = selectable ? 'auto' : 'none';\n    elem.style.KhtmlUserSelect = selectable ? 'auto' : 'none';\n    elem.unselectable = selectable ? 'on' : 'off';\n  },\n\n  /**\n   *\n   * @param elem\n   * @param horizontal\n   * @param vert\n   */\n  makeFullscreen: function(elem, hor, vert) {\n    let vertical = vert;\n    let horizontal = hor;\n\n    if (common.isUndefined(horizontal)) {\n      horizontal = true;\n    }\n\n    if (common.isUndefined(vertical)) {\n      vertical = true;\n    }\n\n    elem.style.position = 'absolute';\n\n    if (horizontal) {\n      elem.style.left = 0;\n      elem.style.right = 0;\n    }\n    if (vertical) {\n      elem.style.top = 0;\n      elem.style.bottom = 0;\n    }\n  },\n\n  /**\n   *\n   * @param elem\n   * @param eventType\n   * @param params\n   */\n  fakeEvent: function(elem, eventType, pars, aux) {\n    const params = pars || {};\n    const className = EVENT_MAP_INV[eventType];\n    if (!className) {\n      throw new Error('Event type ' + eventType + ' not supported.');\n    }\n    const evt = document.createEvent(className);\n    switch (className) {\n      case 'MouseEvents':\n      {\n        const clientX = params.x || params.clientX || 0;\n        const clientY = params.y || params.clientY || 0;\n        evt.initMouseEvent(eventType, params.bubbles || false,\n          params.cancelable || true, window, params.clickCount || 1,\n          0, // screen X\n          0, // screen Y\n          clientX, // client X\n          clientY, // client Y\n          false, false, false, false, 0, null);\n        break;\n      }\n      case 'KeyboardEvents':\n      {\n        const init = evt.initKeyboardEvent || evt.initKeyEvent; // webkit || moz\n        common.defaults(params, {\n          cancelable: true,\n          ctrlKey: false,\n          altKey: false,\n          shiftKey: false,\n          metaKey: false,\n          keyCode: undefined,\n          charCode: undefined\n        });\n        init(eventType, params.bubbles || false,\n          params.cancelable, window,\n          params.ctrlKey, params.altKey,\n          params.shiftKey, params.metaKey,\n          params.keyCode, params.charCode);\n        break;\n      }\n      default:\n      {\n        evt.initEvent(eventType, params.bubbles || false, params.cancelable || true);\n        break;\n      }\n    }\n    common.defaults(evt, aux);\n    elem.dispatchEvent(evt);\n  },\n\n  /**\n   *\n   * @param elem\n   * @param event\n   * @param func\n   * @param bool\n   */\n  bind: function(elem, event, func, newBool) {\n    const bool = newBool || false;\n    if (elem.addEventListener) {\n      elem.addEventListener(event, func, bool);\n    } else if (elem.attachEvent) {\n      elem.attachEvent('on' + event, func);\n    }\n    return dom;\n  },\n\n  /**\n   *\n   * @param elem\n   * @param event\n   * @param func\n   * @param bool\n   */\n  unbind: function(elem, event, func, newBool) {\n    const bool = newBool || false;\n    if (elem.removeEventListener) {\n      elem.removeEventListener(event, func, bool);\n    } else if (elem.detachEvent) {\n      elem.detachEvent('on' + event, func);\n    }\n    return dom;\n  },\n\n  /**\n   *\n   * @param elem\n   * @param className\n   */\n  addClass: function(elem, className) {\n    if (elem.className === undefined) {\n      elem.className = className;\n    } else if (elem.className !== className) {\n      const classes = elem.className.split(/ +/);\n      if (classes.indexOf(className) === -1) {\n        classes.push(className);\n        elem.className = classes.join(' ').replace(/^\\s+/, '').replace(/\\s+$/, '');\n      }\n    }\n    return dom;\n  },\n\n  /**\n   *\n   * @param elem\n   * @param className\n   */\n  removeClass: function(elem, className) {\n    if (className) {\n      if (elem.className === className) {\n        elem.removeAttribute('class');\n      } else {\n        const classes = elem.className.split(/ +/);\n        const index = classes.indexOf(className);\n        if (index !== -1) {\n          classes.splice(index, 1);\n          elem.className = classes.join(' ');\n        }\n      }\n    } else {\n      elem.className = undefined;\n    }\n    return dom;\n  },\n\n  hasClass: function(elem, className) {\n    return new RegExp('(?:^|\\\\s+)' + className + '(?:\\\\s+|$)').test(elem.className) || false;\n  },\n\n  /**\n   *\n   * @param elem\n   */\n  getWidth: function(elem) {\n    const style = getComputedStyle(elem);\n\n    return cssValueToPixels(style['border-left-width']) +\n      cssValueToPixels(style['border-right-width']) +\n      cssValueToPixels(style['padding-left']) +\n      cssValueToPixels(style['padding-right']) +\n      cssValueToPixels(style.width);\n  },\n\n  /**\n   *\n   * @param elem\n   */\n  getHeight: function(elem) {\n    const style = getComputedStyle(elem);\n\n    return cssValueToPixels(style['border-top-width']) +\n      cssValueToPixels(style['border-bottom-width']) +\n      cssValueToPixels(style['padding-top']) +\n      cssValueToPixels(style['padding-bottom']) +\n      cssValueToPixels(style.height);\n  },\n\n  /**\n   *\n   * @param el\n   */\n  getOffset: function(el) {\n    let elem = el;\n    const offset = { left: 0, top: 0 };\n    if (elem.offsetParent) {\n      do {\n        offset.left += elem.offsetLeft;\n        offset.top += elem.offsetTop;\n        elem = elem.offsetParent;\n      } while (elem);\n    }\n    return offset;\n  },\n\n  // http://stackoverflow.com/posts/2684561/revisions\n  /**\n   *\n   * @param elem\n   */\n  isActive: function(elem) {\n    return elem === document.activeElement && (elem.type || elem.href);\n  }\n\n};\n\nexport default dom;\n", "/**\n * dat-gui JavaScript Controller Library\n * https://github.com/dataarts/dat.gui\n *\n * Copyright 2011 Data Arts Team, Google Creative Lab\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n */\n\nimport Controller from './Controller';\nimport dom from '../dom/dom';\n\n/**\n * @class Provides a checkbox input to alter the boolean property of an object.\n *\n * @extends dat.controllers.Controller\n *\n * @param {Object} object The object to be manipulated\n * @param {string} property The name of the property to be manipulated\n */\nclass BooleanController extends Controller {\n  constructor(object, property) {\n    super(object, property);\n\n    const _this = this;\n    this.__prev = this.getValue();\n\n    this.__checkbox = document.createElement('input');\n    this.__checkbox.setAttribute('type', 'checkbox');\n\n    function onChange() {\n      _this.setValue(!_this.__prev);\n    }\n\n    dom.bind(this.__checkbox, 'change', onChange, false);\n\n    this.domElement.appendChild(this.__checkbox);\n\n    // Match original value\n    this.updateDisplay();\n  }\n\n  setValue(v) {\n    const toReturn = super.setValue(v);\n    if (this.__onFinishChange) {\n      this.__onFinishChange.call(this, this.getValue());\n    }\n    this.__prev = this.getValue();\n    return toReturn;\n  }\n\n  updateDisplay() {\n    if (this.getValue() === true) {\n      this.__checkbox.setAttribute('checked', 'checked');\n      this.__checkbox.checked = true;\n      this.__prev = true;\n    } else {\n      this.__checkbox.checked = false;\n      this.__prev = false;\n    }\n\n    return super.updateDisplay();\n  }\n}\n\nexport default BooleanController;\n", "/**\n * dat-gui JavaScript Controller Library\n * https://github.com/dataarts/dat.gui\n *\n * Copyright 2011 Data Arts Team, Google Creative Lab\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n */\n\nimport Controller from './Controller';\nimport dom from '../dom/dom';\nimport common from '../utils/common';\n\n/**\n * @class Provides a select input to alter the property of an object, using a\n * list of accepted values.\n *\n * @extends dat.controllers.Controller\n *\n * @param {Object} object The object to be manipulated\n * @param {string} property The name of the property to be manipulated\n * @param {Object|string[]} options A map of labels to acceptable values, or\n * a list of acceptable string values.\n */\nclass OptionController extends Controller {\n  constructor(object, property, opts) {\n    super(object, property);\n\n    let options = opts;\n\n    const _this = this;\n\n    /**\n     * The drop down menu\n     * @ignore\n     */\n    this.__select = document.createElement('select');\n\n    if (common.isArray(options)) {\n      const map = {};\n      common.each(options, function(element) {\n        map[element] = element;\n      });\n      options = map;\n    }\n\n    common.each(options, function(value, key) {\n      const opt = document.createElement('option');\n      opt.innerHTML = key;\n      opt.setAttribute('value', value);\n      _this.__select.appendChild(opt);\n    });\n\n    // Acknowledge original value\n    this.updateDisplay();\n\n    dom.bind(this.__select, 'change', function() {\n      const desiredValue = this.options[this.selectedIndex].value;\n      _this.setValue(desiredValue);\n    });\n\n    this.domElement.appendChild(this.__select);\n  }\n\n  setValue(v) {\n    const toReturn = super.setValue(v);\n\n    if (this.__onFinishChange) {\n      this.__onFinishChange.call(this, this.getValue());\n    }\n    return toReturn;\n  }\n\n  updateDisplay() {\n    if (dom.isActive(this.__select)) return this; // prevent number from updating if user is trying to manually update\n    this.__select.value = this.getValue();\n    return super.updateDisplay();\n  }\n}\n\nexport default OptionController;\n", "/**\n * dat-gui JavaScript Controller Library\n * https://github.com/dataarts/dat.gui\n *\n * Copyright 2011 Data Arts Team, Google Creative Lab\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n */\n\nimport Controller from './Controller';\nimport dom from '../dom/dom';\n\n/**\n * @class Provides a text input to alter the string property of an object.\n *\n * @extends dat.controllers.Controller\n *\n * @param {Object} object The object to be manipulated\n * @param {string} property The name of the property to be manipulated\n */\nclass StringController extends Controller {\n  constructor(object, property) {\n    super(object, property);\n\n    const _this = this;\n\n    function onChange() {\n      _this.setValue(_this.__input.value);\n    }\n\n    function onBlur() {\n      if (_this.__onFinishChange) {\n        _this.__onFinishChange.call(_this, _this.getValue());\n      }\n    }\n\n    this.__input = document.createElement('input');\n    this.__input.setAttribute('type', 'text');\n\n    dom.bind(this.__input, 'keyup', onChange);\n    dom.bind(this.__input, 'change', onChange);\n    dom.bind(this.__input, 'blur', onBlur);\n    dom.bind(this.__input, 'keydown', function(e) {\n      if (e.keyCode === 13) {\n        this.blur();\n      }\n    });\n\n    this.updateDisplay();\n\n    this.domElement.appendChild(this.__input);\n  }\n\n  updateDisplay() {\n    // Stops the caret from moving on account of:\n    // keyup -> setValue -> updateDisplay\n    if (!dom.isActive(this.__input)) {\n      this.__input.value = this.getValue();\n    }\n    return super.updateDisplay();\n  }\n}\n\nexport default StringController;\n", "/**\n * dat-gui JavaScript Controller Library\n * https://github.com/dataarts/dat.gui\n *\n * Copyright 2011 Data Arts Team, Google Creative Lab\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n */\n\nimport Controller from './Controller';\nimport common from '../utils/common';\n\nfunction numDecimals(x) {\n  const _x = x.toString();\n  if (_x.indexOf('.') > -1) {\n    return _x.length - _x.indexOf('.') - 1;\n  }\n\n  return 0;\n}\n\n/**\n * @class Represents a given property of an object that is a number.\n *\n * @extends dat.controllers.Controller\n *\n * @param {Object} object The object to be manipulated\n * @param {string} property The name of the property to be manipulated\n * @param {Object} [params] Optional parameters\n * @param {Number} [params.min] Minimum allowed value\n * @param {Number} [params.max] Maximum allowed value\n * @param {Number} [params.step] Increment by which to change value\n */\nclass NumberController extends Controller {\n  constructor(object, property, params) {\n    super(object, property);\n\n    const _params = params || {};\n\n    this.__min = _params.min;\n    this.__max = _params.max;\n    this.__step = _params.step;\n\n    if (common.isUndefined(this.__step)) {\n      if (this.initialValue === 0) {\n        this.__impliedStep = 1; // What are we, psychics?\n      } else {\n        // Hey Doug, check this out.\n        this.__impliedStep = Math.pow(10, Math.floor(Math.log(Math.abs(this.initialValue)) / Math.LN10)) / 10;\n      }\n    } else {\n      this.__impliedStep = this.__step;\n    }\n\n    this.__precision = numDecimals(this.__impliedStep);\n  }\n\n  setValue(v) {\n    let _v = v;\n\n    if (this.__min !== undefined && _v < this.__min) {\n      _v = this.__min;\n    } else if (this.__max !== undefined && _v > this.__max) {\n      _v = this.__max;\n    }\n\n    if (this.__step !== undefined && _v % this.__step !== 0) {\n      _v = Math.round(_v / this.__step) * this.__step;\n    }\n\n    return super.setValue(_v);\n  }\n\n  /**\n   * Specify a minimum value for <code>object[property]</code>.\n   *\n   * @param {Number} minValue The minimum value for\n   * <code>object[property]</code>\n   * @returns {dat.controllers.NumberController} this\n   */\n  min(minValue) {\n    this.__min = minValue;\n    return this;\n  }\n\n  /**\n   * Specify a maximum value for <code>object[property]</code>.\n   *\n   * @param {Number} maxValue The maximum value for\n   * <code>object[property]</code>\n   * @returns {dat.controllers.NumberController} this\n   */\n  max(maxValue) {\n    this.__max = maxValue;\n    return this;\n  }\n\n  /**\n   * Specify a step value that dat.controllers.NumberController\n   * increments by.\n   *\n   * @param {Number} stepValue The step value for\n   * dat.controllers.NumberController\n   * @default if minimum and maximum specified increment is 1% of the\n   * difference otherwise stepValue is 1\n   * @returns {dat.controllers.NumberController} this\n   */\n  step(stepValue) {\n    this.__step = stepValue;\n    this.__impliedStep = stepValue;\n    this.__precision = numDecimals(stepValue);\n    return this;\n  }\n}\n\nexport default NumberController;\n", "/**\n * dat-gui JavaScript Controller Library\n * https://github.com/dataarts/dat.gui\n *\n * Copyright 2011 Data Arts Team, Google Creative Lab\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n */\n\nimport NumberController from './NumberController';\nimport dom from '../dom/dom';\nimport common from '../utils/common';\n\nfunction roundToDecimal(value, decimals) {\n  const tenTo = Math.pow(10, decimals);\n  return Math.round(value * tenTo) / tenTo;\n}\n\n/**\n * @class Represents a given property of an object that is a number and\n * provides an input element with which to manipulate it.\n *\n * @extends dat.controllers.Controller\n * @extends dat.controllers.NumberController\n *\n * @param {Object} object The object to be manipulated\n * @param {string} property The name of the property to be manipulated\n * @param {Object} [params] Optional parameters\n * @param {Number} [params.min] Minimum allowed value\n * @param {Number} [params.max] Maximum allowed value\n * @param {Number} [params.step] Increment by which to change value\n */\nclass NumberControllerBox extends NumberController {\n  constructor(object, property, params) {\n    super(object, property, params);\n\n    this.__truncationSuspended = false;\n\n    const _this = this;\n\n    /**\n     * {Number} Previous mouse y position\n     * @ignore\n     */\n    let prevY;\n\n    function onChange() {\n      const attempted = parseFloat(_this.__input.value);\n      if (!common.isNaN(attempted)) {\n        _this.setValue(attempted);\n      }\n    }\n\n    function onFinish() {\n      if (_this.__onFinishChange) {\n        _this.__onFinishChange.call(_this, _this.getValue());\n      }\n    }\n\n    function onBlur() {\n      onFinish();\n    }\n\n    function onMouseDrag(e) {\n      const diff = prevY - e.clientY;\n      _this.setValue(_this.getValue() + diff * _this.__impliedStep);\n\n      prevY = e.clientY;\n    }\n\n    function onMouseUp() {\n      dom.unbind(window, 'mousemove', onMouseDrag);\n      dom.unbind(window, 'mouseup', onMouseUp);\n      onFinish();\n    }\n\n    function onMouseDown(e) {\n      dom.bind(window, 'mousemove', onMouseDrag);\n      dom.bind(window, 'mouseup', onMouseUp);\n      prevY = e.clientY;\n    }\n\n    this.__input = document.createElement('input');\n    this.__input.setAttribute('type', 'text');\n\n    // Makes it so manually specified values are not truncated.\n\n    dom.bind(this.__input, 'change', onChange);\n    dom.bind(this.__input, 'blur', onBlur);\n    dom.bind(this.__input, 'mousedown', onMouseDown);\n    dom.bind(this.__input, 'keydown', function(e) {\n      // When pressing enter, you can be as precise as you want.\n      if (e.keyCode === 13) {\n        _this.__truncationSuspended = true;\n        this.blur();\n        _this.__truncationSuspended = false;\n        onFinish();\n      }\n    });\n\n    this.updateDisplay();\n\n    this.domElement.appendChild(this.__input);\n  }\n\n  updateDisplay() {\n    this.__input.value = this.__truncationSuspended ? this.getValue() : roundToDecimal(this.getValue(), this.__precision);\n    return super.updateDisplay();\n  }\n}\n\nexport default NumberControllerBox;\n", "/**\n * dat-gui JavaScript Controller Library\n * https://github.com/dataarts/dat.gui\n *\n * Copyright 2011 Data Arts Team, Google Creative Lab\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n */\n\nimport NumberController from './NumberController';\nimport dom from '../dom/dom';\n\nfunction map(v, i1, i2, o1, o2) {\n  return o1 + (o2 - o1) * ((v - i1) / (i2 - i1));\n}\n\n/**\n * @class Represents a given property of an object that is a number, contains\n * a minimum and maximum, and provides a slider element with which to\n * manipulate it. It should be noted that the slider element is made up of\n * <code>&lt;div&gt;</code> tags, <strong>not</strong> the html5\n * <code>&lt;slider&gt;</code> element.\n *\n * @extends dat.controllers.Controller\n * @extends dat.controllers.NumberController\n *\n * @param {Object} object The object to be manipulated\n * @param {string} property The name of the property to be manipulated\n * @param {Number} minValue Minimum allowed value\n * @param {Number} maxValue Maximum allowed value\n * @param {Number} stepValue Increment by which to change value\n */\nclass NumberControllerSlider extends NumberController {\n  constructor(object, property, min, max, step) {\n    super(object, property, { min: min, max: max, step: step });\n\n    const _this = this;\n\n    this.__background = document.createElement('div');\n    this.__foreground = document.createElement('div');\n\n    dom.bind(this.__background, 'mousedown', onMouseDown);\n    dom.bind(this.__background, 'touchstart', onTouchStart);\n\n    dom.addClass(this.__background, 'slider');\n    dom.addClass(this.__foreground, 'slider-fg');\n\n    function onMouseDown(e) {\n      document.activeElement.blur();\n\n      dom.bind(window, 'mousemove', onMouseDrag);\n      dom.bind(window, 'mouseup', onMouseUp);\n\n      onMouseDrag(e);\n    }\n\n    function onMouseDrag(e) {\n      e.preventDefault();\n\n      const bgRect = _this.__background.getBoundingClientRect();\n\n      _this.setValue(\n        map(e.clientX, bgRect.left, bgRect.right, _this.__min, _this.__max)\n      );\n\n      return false;\n    }\n\n    function onMouseUp() {\n      dom.unbind(window, 'mousemove', onMouseDrag);\n      dom.unbind(window, 'mouseup', onMouseUp);\n      if (_this.__onFinishChange) {\n        _this.__onFinishChange.call(_this, _this.getValue());\n      }\n    }\n\n    function onTouchStart(e) {\n      if (e.touches.length !== 1) { return; }\n      dom.bind(window, 'touchmove', onTouchMove);\n      dom.bind(window, 'touchend', onTouchEnd);\n      onTouchMove(e);\n    }\n\n    function onTouchMove(e) {\n      const clientX = e.touches[0].clientX;\n      const bgRect = _this.__background.getBoundingClientRect();\n\n      _this.setValue(\n        map(clientX, bgRect.left, bgRect.right, _this.__min, _this.__max)\n      );\n    }\n\n    function onTouchEnd() {\n      dom.unbind(window, 'touchmove', onTouchMove);\n      dom.unbind(window, 'touchend', onTouchEnd);\n      if (_this.__onFinishChange) {\n        _this.__onFinishChange.call(_this, _this.getValue());\n      }\n    }\n\n    this.updateDisplay();\n\n    this.__background.appendChild(this.__foreground);\n    this.domElement.appendChild(this.__background);\n  }\n\n  updateDisplay() {\n    const pct = (this.getValue() - this.__min) / (this.__max - this.__min);\n    this.__foreground.style.width = pct * 100 + '%';\n    return super.updateDisplay();\n  }\n}\n\nexport default NumberControllerSlider;\n", "/**\n * dat-gui JavaScript Controller Library\n * https://github.com/dataarts/dat.gui\n *\n * Copyright 2011 Data Arts Team, Google Creative Lab\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n */\n\nimport Controller from './Controller';\nimport dom from '../dom/dom';\n\n/**\n * @class Provides a GUI interface to fire a specified method, a property of an object.\n *\n * @extends dat.controllers.Controller\n *\n * @param {Object} object The object to be manipulated\n * @param {string} property The name of the property to be manipulated\n */\nclass FunctionController extends Controller {\n  constructor(object, property, text) {\n    super(object, property);\n\n    const _this = this;\n\n    this.__button = document.createElement('div');\n    this.__button.innerHTML = text === undefined ? 'Fire' : text;\n\n    dom.bind(this.__button, 'click', function(e) {\n      e.preventDefault();\n      _this.fire();\n      return false;\n    });\n\n    dom.addClass(this.__button, 'button');\n\n    this.domElement.appendChild(this.__button);\n  }\n\n  fire() {\n    if (this.__onChange) {\n      this.__onChange.call(this);\n    }\n    this.getValue().call(this.object);\n    if (this.__onFinishChange) {\n      this.__onFinishChange.call(this, this.getValue());\n    }\n  }\n}\n\nexport default FunctionController;\n", "/**\n * dat-gui JavaScript Controller Library\n * https://github.com/dataarts/dat.gui\n *\n * Copyright 2011 Data Arts Team, Google Creative Lab\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n */\n\nimport Controller from './Controller';\nimport dom from '../dom/dom';\nimport Color from '../color/Color';\nimport interpret from '../color/interpret';\nimport common from '../utils/common';\n\n/**\n * @class Represents a given property of an object that is a color.\n * @param {Object} object\n * @param {string} property\n */\nclass ColorController extends Controller {\n  constructor(object, property) {\n    super(object, property);\n\n    this.__color = new Color(this.getValue());\n    this.__temp = new Color(0);\n\n    const _this = this;\n\n    this.domElement = document.createElement('div');\n\n    dom.makeSelectable(this.domElement, false);\n\n    this.__selector = document.createElement('div');\n    this.__selector.className = 'selector';\n\n    this.__saturation_field = document.createElement('div');\n    this.__saturation_field.className = 'saturation-field';\n\n    this.__field_knob = document.createElement('div');\n    this.__field_knob.className = 'field-knob';\n    this.__field_knob_border = '2px solid ';\n\n    this.__hue_knob = document.createElement('div');\n    this.__hue_knob.className = 'hue-knob';\n\n    this.__hue_field = document.createElement('div');\n    this.__hue_field.className = 'hue-field';\n\n    this.__input = document.createElement('input');\n    this.__input.type = 'text';\n    this.__input_textShadow = '0 1px 1px ';\n\n    dom.bind(this.__input, 'keydown', function(e) {\n      if (e.keyCode === 13) { // on enter\n        onBlur.call(this);\n      }\n    });\n\n    dom.bind(this.__input, 'blur', onBlur);\n\n    dom.bind(this.__selector, 'mousedown', function(/* e */) {\n      dom\n        .addClass(this, 'drag')\n        .bind(window, 'mouseup', function(/* e */) {\n          dom.removeClass(_this.__selector, 'drag');\n        });\n    });\n\n    dom.bind(this.__selector, 'touchstart', function(/* e */) {\n      dom\n        .addClass(this, 'drag')\n        .bind(window, 'touchend', function(/* e */) {\n          dom.removeClass(_this.__selector, 'drag');\n        });\n    });\n\n    const valueField = document.createElement('div');\n\n    common.extend(this.__selector.style, {\n      width: '122px',\n      height: '102px',\n      padding: '3px',\n      backgroundColor: '#222',\n      boxShadow: '0px 1px 3px rgba(0,0,0,0.3)'\n    });\n\n    common.extend(this.__field_knob.style, {\n      position: 'absolute',\n      width: '12px',\n      height: '12px',\n      border: this.__field_knob_border + (this.__color.v < 0.5 ? '#fff' : '#000'),\n      boxShadow: '0px 1px 3px rgba(0,0,0,0.5)',\n      borderRadius: '12px',\n      zIndex: 1\n    });\n\n    common.extend(this.__hue_knob.style, {\n      position: 'absolute',\n      width: '15px',\n      height: '2px',\n      borderRight: '4px solid #fff',\n      zIndex: 1\n    });\n\n    common.extend(this.__saturation_field.style, {\n      width: '100px',\n      height: '100px',\n      border: '1px solid #555',\n      marginRight: '3px',\n      display: 'inline-block',\n      cursor: 'pointer'\n    });\n\n    common.extend(valueField.style, {\n      width: '100%',\n      height: '100%',\n      background: 'none'\n    });\n\n    linearGradient(valueField, 'top', 'rgba(0,0,0,0)', '#000');\n\n    common.extend(this.__hue_field.style, {\n      width: '15px',\n      height: '100px',\n      border: '1px solid #555',\n      cursor: 'ns-resize',\n      position: 'absolute',\n      top: '3px',\n      right: '3px'\n    });\n\n    hueGradient(this.__hue_field);\n\n    common.extend(this.__input.style, {\n      outline: 'none',\n      //      width: '120px',\n      textAlign: 'center',\n      //      padding: '4px',\n      //      marginBottom: '6px',\n      color: '#fff',\n      border: 0,\n      fontWeight: 'bold',\n      textShadow: this.__input_textShadow + 'rgba(0,0,0,0.7)'\n    });\n\n    dom.bind(this.__saturation_field, 'mousedown', fieldDown);\n    dom.bind(this.__saturation_field, 'touchstart', fieldDown);\n\n    dom.bind(this.__field_knob, 'mousedown', fieldDown);\n    dom.bind(this.__field_knob, 'touchstart', fieldDown);\n\n    dom.bind(this.__hue_field, 'mousedown', fieldDownH);\n    dom.bind(this.__hue_field, 'touchstart', fieldDownH);\n\n    function fieldDown(e) {\n      setSV(e);\n      dom.bind(window, 'mousemove', setSV);\n      dom.bind(window, 'touchmove', setSV);\n      dom.bind(window, 'mouseup', fieldUpSV);\n      dom.bind(window, 'touchend', fieldUpSV);\n    }\n\n    function fieldDownH(e) {\n      setH(e);\n      dom.bind(window, 'mousemove', setH);\n      dom.bind(window, 'touchmove', setH);\n      dom.bind(window, 'mouseup', fieldUpH);\n      dom.bind(window, 'touchend', fieldUpH);\n    }\n\n    function fieldUpSV() {\n      dom.unbind(window, 'mousemove', setSV);\n      dom.unbind(window, 'touchmove', setSV);\n      dom.unbind(window, 'mouseup', fieldUpSV);\n      dom.unbind(window, 'touchend', fieldUpSV);\n      onFinish();\n    }\n\n    function fieldUpH() {\n      dom.unbind(window, 'mousemove', setH);\n      dom.unbind(window, 'touchmove', setH);\n      dom.unbind(window, 'mouseup', fieldUpH);\n      dom.unbind(window, 'touchend', fieldUpH);\n      onFinish();\n    }\n\n    function onBlur() {\n      const i = interpret(this.value);\n      if (i !== false) {\n        _this.__color.__state = i;\n        _this.setValue(_this.__color.toOriginal());\n      } else {\n        this.value = _this.__color.toString();\n      }\n    }\n\n    function onFinish() {\n      if (_this.__onFinishChange) {\n        _this.__onFinishChange.call(_this, _this.__color.toOriginal());\n      }\n    }\n\n    this.__saturation_field.appendChild(valueField);\n    this.__selector.appendChild(this.__field_knob);\n    this.__selector.appendChild(this.__saturation_field);\n    this.__selector.appendChild(this.__hue_field);\n    this.__hue_field.appendChild(this.__hue_knob);\n\n    this.domElement.appendChild(this.__input);\n    this.domElement.appendChild(this.__selector);\n\n    this.updateDisplay();\n\n    function setSV(e) {\n      if (e.type.indexOf('touch') === -1) { e.preventDefault(); }\n\n      const fieldRect = _this.__saturation_field.getBoundingClientRect();\n      const { clientX, clientY } = (e.touches && e.touches[0]) || e;\n      let s = (clientX - fieldRect.left) / (fieldRect.right - fieldRect.left);\n      let v = 1 - (clientY - fieldRect.top) / (fieldRect.bottom - fieldRect.top);\n\n      if (v > 1) {\n        v = 1;\n      } else if (v < 0) {\n        v = 0;\n      }\n\n      if (s > 1) {\n        s = 1;\n      } else if (s < 0) {\n        s = 0;\n      }\n\n      _this.__color.v = v;\n      _this.__color.s = s;\n\n      _this.setValue(_this.__color.toOriginal());\n\n\n      return false;\n    }\n\n    function setH(e) {\n      if (e.type.indexOf('touch') === -1) { e.preventDefault(); }\n\n      const fieldRect = _this.__hue_field.getBoundingClientRect();\n      const { clientY } = (e.touches && e.touches[0]) || e;\n      let h = 1 - (clientY - fieldRect.top) / (fieldRect.bottom - fieldRect.top);\n\n      if (h > 1) {\n        h = 1;\n      } else if (h < 0) {\n        h = 0;\n      }\n\n      _this.__color.h = h * 360;\n\n      _this.setValue(_this.__color.toOriginal());\n\n      return false;\n    }\n  }\n\n  updateDisplay() {\n    const i = interpret(this.getValue());\n\n    if (i !== false) {\n      let mismatch = false;\n\n      // Check for mismatch on the interpreted value.\n\n      common.each(Color.COMPONENTS, function(component) {\n        if (!common.isUndefined(i[component]) && !common.isUndefined(this.__color.__state[component]) &&\n          i[component] !== this.__color.__state[component]) {\n          mismatch = true;\n          return {}; // break\n        }\n      }, this);\n\n      // If nothing diverges, we keep our previous values\n      // for statefulness, otherwise we recalculate fresh\n      if (mismatch) {\n        common.extend(this.__color.__state, i);\n      }\n    }\n\n    common.extend(this.__temp.__state, this.__color.__state);\n\n    this.__temp.a = 1;\n\n    const flip = (this.__color.v < 0.5 || this.__color.s > 0.5) ? 255 : 0;\n    const _flip = 255 - flip;\n\n    common.extend(this.__field_knob.style, {\n      marginLeft: 100 * this.__color.s - 7 + 'px',\n      marginTop: 100 * (1 - this.__color.v) - 7 + 'px',\n      backgroundColor: this.__temp.toHexString(),\n      border: this.__field_knob_border + 'rgb(' + flip + ',' + flip + ',' + flip + ')'\n    });\n\n    this.__hue_knob.style.marginTop = (1 - this.__color.h / 360) * 100 + 'px';\n\n    this.__temp.s = 1;\n    this.__temp.v = 1;\n\n    linearGradient(this.__saturation_field, 'left', '#fff', this.__temp.toHexString());\n\n    this.__input.value = this.__color.toString();\n\n    common.extend(this.__input.style, {\n      backgroundColor: this.__color.toHexString(),\n      color: 'rgb(' + flip + ',' + flip + ',' + flip + ')',\n      textShadow: this.__input_textShadow + 'rgba(' + _flip + ',' + _flip + ',' + _flip + ',.7)'\n    });\n  }\n}\n\nconst vendors = ['-moz-', '-o-', '-webkit-', '-ms-', ''];\n\nfunction linearGradient(elem, x, a, b) {\n  elem.style.background = '';\n  common.each(vendors, function(vendor) {\n    elem.style.cssText += 'background: ' + vendor + 'linear-gradient(' + x + ', ' + a + ' 0%, ' + b + ' 100%); ';\n  });\n}\n\nfunction hueGradient(elem) {\n  elem.style.background = '';\n  elem.style.cssText += 'background: -moz-linear-gradient(top,  #ff0000 0%, #ff00ff 17%, #0000ff 34%, #00ffff 50%, #00ff00 67%, #ffff00 84%, #ff0000 100%);';\n  elem.style.cssText += 'background: -webkit-linear-gradient(top,  #ff0000 0%,#ff00ff 17%,#0000ff 34%,#00ffff 50%,#00ff00 67%,#ffff00 84%,#ff0000 100%);';\n  elem.style.cssText += 'background: -o-linear-gradient(top,  #ff0000 0%,#ff00ff 17%,#0000ff 34%,#00ffff 50%,#00ff00 67%,#ffff00 84%,#ff0000 100%);';\n  elem.style.cssText += 'background: -ms-linear-gradient(top,  #ff0000 0%,#ff00ff 17%,#0000ff 34%,#00ffff 50%,#00ff00 67%,#ffff00 84%,#ff0000 100%);';\n  elem.style.cssText += 'background: linear-gradient(top,  #ff0000 0%,#ff00ff 17%,#0000ff 34%,#00ffff 50%,#00ff00 67%,#ffff00 84%,#ff0000 100%);';\n}\n\nexport default ColorController;\n", "/**\n * dat-gui JavaScript Controller Library\n * https://github.com/dataarts/dat.gui\n *\n * Copyright 2011 Data Arts Team, Google Creative Lab\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n */\n\nconst css = {\n  load: function(url, indoc) {\n    const doc = indoc || document;\n    const link = doc.createElement('link');\n    link.type = 'text/css';\n    link.rel = 'stylesheet';\n    link.href = url;\n    doc.getElementsByTagName('head')[0].appendChild(link);\n  },\n\n  inject: function(cssContent, indoc) {\n    const doc = indoc || document;\n    const injected = document.createElement('style');\n    injected.type = 'text/css';\n    injected.innerHTML = cssContent;\n    const head = doc.getElementsByTagName('head')[0];\n    try {\n      head.appendChild(injected);\n    } catch (e) { // Unable to inject CSS, probably because of a Content Security Policy\n    }\n  }\n};\n\nexport default css;\n", "const saveDialogContents = `<div id=\"dg-save\" class=\"dg dialogue\">\n\n  Here's the new load parameter for your <code>GUI</code>'s constructor:\n\n  <textarea id=\"dg-new-constructor\"></textarea>\n\n  <div id=\"dg-save-locally\">\n\n    <input id=\"dg-local-storage\" type=\"checkbox\"/> Automatically save\n    values to <code>localStorage</code> on exit.\n\n    <div id=\"dg-local-explain\">The values saved to <code>localStorage</code> will\n      override those passed to <code>dat.GUI</code>'s constructor. This makes it\n      easier to work incrementally, but <code>localStorage</code> is fragile,\n      and your friends may not see the same values you do.\n\n    </div>\n\n  </div>\n\n</div>`;\n\nexport default saveDialogContents;\n", "/**\n * dat-gui JavaScript Controller Library\n * https://github.com/dataarts/dat.gui\n *\n * Copyright 2011 Data Arts Team, Google Creative Lab\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n */\n\nimport OptionController from './OptionController';\nimport NumberControllerBox from './NumberControllerBox';\nimport NumberControllerSlider from './NumberControllerSlider';\nimport StringController from './StringController';\nimport FunctionController from './FunctionController';\nimport BooleanController from './BooleanController';\nimport common from '../utils/common';\n\nconst ControllerFactory = function(object, property) {\n  const initialValue = object[property];\n\n  // Providing options?\n  if (common.isArray(arguments[2]) || common.isObject(arguments[2])) {\n    return new OptionController(object, property, arguments[2]);\n  }\n\n  // Providing a map?\n  if (common.isNumber(initialValue)) {\n    // Has min and max? (slider)\n    if (common.isNumber(arguments[2]) && common.isNumber(arguments[3])) {\n      // has step?\n      if (common.isNumber(arguments[4])) {\n        return new NumberControllerSlider(object, property,\n          arguments[2], arguments[3], arguments[4]);\n      }\n\n      return new NumberControllerSlider(object, property, arguments[2], arguments[3]);\n    }\n\n    // number box\n    if (common.isNumber(arguments[4])) { // has step\n      return new NumberControllerBox(object, property,\n        { min: arguments[2], max: arguments[3], step: arguments[4] });\n    }\n    return new NumberControllerBox(object, property, { min: arguments[2], max: arguments[3] });\n  }\n\n  if (common.isString(initialValue)) {\n    return new StringController(object, property);\n  }\n\n  if (common.isFunction(initialValue)) {\n    return new FunctionController(object, property, '');\n  }\n\n  if (common.isBoolean(initialValue)) {\n    return new BooleanController(object, property);\n  }\n\n  return null;\n};\n\nexport default ControllerFactory;\n", "/**\n * dat-gui JavaScript Controller Library\n * https://github.com/dataarts/dat.gui\n *\n * Copyright 2011 Data Arts Team, Google Creative Lab\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n */\n\nfunction requestAnimationFrame(callback) {\n  setTimeout(callback, 1000 / 60);\n}\n\nexport default window.requestAnimationFrame ||\n    window.webkitRequestAnimationFrame ||\n    window.mozRequestAnimationFrame ||\n    window.oRequestAnimationFrame ||\n    window.msRequestAnimationFrame ||\n    requestAnimationFrame;\n", "/**\n * dat-gui JavaScript Controller Library\n * https://github.com/dataarts/dat.gui\n *\n * Copyright 2011 Data Arts Team, Google Creative Lab\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n */\n\nimport dom from './dom';\nimport common from '../utils/common';\n\nclass CenteredDiv {\n  constructor() {\n    this.backgroundElement = document.createElement('div');\n    common.extend(this.backgroundElement.style, {\n      backgroundColor: 'rgba(0,0,0,0.8)',\n      top: 0,\n      left: 0,\n      display: 'none',\n      zIndex: '1000',\n      opacity: 0,\n      WebkitTransition: 'opacity 0.2s linear',\n      transition: 'opacity 0.2s linear'\n    });\n\n    dom.makeFullscreen(this.backgroundElement);\n    this.backgroundElement.style.position = 'fixed';\n\n    this.domElement = document.createElement('div');\n    common.extend(this.domElement.style, {\n      position: 'fixed',\n      display: 'none',\n      zIndex: '1001',\n      opacity: 0,\n      WebkitTransition: '-webkit-transform 0.2s ease-out, opacity 0.2s linear',\n      transition: 'transform 0.2s ease-out, opacity 0.2s linear'\n    });\n\n\n    document.body.appendChild(this.backgroundElement);\n    document.body.appendChild(this.domElement);\n\n    const _this = this;\n    dom.bind(this.backgroundElement, 'click', function() {\n      _this.hide();\n    });\n  }\n\n  show() {\n    const _this = this;\n\n    this.backgroundElement.style.display = 'block';\n\n    this.domElement.style.display = 'block';\n    this.domElement.style.opacity = 0;\n    //    this.domElement.style.top = '52%';\n    this.domElement.style.webkitTransform = 'scale(1.1)';\n\n    this.layout();\n\n    common.defer(function() {\n      _this.backgroundElement.style.opacity = 1;\n      _this.domElement.style.opacity = 1;\n      _this.domElement.style.webkitTransform = 'scale(1)';\n    });\n  }\n\n  /**\n   * Hide centered div\n   */\n  hide() {\n    const _this = this;\n\n    const hide = function() {\n      _this.domElement.style.display = 'none';\n      _this.backgroundElement.style.display = 'none';\n\n      dom.unbind(_this.domElement, 'webkitTransitionEnd', hide);\n      dom.unbind(_this.domElement, 'transitionend', hide);\n      dom.unbind(_this.domElement, 'oTransitionEnd', hide);\n    };\n\n    dom.bind(this.domElement, 'webkitTransitionEnd', hide);\n    dom.bind(this.domElement, 'transitionend', hide);\n    dom.bind(this.domElement, 'oTransitionEnd', hide);\n\n    this.backgroundElement.style.opacity = 0;\n    //    this.domElement.style.top = '48%';\n    this.domElement.style.opacity = 0;\n    this.domElement.style.webkitTransform = 'scale(1.1)';\n  }\n\n  layout() {\n    this.domElement.style.left = window.innerWidth / 2 - dom.getWidth(this.domElement) / 2 + 'px';\n    this.domElement.style.top = window.innerHeight / 2 - dom.getHeight(this.domElement) / 2 + 'px';\n  }\n}\n\nexport default CenteredDiv;\n", "/**\n * dat-gui JavaScript Controller Library\n * https://github.com/dataarts/dat.gui\n *\n * Copyright 2011 Data Arts Team, Google Creative Lab\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n */\n\nimport css from '../utils/css';\nimport saveDialogueContents from './saveDialogue.html';\nimport ControllerFactory from '../controllers/ControllerFactory';\nimport Controller from '../controllers/Controller';\nimport BooleanController from '../controllers/BooleanController';\nimport FunctionController from '../controllers/FunctionController';\nimport Number<PERSON>ontrollerBox from '../controllers/NumberControllerBox';\nimport NumberControllerSlider from '../controllers/NumberControllerSlider';\nimport ColorController from '../controllers/ColorController';\nimport requestAnimationFrame from '../utils/requestAnimationFrame';\nimport CenteredDiv from '../dom/CenteredDiv';\nimport dom from '../dom/dom';\nimport common from '../utils/common';\n\nimport styleSheet from './style.scss'; // CSS to embed in build\n\ncss.inject(styleSheet);\n\n/** @ignore Outer-most className for GUI's */\nconst CSS_NAMESPACE = 'dg';\n\nconst HIDE_KEY_CODE = 72;\n\n/** @ignore The only value shared between the JS and SCSS. Use caution. */\nconst CLOSE_BUTTON_HEIGHT = 20;\n\nconst DEFAULT_DEFAULT_PRESET_NAME = 'Default';\n\nconst SUPPORTS_LOCAL_STORAGE = (function() {\n  try {\n    return !!window.localStorage;\n  } catch (e) {\n    return false;\n  }\n}());\n\nlet SAVE_DIALOGUE;\n\n/** @ignore Have we yet to create an autoPlace GUI? */\nlet autoPlaceVirgin = true;\n\n/** @ignore Fixed position div that auto place GUI's go inside */\nlet autoPlaceContainer;\n\n/** @ignore Are we hiding the GUI's ? */\nlet hide = false;\n\n/** @private GUI's which should be hidden */\nconst hideableGuis = [];\n\n/**\n * @class A lightweight controller library for JavaScript. It allows you to easily\n * manipulate variables and fire functions on the fly.\n *\n * @typicalname gui\n *\n * @example\n * // Creating a GUI with options.\n * var gui = new dat.GUI({name: 'My GUI'});\n *\n * @example\n * // Creating a GUI and a subfolder.\n * var gui = new dat.GUI();\n * var folder1 = gui.addFolder('Flow Field');\n *\n * @param {Object} [params]\n * @param {String} [params.name] The name of this GUI.\n * @param {Object} [params.load] JSON object representing the saved state of\n * this GUI.\n * @param {dat.gui.GUI} [params.parent] The GUI I'm nested in.\n * @param {Boolean} [params.autoPlace=true]\n * @param {Boolean} [params.hideable=true] If true, GUI is shown/hidden by <kbd>h</kbd> keypress.\n * @param {Boolean} [params.closed=false] If true, starts closed\n * @param {Boolean} [params.closeOnTop=false] If true, close/open button shows on top of the GUI\n */\nconst GUI = function(pars) {\n  const _this = this;\n\n  let params = pars || {};\n\n  /**\n   * Outermost DOM Element\n   * @type {DOMElement}\n   */\n  this.domElement = document.createElement('div');\n  this.__ul = document.createElement('ul');\n  this.domElement.appendChild(this.__ul);\n\n  dom.addClass(this.domElement, CSS_NAMESPACE);\n\n  /**\n   * Nested GUI's by name\n   * @ignore\n   */\n  this.__folders = {};\n\n  this.__controllers = [];\n\n  /**\n   * List of objects I'm remembering for save, only used in top level GUI\n   * @ignore\n   */\n  this.__rememberedObjects = [];\n\n  /**\n   * Maps the index of remembered objects to a map of controllers, only used\n   * in top level GUI.\n   *\n   * @private\n   * @ignore\n   *\n   * @example\n   * [\n   *  {\n     *    propertyName: Controller,\n     *    anotherPropertyName: Controller\n     *  },\n   *  {\n     *    propertyName: Controller\n     *  }\n   * ]\n   */\n  this.__rememberedObjectIndecesToControllers = [];\n\n  this.__listening = [];\n\n  // Default parameters\n  params = common.defaults(params, {\n    closeOnTop: false,\n    autoPlace: true,\n    width: GUI.DEFAULT_WIDTH\n  });\n\n  params = common.defaults(params, {\n    resizable: params.autoPlace,\n    hideable: params.autoPlace\n  });\n\n  if (!common.isUndefined(params.load)) {\n    // Explicit preset\n    if (params.preset) {\n      params.load.preset = params.preset;\n    }\n  } else {\n    params.load = { preset: DEFAULT_DEFAULT_PRESET_NAME };\n  }\n\n  if (common.isUndefined(params.parent) && params.hideable) {\n    hideableGuis.push(this);\n  }\n\n  // Only root level GUI's are resizable.\n  params.resizable = common.isUndefined(params.parent) && params.resizable;\n\n  if (params.autoPlace && common.isUndefined(params.scrollable)) {\n    params.scrollable = true;\n  }\n  //    params.scrollable = common.isUndefined(params.parent) && params.scrollable === true;\n\n  // Not part of params because I don't want people passing this in via\n  // constructor. Should be a 'remembered' value.\n  let useLocalStorage =\n    SUPPORTS_LOCAL_STORAGE &&\n    localStorage.getItem(getLocalStorageHash(this, 'isLocal')) === 'true';\n\n  let saveToLocalStorage;\n  let titleRow;\n\n  Object.defineProperties(this,\n    /** @lends GUI.prototype */\n    {\n      /**\n       * The parent <code>GUI</code>\n       * @type dat.gui.GUI\n       */\n      parent: {\n        get: function() {\n          return params.parent;\n        }\n      },\n\n      scrollable: {\n        get: function() {\n          return params.scrollable;\n        }\n      },\n\n      /**\n       * Handles <code>GUI</code>'s element placement for you\n       * @type Boolean\n       */\n      autoPlace: {\n        get: function() {\n          return params.autoPlace;\n        }\n      },\n\n      /**\n       * Handles <code>GUI</code>'s position of open/close button\n       * @type Boolean\n       */\n      closeOnTop: {\n        get: function() {\n          return params.closeOnTop;\n        }\n      },\n\n      /**\n       * The identifier for a set of saved values\n       * @type String\n       */\n      preset: {\n        get: function() {\n          if (_this.parent) {\n            return _this.getRoot().preset;\n          }\n\n          return params.load.preset;\n        },\n\n        set: function(v) {\n          if (_this.parent) {\n            _this.getRoot().preset = v;\n          } else {\n            params.load.preset = v;\n          }\n          setPresetSelectIndex(this);\n          _this.revert();\n        }\n      },\n\n      /**\n       * The width of <code>GUI</code> element\n       * @type Number\n       */\n      width: {\n        get: function() {\n          return params.width;\n        },\n        set: function(v) {\n          params.width = v;\n          setWidth(_this, v);\n        }\n      },\n\n      /**\n       * The name of <code>GUI</code>. Used for folders. i.e\n       * a folder's name\n       * @type String\n       */\n      name: {\n        get: function() {\n          return params.name;\n        },\n        set: function(v) {\n          // TODO Check for collisions among sibling folders\n          params.name = v;\n          if (titleRow) {\n            titleRow.innerHTML = params.name;\n          }\n        }\n      },\n\n      /**\n       * Whether the <code>GUI</code> is collapsed or not\n       * @type Boolean\n       */\n      closed: {\n        get: function() {\n          return params.closed;\n        },\n        set: function(v) {\n          params.closed = v;\n          if (params.closed) {\n            dom.addClass(_this.__ul, GUI.CLASS_CLOSED);\n          } else {\n            dom.removeClass(_this.__ul, GUI.CLASS_CLOSED);\n          }\n          // For browsers that aren't going to respect the CSS transition,\n          // Lets just check our height against the window height right off\n          // the bat.\n          this.onResize();\n\n          if (_this.__closeButton) {\n            _this.__closeButton.innerHTML = v ? GUI.TEXT_OPEN : GUI.TEXT_CLOSED;\n          }\n        }\n      },\n\n      /**\n       * Contains all presets\n       * @type Object\n       */\n      load: {\n        get: function() {\n          return params.load;\n        }\n      },\n\n      /**\n       * Determines whether or not to use <a href=\"https://developer.mozilla.org/en/DOM/Storage#localStorage\">localStorage</a> as the means for\n       * <code>remember</code>ing\n       * @type Boolean\n       */\n      useLocalStorage: {\n\n        get: function() {\n          return useLocalStorage;\n        },\n        set: function(bool) {\n          if (SUPPORTS_LOCAL_STORAGE) {\n            useLocalStorage = bool;\n            if (bool) {\n              dom.bind(window, 'unload', saveToLocalStorage);\n            } else {\n              dom.unbind(window, 'unload', saveToLocalStorage);\n            }\n            localStorage.setItem(getLocalStorageHash(_this, 'isLocal'), bool);\n          }\n        }\n      }\n    });\n\n  // Are we a root level GUI?\n  if (common.isUndefined(params.parent)) {\n    this.closed = params.closed || false;\n\n    dom.addClass(this.domElement, GUI.CLASS_MAIN);\n    dom.makeSelectable(this.domElement, false);\n\n    // Are we supposed to be loading locally?\n    if (SUPPORTS_LOCAL_STORAGE) {\n      if (useLocalStorage) {\n        _this.useLocalStorage = true;\n\n        const savedGui = localStorage.getItem(getLocalStorageHash(this, 'gui'));\n\n        if (savedGui) {\n          params.load = JSON.parse(savedGui);\n        }\n      }\n    }\n\n    this.__closeButton = document.createElement('div');\n    this.__closeButton.innerHTML = GUI.TEXT_CLOSED;\n    dom.addClass(this.__closeButton, GUI.CLASS_CLOSE_BUTTON);\n    if (params.closeOnTop) {\n      dom.addClass(this.__closeButton, GUI.CLASS_CLOSE_TOP);\n      this.domElement.insertBefore(this.__closeButton, this.domElement.childNodes[0]);\n    } else {\n      dom.addClass(this.__closeButton, GUI.CLASS_CLOSE_BOTTOM);\n      this.domElement.appendChild(this.__closeButton);\n    }\n\n    dom.bind(this.__closeButton, 'click', function() {\n      _this.closed = !_this.closed;\n    });\n    // Oh, you're a nested GUI!\n  } else {\n    if (params.closed === undefined) {\n      params.closed = true;\n    }\n\n    const titleRowName = document.createTextNode(params.name);\n    dom.addClass(titleRowName, 'controller-name');\n\n    titleRow = addRow(_this, titleRowName);\n\n    const onClickTitle = function(e) {\n      e.preventDefault();\n      _this.closed = !_this.closed;\n      return false;\n    };\n\n    dom.addClass(this.__ul, GUI.CLASS_CLOSED);\n\n    dom.addClass(titleRow, 'title');\n    dom.bind(titleRow, 'click', onClickTitle);\n\n    if (!params.closed) {\n      this.closed = false;\n    }\n  }\n\n  if (params.autoPlace) {\n    if (common.isUndefined(params.parent)) {\n      if (autoPlaceVirgin) {\n        autoPlaceContainer = document.createElement('div');\n        dom.addClass(autoPlaceContainer, CSS_NAMESPACE);\n        dom.addClass(autoPlaceContainer, GUI.CLASS_AUTO_PLACE_CONTAINER);\n        document.body.appendChild(autoPlaceContainer);\n        autoPlaceVirgin = false;\n      }\n\n      // Put it in the dom for you.\n      autoPlaceContainer.appendChild(this.domElement);\n\n      // Apply the auto styles\n      dom.addClass(this.domElement, GUI.CLASS_AUTO_PLACE);\n    }\n\n\n    // Make it not elastic.\n    if (!this.parent) {\n      setWidth(_this, params.width);\n    }\n  }\n\n  this.__resizeHandler = function() {\n    _this.onResizeDebounced();\n  };\n\n  dom.bind(window, 'resize', this.__resizeHandler);\n  dom.bind(this.__ul, 'webkitTransitionEnd', this.__resizeHandler);\n  dom.bind(this.__ul, 'transitionend', this.__resizeHandler);\n  dom.bind(this.__ul, 'oTransitionEnd', this.__resizeHandler);\n  this.onResize();\n\n  if (params.resizable) {\n    addResizeHandle(this);\n  }\n\n  saveToLocalStorage = function() {\n    if (SUPPORTS_LOCAL_STORAGE && localStorage.getItem(getLocalStorageHash(_this, 'isLocal')) === 'true') {\n      localStorage.setItem(getLocalStorageHash(_this, 'gui'), JSON.stringify(_this.getSaveObject()));\n    }\n  };\n\n  // expose this method publicly\n  this.saveToLocalStorageIfPossible = saveToLocalStorage;\n\n  function resetWidth() {\n    const root = _this.getRoot();\n    root.width += 1;\n    common.defer(function() {\n      root.width -= 1;\n    });\n  }\n\n  if (!params.parent) {\n    resetWidth();\n  }\n};\n\nGUI.toggleHide = function() {\n  hide = !hide;\n  common.each(hideableGuis, function(gui) {\n    gui.domElement.style.display = hide ? 'none' : '';\n  });\n};\n\nGUI.CLASS_AUTO_PLACE = 'a';\nGUI.CLASS_AUTO_PLACE_CONTAINER = 'ac';\nGUI.CLASS_MAIN = 'main';\nGUI.CLASS_CONTROLLER_ROW = 'cr';\nGUI.CLASS_TOO_TALL = 'taller-than-window';\nGUI.CLASS_CLOSED = 'closed';\nGUI.CLASS_CLOSE_BUTTON = 'close-button';\nGUI.CLASS_CLOSE_TOP = 'close-top';\nGUI.CLASS_CLOSE_BOTTOM = 'close-bottom';\nGUI.CLASS_DRAG = 'drag';\n\nGUI.DEFAULT_WIDTH = 245;\nGUI.TEXT_CLOSED = 'Close Controls';\nGUI.TEXT_OPEN = 'Open Controls';\n\nGUI._keydownHandler = function(e) {\n  if (document.activeElement.type !== 'text' &&\n    (e.which === HIDE_KEY_CODE || e.keyCode === HIDE_KEY_CODE)) {\n    GUI.toggleHide();\n  }\n};\ndom.bind(window, 'keydown', GUI._keydownHandler, false);\n\ncommon.extend(\n  GUI.prototype,\n\n  /** @lends GUI.prototype */\n  {\n\n    /**\n     * Adds a new {@link Controller} to the GUI. The type of controller created\n     * is inferred from the initial value of <code>object[property]</code>. For\n     * color properties, see {@link addColor}.\n     *\n     * @param {Object} object The object to be manipulated\n     * @param {String} property The name of the property to be manipulated\n     * @param {Number} [min] Minimum allowed value\n     * @param {Number} [max] Maximum allowed value\n     * @param {Number} [step] Increment by which to change value\n     * @returns {Controller} The controller that was added to the GUI.\n     * @instance\n     *\n     * @example\n     * // Add a string controller.\n     * var person = {name: 'Sam'};\n     * gui.add(person, 'name');\n     *\n     * @example\n     * // Add a number controller slider.\n     * var person = {age: 45};\n     * gui.add(person, 'age', 0, 100);\n     */\n    add: function(object, property) {\n      return add(\n        this,\n        object,\n        property,\n        {\n          factoryArgs: Array.prototype.slice.call(arguments, 2)\n        }\n      );\n    },\n\n    /**\n     * Adds a new color controller to the GUI.\n     *\n     * @param object\n     * @param property\n     * @returns {Controller} The controller that was added to the GUI.\n     * @instance\n     *\n     * @example\n     * var palette = {\n     *   color1: '#FF0000', // CSS string\n     *   color2: [ 0, 128, 255 ], // RGB array\n     *   color3: [ 0, 128, 255, 0.3 ], // RGB with alpha\n     *   color4: { h: 350, s: 0.9, v: 0.3 } // Hue, saturation, value\n     * };\n     * gui.addColor(palette, 'color1');\n     * gui.addColor(palette, 'color2');\n     * gui.addColor(palette, 'color3');\n     * gui.addColor(palette, 'color4');\n     */\n    addColor: function(object, property) {\n      return add(\n        this,\n        object,\n        property,\n        {\n          color: true\n        }\n      );\n    },\n\n    /**\n     * Removes the given controller from the GUI.\n     * @param {Controller} controller\n     * @instance\n     */\n    remove: function(controller) {\n      // TODO listening?\n      this.__ul.removeChild(controller.__li);\n      this.__controllers.splice(this.__controllers.indexOf(controller), 1);\n      const _this = this;\n      common.defer(function() {\n        _this.onResize();\n      });\n    },\n\n    /**\n     * Removes the root GUI from the document and unbinds all event listeners.\n     * For subfolders, use `gui.removeFolder(folder)` instead.\n     * @instance\n     */\n    destroy: function() {\n      if (this.parent) {\n        throw new Error(\n          'Only the root GUI should be removed with .destroy(). ' +\n          'For subfolders, use gui.removeFolder(folder) instead.'\n        );\n      }\n\n      if (this.autoPlace) {\n        autoPlaceContainer.removeChild(this.domElement);\n      }\n\n      const _this = this;\n      common.each(this.__folders, function(subfolder) {\n        _this.removeFolder(subfolder);\n      });\n\n      dom.unbind(window, 'keydown', GUI._keydownHandler, false);\n\n      removeListeners(this);\n    },\n\n    /**\n     * Creates a new subfolder GUI instance.\n     * @param name\n     * @returns {dat.gui.GUI} The new folder.\n     * @throws {Error} if this GUI already has a folder by the specified\n     * name\n     * @instance\n     */\n    addFolder: function(name) {\n      // We have to prevent collisions on names in order to have a key\n      // by which to remember saved values\n      if (this.__folders[name] !== undefined) {\n        throw new Error('You already have a folder in this GUI by the' +\n          ' name \"' + name + '\"');\n      }\n\n      const newGuiParams = { name: name, parent: this };\n\n      // We need to pass down the autoPlace trait so that we can\n      // attach event listeners to open/close folder actions to\n      // ensure that a scrollbar appears if the window is too short.\n      newGuiParams.autoPlace = this.autoPlace;\n\n      // Do we have saved appearance data for this folder?\n      if (this.load && // Anything loaded?\n        this.load.folders && // Was my parent a dead-end?\n        this.load.folders[name]) { // Did daddy remember me?\n        // Start me closed if I was closed\n        newGuiParams.closed = this.load.folders[name].closed;\n\n        // Pass down the loaded data\n        newGuiParams.load = this.load.folders[name];\n      }\n\n      const gui = new GUI(newGuiParams);\n      this.__folders[name] = gui;\n\n      const li = addRow(this, gui.domElement);\n      dom.addClass(li, 'folder');\n      return gui;\n    },\n\n    /**\n     * Removes a subfolder GUI instance.\n     * @param {dat.gui.GUI} folder The folder to remove.\n     * @instance\n     */\n    removeFolder: function(folder) {\n      this.__ul.removeChild(folder.domElement.parentElement);\n\n      delete this.__folders[folder.name];\n\n      // Do we have saved appearance data for this folder?\n      if (this.load && // Anything loaded?\n        this.load.folders && // Was my parent a dead-end?\n        this.load.folders[folder.name]) {\n        delete this.load.folders[folder.name];\n      }\n\n      removeListeners(folder);\n\n      const _this = this;\n\n      common.each(folder.__folders, function(subfolder) {\n        folder.removeFolder(subfolder);\n      });\n\n      common.defer(function() {\n        _this.onResize();\n      });\n    },\n\n    /**\n     * Opens the GUI.\n     */\n    open: function() {\n      this.closed = false;\n    },\n\n    /**\n     * Closes the GUI.\n     */\n    close: function() {\n      this.closed = true;\n    },\n\n    /**\n    * Hides the GUI.\n    */\n    hide: function() {\n      this.domElement.style.display = 'none';\n    },\n\n    /**\n    * Shows the GUI.\n    */\n    show: function() {\n      this.domElement.style.display = '';\n    },\n\n\n    onResize: function() {\n      // we debounce this function to prevent performance issues when rotating on tablet/mobile\n      const root = this.getRoot();\n      if (root.scrollable) {\n        const top = dom.getOffset(root.__ul).top;\n        let h = 0;\n\n        common.each(root.__ul.childNodes, function(node) {\n          if (!(root.autoPlace && node === root.__save_row)) {\n            h += dom.getHeight(node);\n          }\n        });\n\n        if (window.innerHeight - top - CLOSE_BUTTON_HEIGHT < h) {\n          dom.addClass(root.domElement, GUI.CLASS_TOO_TALL);\n          root.__ul.style.height = window.innerHeight - top - CLOSE_BUTTON_HEIGHT + 'px';\n        } else {\n          dom.removeClass(root.domElement, GUI.CLASS_TOO_TALL);\n          root.__ul.style.height = 'auto';\n        }\n      }\n\n      if (root.__resize_handle) {\n        common.defer(function() {\n          root.__resize_handle.style.height = root.__ul.offsetHeight + 'px';\n        });\n      }\n\n      if (root.__closeButton) {\n        root.__closeButton.style.width = root.width + 'px';\n      }\n    },\n\n    onResizeDebounced: common.debounce(function() { this.onResize(); }, 50),\n\n    /**\n     * Mark objects for saving. The order of these objects cannot change as\n     * the GUI grows. When remembering new objects, append them to the end\n     * of the list.\n     *\n     * @param {...Object} objects\n     * @throws {Error} if not called on a top level GUI.\n     * @instance\n     * @ignore\n     */\n    remember: function() {\n      if (common.isUndefined(SAVE_DIALOGUE)) {\n        SAVE_DIALOGUE = new CenteredDiv();\n        SAVE_DIALOGUE.domElement.innerHTML = saveDialogueContents;\n      }\n\n      if (this.parent) {\n        throw new Error('You can only call remember on a top level GUI.');\n      }\n\n      const _this = this;\n\n      common.each(Array.prototype.slice.call(arguments), function(object) {\n        if (_this.__rememberedObjects.length === 0) {\n          addSaveMenu(_this);\n        }\n        if (_this.__rememberedObjects.indexOf(object) === -1) {\n          _this.__rememberedObjects.push(object);\n        }\n      });\n\n      if (this.autoPlace) {\n        // Set save row width\n        setWidth(this, this.width);\n      }\n    },\n\n    /**\n     * @returns {dat.gui.GUI} the topmost parent GUI of a nested GUI.\n     * @instance\n     */\n    getRoot: function() {\n      let gui = this;\n      while (gui.parent) {\n        gui = gui.parent;\n      }\n      return gui;\n    },\n\n    /**\n     * @returns {Object} a JSON object representing the current state of\n     * this GUI as well as its remembered properties.\n     * @instance\n     */\n    getSaveObject: function() {\n      const toReturn = this.load;\n      toReturn.closed = this.closed;\n\n      // Am I remembering any values?\n      if (this.__rememberedObjects.length > 0) {\n        toReturn.preset = this.preset;\n\n        if (!toReturn.remembered) {\n          toReturn.remembered = {};\n        }\n\n        toReturn.remembered[this.preset] = getCurrentPreset(this);\n      }\n\n      toReturn.folders = {};\n      common.each(this.__folders, function(element, key) {\n        toReturn.folders[key] = element.getSaveObject();\n      });\n\n      return toReturn;\n    },\n\n    save: function() {\n      if (!this.load.remembered) {\n        this.load.remembered = {};\n      }\n\n      this.load.remembered[this.preset] = getCurrentPreset(this);\n      markPresetModified(this, false);\n      this.saveToLocalStorageIfPossible();\n    },\n\n    saveAs: function(presetName) {\n      if (!this.load.remembered) {\n        // Retain default values upon first save\n        this.load.remembered = {};\n        this.load.remembered[DEFAULT_DEFAULT_PRESET_NAME] = getCurrentPreset(this, true);\n      }\n\n      this.load.remembered[presetName] = getCurrentPreset(this);\n      this.preset = presetName;\n      addPresetOption(this, presetName, true);\n      this.saveToLocalStorageIfPossible();\n    },\n\n    revert: function(gui) {\n      common.each(this.__controllers, function(controller) {\n        // Make revert work on Default.\n        if (!this.getRoot().load.remembered) {\n          controller.setValue(controller.initialValue);\n        } else {\n          recallSavedValue(gui || this.getRoot(), controller);\n        }\n\n        // fire onFinishChange callback\n        if (controller.__onFinishChange) {\n          controller.__onFinishChange.call(controller, controller.getValue());\n        }\n      }, this);\n\n      common.each(this.__folders, function(folder) {\n        folder.revert(folder);\n      });\n\n      if (!gui) {\n        markPresetModified(this.getRoot(), false);\n      }\n    },\n\n    listen: function(controller) {\n      const init = this.__listening.length === 0;\n      this.__listening.push(controller);\n      if (init) {\n        updateDisplays(this.__listening);\n      }\n    },\n\n    updateDisplay: function() {\n      common.each(this.__controllers, function(controller) {\n        controller.updateDisplay();\n      });\n      common.each(this.__folders, function(folder) {\n        folder.updateDisplay();\n      });\n    }\n  }\n);\n\n/**\n * Add a row to the end of the GUI or before another row.\n *\n * @param gui\n * @param [newDom] If specified, inserts the dom content in the new row\n * @param [liBefore] If specified, places the new row before another row\n *\n * @ignore\n */\nfunction addRow(gui, newDom, liBefore) {\n  const li = document.createElement('li');\n  if (newDom) {\n    li.appendChild(newDom);\n  }\n\n  if (liBefore) {\n    gui.__ul.insertBefore(li, liBefore);\n  } else {\n    gui.__ul.appendChild(li);\n  }\n  gui.onResize();\n  return li;\n}\n\nfunction removeListeners(gui) {\n  dom.unbind(window, 'resize', gui.__resizeHandler);\n\n  if (gui.saveToLocalStorageIfPossible) {\n    dom.unbind(window, 'unload', gui.saveToLocalStorageIfPossible);\n  }\n}\n\nfunction markPresetModified(gui, modified) {\n  const opt = gui.__preset_select[gui.__preset_select.selectedIndex];\n\n  if (modified) {\n    opt.innerHTML = opt.value + '*';\n  } else {\n    opt.innerHTML = opt.value;\n  }\n}\n\nfunction augmentController(gui, li, controller) {\n  controller.__li = li;\n  controller.__gui = gui;\n\n  common.extend(controller, /** @lends Controller.prototype */ {\n    /**\n     * @param  {Array|Object} options\n     * @return {Controller}\n     */\n    options: function(options) {\n      if (arguments.length > 1) {\n        const nextSibling = controller.__li.nextElementSibling;\n        controller.remove();\n\n        return add(\n          gui,\n          controller.object,\n          controller.property,\n          {\n            before: nextSibling,\n            factoryArgs: [common.toArray(arguments)]\n          }\n        );\n      }\n\n      if (common.isArray(options) || common.isObject(options)) {\n        const nextSibling = controller.__li.nextElementSibling;\n        controller.remove();\n\n        return add(\n          gui,\n          controller.object,\n          controller.property,\n          {\n            before: nextSibling,\n            factoryArgs: [options]\n          }\n        );\n      }\n    },\n\n    /**\n     * Sets the name of the controller.\n     * @param  {string} name\n     * @return {Controller}\n     */\n    name: function(name) {\n      controller.__li.firstElementChild.firstElementChild.innerHTML = name;\n      return controller;\n    },\n\n    /**\n     * Sets controller to listen for changes on its underlying object.\n     * @return {Controller}\n     */\n    listen: function() {\n      controller.__gui.listen(controller);\n      return controller;\n    },\n\n    /**\n     * Removes the controller from its parent GUI.\n     * @return {Controller}\n     */\n    remove: function() {\n      controller.__gui.remove(controller);\n      return controller;\n    }\n  });\n\n  // All sliders should be accompanied by a box.\n  if (controller instanceof NumberControllerSlider) {\n    const box = new NumberControllerBox(controller.object, controller.property,\n      { min: controller.__min, max: controller.__max, step: controller.__step });\n\n    common.each(['updateDisplay', 'onChange', 'onFinishChange', 'step', 'min', 'max'], function(method) {\n      const pc = controller[method];\n      const pb = box[method];\n      controller[method] = box[method] = function() {\n        const args = Array.prototype.slice.call(arguments);\n        pb.apply(box, args);\n        return pc.apply(controller, args);\n      };\n    });\n\n    dom.addClass(li, 'has-slider');\n    controller.domElement.insertBefore(box.domElement, controller.domElement.firstElementChild);\n  } else if (controller instanceof NumberControllerBox) {\n    const r = function(returned) {\n      // Have we defined both boundaries?\n      if (common.isNumber(controller.__min) && common.isNumber(controller.__max)) {\n        // Well, then lets just replace this with a slider.\n\n        // lets remember if the old controller had a specific name or was listening\n        const oldName = controller.__li.firstElementChild.firstElementChild.innerHTML;\n        const wasListening = controller.__gui.__listening.indexOf(controller) > -1;\n\n        controller.remove();\n        const newController = add(\n          gui,\n          controller.object,\n          controller.property,\n          {\n            before: controller.__li.nextElementSibling,\n            factoryArgs: [controller.__min, controller.__max, controller.__step]\n          }\n        );\n\n        newController.name(oldName);\n        if (wasListening) newController.listen();\n\n        return newController;\n      }\n\n      return returned;\n    };\n\n    controller.min = common.compose(r, controller.min);\n    controller.max = common.compose(r, controller.max);\n  } else if (controller instanceof BooleanController) {\n    dom.bind(li, 'click', function() {\n      dom.fakeEvent(controller.__checkbox, 'click');\n    });\n\n    dom.bind(controller.__checkbox, 'click', function(e) {\n      e.stopPropagation(); // Prevents double-toggle\n    });\n  } else if (controller instanceof FunctionController) {\n    dom.bind(li, 'click', function() {\n      dom.fakeEvent(controller.__button, 'click');\n    });\n\n    dom.bind(li, 'mouseover', function() {\n      dom.addClass(controller.__button, 'hover');\n    });\n\n    dom.bind(li, 'mouseout', function() {\n      dom.removeClass(controller.__button, 'hover');\n    });\n  } else if (controller instanceof ColorController) {\n    dom.addClass(li, 'color');\n    controller.updateDisplay = common.compose(function(val) {\n      li.style.borderLeftColor = controller.__color.toString();\n      return val;\n    }, controller.updateDisplay);\n\n    controller.updateDisplay();\n  }\n\n  controller.setValue = common.compose(function(val) {\n    if (gui.getRoot().__preset_select && controller.isModified()) {\n      markPresetModified(gui.getRoot(), true);\n    }\n\n    return val;\n  }, controller.setValue);\n}\n\nfunction recallSavedValue(gui, controller) {\n  // Find the topmost GUI, that's where remembered objects live.\n  const root = gui.getRoot();\n\n  // Does the object we're controlling match anything we've been told to\n  // remember?\n  const matchedIndex = root.__rememberedObjects.indexOf(controller.object);\n\n  // Why yes, it does!\n  if (matchedIndex !== -1) {\n    // Let me fetch a map of controllers for thcommon.isObject.\n    let controllerMap = root.__rememberedObjectIndecesToControllers[matchedIndex];\n\n    // Ohp, I believe this is the first controller we've created for this\n    // object. Lets make the map fresh.\n    if (controllerMap === undefined) {\n      controllerMap = {};\n      root.__rememberedObjectIndecesToControllers[matchedIndex] =\n        controllerMap;\n    }\n\n    // Keep track of this controller\n    controllerMap[controller.property] = controller;\n\n    // Okay, now have we saved any values for this controller?\n    if (root.load && root.load.remembered) {\n      const presetMap = root.load.remembered;\n\n      // Which preset are we trying to load?\n      let preset;\n\n      if (presetMap[gui.preset]) {\n        preset = presetMap[gui.preset];\n      } else if (presetMap[DEFAULT_DEFAULT_PRESET_NAME]) {\n        // Uhh, you can have the default instead?\n        preset = presetMap[DEFAULT_DEFAULT_PRESET_NAME];\n      } else {\n        // Nada.\n        return;\n      }\n\n      // Did the loaded object remember thcommon.isObject? &&  Did we remember this particular property?\n      if (preset[matchedIndex] && preset[matchedIndex][controller.property] !== undefined) {\n        // We did remember something for this guy ...\n        const value = preset[matchedIndex][controller.property];\n\n        // And that's what it is.\n        controller.initialValue = value;\n        controller.setValue(value);\n      }\n    }\n  }\n}\n\nfunction add(gui, object, property, params) {\n  if (object[property] === undefined) {\n    throw new Error(`Object \"${object}\" has no property \"${property}\"`);\n  }\n\n  let controller;\n\n  if (params.color) {\n    controller = new ColorController(object, property);\n  } else {\n    const factoryArgs = [object, property].concat(params.factoryArgs);\n    controller = ControllerFactory.apply(gui, factoryArgs);\n  }\n\n  if (params.before instanceof Controller) {\n    params.before = params.before.__li;\n  }\n\n  recallSavedValue(gui, controller);\n\n  dom.addClass(controller.domElement, 'c');\n\n  const name = document.createElement('span');\n  dom.addClass(name, 'property-name');\n  name.innerHTML = controller.property;\n\n  const container = document.createElement('div');\n  container.appendChild(name);\n  container.appendChild(controller.domElement);\n\n  const li = addRow(gui, container, params.before);\n\n  dom.addClass(li, GUI.CLASS_CONTROLLER_ROW);\n  if (controller instanceof ColorController) {\n    dom.addClass(li, 'color');\n  } else {\n    dom.addClass(li, typeof controller.getValue());\n  }\n\n  augmentController(gui, li, controller);\n\n  gui.__controllers.push(controller);\n\n  return controller;\n}\n\nfunction getLocalStorageHash(gui, key) {\n  // TODO how does this deal with multiple GUI's?\n  return document.location.href + '.' + key;\n}\n\nfunction addPresetOption(gui, name, setSelected) {\n  const opt = document.createElement('option');\n  opt.innerHTML = name;\n  opt.value = name;\n  gui.__preset_select.appendChild(opt);\n  if (setSelected) {\n    gui.__preset_select.selectedIndex = gui.__preset_select.length - 1;\n  }\n}\n\nfunction showHideExplain(gui, explain) {\n  explain.style.display = gui.useLocalStorage ? 'block' : 'none';\n}\n\nfunction addSaveMenu(gui) {\n  const div = gui.__save_row = document.createElement('li');\n\n  dom.addClass(gui.domElement, 'has-save');\n\n  gui.__ul.insertBefore(div, gui.__ul.firstChild);\n\n  dom.addClass(div, 'save-row');\n\n  const gears = document.createElement('span');\n  gears.innerHTML = '&nbsp;';\n  dom.addClass(gears, 'button gears');\n\n  // TODO replace with FunctionController\n  const button = document.createElement('span');\n  button.innerHTML = 'Save';\n  dom.addClass(button, 'button');\n  dom.addClass(button, 'save');\n\n  const button2 = document.createElement('span');\n  button2.innerHTML = 'New';\n  dom.addClass(button2, 'button');\n  dom.addClass(button2, 'save-as');\n\n  const button3 = document.createElement('span');\n  button3.innerHTML = 'Revert';\n  dom.addClass(button3, 'button');\n  dom.addClass(button3, 'revert');\n\n  const select = gui.__preset_select = document.createElement('select');\n\n  if (gui.load && gui.load.remembered) {\n    common.each(gui.load.remembered, function(value, key) {\n      addPresetOption(gui, key, key === gui.preset);\n    });\n  } else {\n    addPresetOption(gui, DEFAULT_DEFAULT_PRESET_NAME, false);\n  }\n\n  dom.bind(select, 'change', function() {\n    for (let index = 0; index < gui.__preset_select.length; index++) {\n      gui.__preset_select[index].innerHTML = gui.__preset_select[index].value;\n    }\n\n    gui.preset = this.value;\n  });\n\n  div.appendChild(select);\n  div.appendChild(gears);\n  div.appendChild(button);\n  div.appendChild(button2);\n  div.appendChild(button3);\n\n  if (SUPPORTS_LOCAL_STORAGE) {\n    const explain = document.getElementById('dg-local-explain');\n    const localStorageCheckBox = document.getElementById('dg-local-storage');\n    const saveLocally = document.getElementById('dg-save-locally');\n\n    saveLocally.style.display = 'block';\n\n    if (localStorage.getItem(getLocalStorageHash(gui, 'isLocal')) === 'true') {\n      localStorageCheckBox.setAttribute('checked', 'checked');\n    }\n\n    showHideExplain(gui, explain);\n\n    // TODO: Use a boolean controller, fool!\n    dom.bind(localStorageCheckBox, 'change', function() {\n      gui.useLocalStorage = !gui.useLocalStorage;\n      showHideExplain(gui, explain);\n    });\n  }\n\n  const newConstructorTextArea = document.getElementById('dg-new-constructor');\n\n  dom.bind(newConstructorTextArea, 'keydown', function(e) {\n    if (e.metaKey && (e.which === 67 || e.keyCode === 67)) {\n      SAVE_DIALOGUE.hide();\n    }\n  });\n\n  dom.bind(gears, 'click', function() {\n    newConstructorTextArea.innerHTML = JSON.stringify(gui.getSaveObject(), undefined, 2);\n    SAVE_DIALOGUE.show();\n    newConstructorTextArea.focus();\n    newConstructorTextArea.select();\n  });\n\n  dom.bind(button, 'click', function() {\n    gui.save();\n  });\n\n  dom.bind(button2, 'click', function() {\n    const presetName = prompt('Enter a new preset name.');\n    if (presetName) {\n      gui.saveAs(presetName);\n    }\n  });\n\n  dom.bind(button3, 'click', function() {\n    gui.revert();\n  });\n\n  // div.appendChild(button2);\n}\n\nfunction addResizeHandle(gui) {\n  let pmouseX;\n\n  gui.__resize_handle = document.createElement('div');\n\n  common.extend(gui.__resize_handle.style, {\n\n    width: '6px',\n    marginLeft: '-3px',\n    height: '200px',\n    cursor: 'ew-resize',\n    position: 'absolute'\n    // border: '1px solid blue'\n\n  });\n\n  function drag(e) {\n    e.preventDefault();\n\n    gui.width += pmouseX - e.clientX;\n    gui.onResize();\n    pmouseX = e.clientX;\n\n    return false;\n  }\n\n  function dragStop() {\n    dom.removeClass(gui.__closeButton, GUI.CLASS_DRAG);\n    dom.unbind(window, 'mousemove', drag);\n    dom.unbind(window, 'mouseup', dragStop);\n  }\n\n  function dragStart(e) {\n    e.preventDefault();\n\n    pmouseX = e.clientX;\n\n    dom.addClass(gui.__closeButton, GUI.CLASS_DRAG);\n    dom.bind(window, 'mousemove', drag);\n    dom.bind(window, 'mouseup', dragStop);\n\n    return false;\n  }\n\n  dom.bind(gui.__resize_handle, 'mousedown', dragStart);\n  dom.bind(gui.__closeButton, 'mousedown', dragStart);\n\n  gui.domElement.insertBefore(gui.__resize_handle, gui.domElement.firstElementChild);\n}\n\nfunction setWidth(gui, w) {\n  gui.domElement.style.width = w + 'px';\n  // Auto placed save-rows are position fixed, so we have to\n  // set the width manually if we want it to bleed to the edge\n  if (gui.__save_row && gui.autoPlace) {\n    gui.__save_row.style.width = w + 'px';\n  }\n  if (gui.__closeButton) {\n    gui.__closeButton.style.width = w + 'px';\n  }\n}\n\nfunction getCurrentPreset(gui, useInitialValues) {\n  const toReturn = {};\n\n  // For each object I'm remembering\n  common.each(gui.__rememberedObjects, function(val, index) {\n    const savedValues = {};\n\n    // The controllers I've made for thcommon.isObject by property\n    const controllerMap =\n      gui.__rememberedObjectIndecesToControllers[index];\n\n    // Remember each value for each property\n    common.each(controllerMap, function(controller, property) {\n      savedValues[property] = useInitialValues ? controller.initialValue : controller.getValue();\n    });\n\n    // Save the values for thcommon.isObject\n    toReturn[index] = savedValues;\n  });\n\n  return toReturn;\n}\n\nfunction setPresetSelectIndex(gui) {\n  for (let index = 0; index < gui.__preset_select.length; index++) {\n    if (gui.__preset_select[index].value === gui.preset) {\n      gui.__preset_select.selectedIndex = index;\n    }\n  }\n}\n\nfunction updateDisplays(controllerArray) {\n  if (controllerArray.length !== 0) {\n    requestAnimationFrame.call(window, function() {\n      updateDisplays(controllerArray);\n    });\n  }\n\n  common.each(controllerArray, function(c) {\n    c.updateDisplay();\n  });\n}\n\nexport default GUI;\n", "/**\n * dat-gui JavaScript Controller Library\n * https://github.com/dataarts/dat.gui\n *\n * Copyright 2011 Data Arts Team, Google Creative Lab\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n */\n\nimport Color from './color/Color';\nimport math from './color/math';\nimport interpret from './color/interpret';\n\nimport Controller from './controllers/Controller';\nimport BooleanController from './controllers/BooleanController';\nimport OptionController from './controllers/OptionController';\nimport StringController from './controllers/StringController';\nimport NumberController from './controllers/NumberController';\nimport NumberControllerBox from './controllers/NumberControllerBox';\nimport NumberControllerSlider from './controllers/NumberControllerSlider';\nimport FunctionController from './controllers/FunctionController';\nimport ColorController from './controllers/ColorController';\n\nimport domImport from './dom/dom';\nimport GUIImport from './gui/GUI';\n\nexport const color = {\n  Color: Color,\n  math: math,\n  interpret: interpret\n};\n\nexport const controllers = {\n  Controller: Controller,\n  BooleanController: BooleanController,\n  OptionController: OptionController,\n  StringController: StringController,\n  NumberController: NumberController,\n  NumberControllerBox: NumberControllerBox,\n  NumberControllerSlider: NumberControllerSlider,\n  FunctionController: FunctionController,\n  ColorController: ColorController\n};\n\nexport const dom = { dom: domImport };\n\nexport const gui = { GUI: GUIImport };\n\nexport const GUI = GUIImport;\n\nexport default {\n  color,\n  controllers,\n  dom,\n  gui,\n  GUI\n};\n"], "names": ["color", "forceCSSHex", "colorFormat", "__state", "conversionName", "toString", "r", "Math", "round", "g", "b", "a", "h", "s", "toFixed", "v", "str", "hex", "length", "ARR_EACH", "Array", "prototype", "for<PERSON>ach", "ARR_SLICE", "slice", "Common", "target", "each", "call", "arguments", "obj", "keys", "isObject", "Object", "key", "isUndefined", "bind", "toCall", "args", "i", "apply", "itr", "scope", "l", "BREAK", "fnc", "func", "threshold", "callImmediately", "timeout", "delayed", "callNow", "setTimeout", "toArray", "undefined", "isNaN", "isArray", "constructor", "Function", "INTERPRETATIONS", "common", "isString", "original", "test", "match", "parseInt", "parseFloat", "isNumber", "result", "toReturn", "interpret", "family", "litmus", "conversions", "conversion", "read", "tmpComponent", "ColorMath", "hi", "floor", "f", "p", "q", "t", "c", "min", "max", "delta", "NaN", "hex_with_component", "componentIndex", "value", "Color", "Error", "colorToString", "write", "defineRGBComponent", "component", "componentHexIndex", "defineProperty", "space", "recalculateRGB", "defineHSVComponent", "recalculateHSV", "math", "component_from_hex", "extend", "hsv_to_rgb", "rgb_to_hsv", "COMPONENTS", "rgb_to_hex", "Controller", "object", "property", "initialValue", "dom<PERSON>lement", "document", "createElement", "__on<PERSON><PERSON>e", "__onFinishChange", "newValue", "updateDisplay", "getValue", "EVENT_MAP", "EVENT_MAP_INV", "k", "e", "CSS_VALUE_PIXELS", "cssValueToPixels", "val", "isNull", "dom", "elem", "selectable", "style", "onselectstart", "MozUserSelect", "KhtmlUserSelect", "unselectable", "hor", "vert", "vertical", "horizontal", "position", "left", "right", "top", "bottom", "eventType", "pars", "aux", "params", "className", "evt", "createEvent", "clientX", "x", "clientY", "y", "initMouseEvent", "bubbles", "cancelable", "window", "clickCount", "init", "initKeyboardEvent", "initKeyEvent", "defaults", "ctrl<PERSON>ey", "altKey", "shift<PERSON>ey", "metaKey", "keyCode", "charCode", "initEvent", "dispatchEvent", "event", "newBool", "bool", "addEventListener", "attachEvent", "removeEventListener", "detachEvent", "classes", "split", "indexOf", "push", "join", "replace", "removeAttribute", "index", "splice", "RegExp", "getComputedStyle", "width", "height", "el", "offset", "offsetParent", "offsetLeft", "offsetTop", "activeElement", "type", "href", "BooleanController", "_this", "__prev", "__checkbox", "setAttribute", "onChange", "setValue", "append<PERSON><PERSON><PERSON>", "checked", "OptionController", "opts", "options", "__select", "map", "element", "opt", "innerHTML", "desiredValue", "selectedIndex", "isActive", "StringController", "__input", "onBlur", "blur", "numDecimals", "_x", "NumberController", "_params", "__min", "__max", "__step", "step", "__impliedStep", "pow", "log", "abs", "LN10", "__precision", "_v", "minValue", "maxValue", "<PERSON><PERSON><PERSON><PERSON>", "roundToDecimal", "decimals", "tenTo", "NumberControllerBox", "__truncationSuspended", "prevY", "attempted", "onFinish", "onMouseDrag", "diff", "onMouseUp", "unbind", "onMouseDown", "i1", "i2", "o1", "o2", "NumberControllerSlider", "__background", "__foreground", "onTouchStart", "addClass", "preventDefault", "bgRect", "getBoundingClientRect", "touches", "onTouchMove", "onTouchEnd", "pct", "FunctionController", "text", "__button", "fire", "ColorController", "__color", "__temp", "makeSelectable", "__selector", "__saturation_field", "__field_knob", "__field_knob_border", "__hue_knob", "__hue_field", "__input_textShadow", "removeClass", "valueField", "fieldDown", "fieldDownH", "setSV", "fieldUpSV", "setH", "fieldUpH", "toOriginal", "fieldRect", "mismatch", "flip", "_flip", "toHexString", "marginTop", "vendors", "linearGradient", "background", "vendor", "cssText", "hue<PERSON><PERSON><PERSON>", "css", "url", "indoc", "doc", "link", "rel", "getElementsByTagName", "cssContent", "injected", "head", "saveDialogContents", "ControllerFactory", "isFunction", "isBoolean", "requestAnimationFrame", "callback", "webkitRequestAnimationFrame", "mozRequestAnimationFrame", "oRequestAnimationFrame", "msRequestAnimationFrame", "CenteredDiv", "backgroundElement", "makeFullscreen", "body", "hide", "display", "opacity", "webkitTransform", "layout", "defer", "innerWidth", "getWidth", "innerHeight", "getHeight", "inject", "styleSheet", "CSS_NAMESPACE", "HIDE_KEY_CODE", "CLOSE_BUTTON_HEIGHT", "DEFAULT_DEFAULT_PRESET_NAME", "SUPPORTS_LOCAL_STORAGE", "localStorage", "SAVE_DIALOGUE", "autoPlaceVirgin", "autoPlaceContainer", "hideable<PERSON><PERSON><PERSON>", "GUI", "__ul", "__folders", "__controllers", "__rememberedObjects", "__rememberedObjectIndecesToControllers", "__listening", "DEFAULT_WIDTH", "autoPlace", "load", "preset", "parent", "hideable", "resizable", "scrollable", "useLocalStorage", "getItem", "getLocalStorageHash", "saveToLocalStorage", "titleRow", "defineProperties", "closeOnTop", "getRoot", "revert", "name", "closed", "CLASS_CLOSED", "onResize", "__<PERSON><PERSON><PERSON><PERSON>", "TEXT_OPEN", "TEXT_CLOSED", "setItem", "CLASS_MAIN", "savedGui", "JSON", "parse", "CLASS_CLOSE_BUTTON", "CLASS_CLOSE_TOP", "insertBefore", "childNodes", "CLASS_CLOSE_BOTTOM", "titleRowName", "createTextNode", "addRow", "onClickTitle", "CLASS_AUTO_PLACE_CONTAINER", "CLASS_AUTO_PLACE", "__resi<PERSON><PERSON><PERSON><PERSON>", "onResizeDebounced", "stringify", "getSaveObject", "saveToLocalStorageIfPossible", "resetWidth", "root", "toggleHide", "gui", "CLASS_CONTROLLER_ROW", "CLASS_TOO_TALL", "CLASS_DRAG", "_keydownHandler", "which", "add", "controller", "<PERSON><PERSON><PERSON><PERSON>", "__li", "subfolder", "removeFolder", "newGuiParams", "folders", "li", "folder", "parentElement", "getOffset", "node", "__save_row", "__resize_handle", "offsetHeight", "debounce", "saveDialogueContents", "remembered", "getCurrentPreset", "presetName", "newDom", "liBefore", "removeListeners", "markPresetModified", "modified", "__preset_select", "augmentController", "__gui", "nextS<PERSON>ling", "nextElement<PERSON><PERSON>ling", "remove", "first<PERSON><PERSON><PERSON><PERSON><PERSON>", "listen", "box", "method", "pc", "pb", "returned", "old<PERSON>ame", "wasListening", "newController", "compose", "fakeEvent", "stopPropagation", "borderLeftColor", "isModified", "recallSavedValue", "matchedIndex", "controllerMap", "presetMap", "factoryArgs", "concat", "before", "container", "location", "addPresetOption", "setSelected", "showHideExplain", "explain", "addSaveMenu", "div", "<PERSON><PERSON><PERSON><PERSON>", "gears", "button", "button2", "button3", "select", "getElementById", "localStorageCheckBox", "saveLocally", "newConstructorTextArea", "show", "focus", "save", "prompt", "saveAs", "addResizeHandle", "pmouseX", "drag", "dragStop", "dragStart", "<PERSON><PERSON><PERSON><PERSON>", "w", "useInitialValues", "savedValues", "setPresetSelectIndex", "updateDisplays", "controllerArray", "controllers", "domImport", "GUIImport"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAae,wBAASA,KAAT,EAAgBC,WAAhB,EAA6B;MACpCC,cAAcF,MAAMG,OAAN,CAAcC,cAAd,CAA6BC,QAA7B,EAApB;MAEMC,IAAIC,KAAKC,KAAL,CAAWR,MAAMM,CAAjB,CAAV;MACMG,IAAIF,KAAKC,KAAL,CAAWR,MAAMS,CAAjB,CAAV;MACMC,IAAIH,KAAKC,KAAL,CAAWR,MAAMU,CAAjB,CAAV;MACMC,IAAIX,MAAMW,CAAhB;MACMC,IAAIL,KAAKC,KAAL,CAAWR,MAAMY,CAAjB,CAAV;MACMC,IAAIb,MAAMa,CAAN,CAAQC,OAAR,CAAgB,CAAhB,CAAV;MACMC,IAAIf,MAAMe,CAAN,CAAQD,OAAR,CAAgB,CAAhB,CAAV;MAEIb,eAAgBC,gBAAgB,gBAAhC,IAAsDA,gBAAgB,cAA1E,EAA2F;QACrFc,MAAMhB,MAAMiB,GAAN,CAAUZ,QAAV,CAAmB,EAAnB,CAAV;WACOW,IAAIE,MAAJ,GAAa,CAApB,EAAuB;YACf,MAAMF,GAAZ;;WAEK,MAAMA,GAAb;GALF,MAMO,IAAId,gBAAgB,SAApB,EAA+B;WAC7B,SAASI,CAAT,GAAa,GAAb,GAAmBG,CAAnB,GAAuB,GAAvB,GAA6BC,CAA7B,GAAiC,GAAxC;GADK,MAEA,IAAIR,gBAAgB,UAApB,EAAgC;WAC9B,UAAUI,CAAV,GAAc,GAAd,GAAoBG,CAApB,GAAwB,GAAxB,GAA8BC,CAA9B,GAAkC,GAAlC,GAAwCC,CAAxC,GAA4C,GAAnD;GADK,MAEA,IAAIT,gBAAgB,KAApB,EAA2B;WACzB,OAAOF,MAAMiB,GAAN,CAAUZ,QAAV,CAAmB,EAAnB,CAAd;GADK,MAEA,IAAIH,gBAAgB,WAApB,EAAiC;WAC/B,MAAMI,CAAN,GAAU,GAAV,GAAgBG,CAAhB,GAAoB,GAApB,GAA0BC,CAA1B,GAA8B,GAArC;GADK,MAEA,IAAIR,gBAAgB,YAApB,EAAkC;WAChC,MAAMI,CAAN,GAAU,GAAV,GAAgBG,CAAhB,GAAoB,GAApB,GAA0BC,CAA1B,GAA8B,GAA9B,GAAoCC,CAApC,GAAwC,GAA/C;GADK,MAEA,IAAIT,gBAAgB,SAApB,EAA+B;WAC7B,QAAQI,CAAR,GAAY,KAAZ,GAAoBG,CAApB,GAAwB,KAAxB,GAAgCC,CAAhC,GAAoC,GAA3C;GADK,MAEA,IAAIR,gBAAgB,UAApB,EAAgC;WAC9B,QAAQI,CAAR,GAAY,KAAZ,GAAoBG,CAApB,GAAwB,KAAxB,GAAgCC,CAAhC,GAAoC,KAApC,GAA4CC,CAA5C,GAAgD,GAAvD;GADK,MAEA,IAAIT,gBAAgB,SAApB,EAA+B;WAC7B,QAAQU,CAAR,GAAY,KAAZ,GAAoBC,CAApB,GAAwB,KAAxB,GAAgCE,CAAhC,GAAoC,GAA3C;GADK,MAEA,IAAIb,gBAAgB,UAApB,EAAgC;WAC9B,QAAQU,CAAR,GAAY,KAAZ,GAAoBC,CAApB,GAAwB,KAAxB,GAAgCE,CAAhC,GAAoC,KAApC,GAA4CJ,CAA5C,GAAgD,GAAvD;;SAGK,gBAAP;;;ACrCF,IAAMQ,WAAWC,MAAMC,SAAN,CAAgBC,OAAjC;AACA,IAAMC,YAAYH,MAAMC,SAAN,CAAgBG,KAAlC;AAQA,IAAMC,SAAS;SACN,EADM;UAGL,gBAASC,MAAT,EAAiB;SAClBC,IAAL,CAAUJ,UAAUK,IAAV,CAAeC,SAAf,EAA0B,CAA1B,CAAV,EAAwC,UAASC,GAAT,EAAc;UAC9CC,OAAO,KAAKC,QAAL,CAAcF,GAAd,IAAqBG,OAAOF,IAAP,CAAYD,GAAZ,CAArB,GAAwC,EAArD;WACKR,OAAL,CAAa,UAASY,GAAT,EAAc;YACrB,CAAC,KAAKC,WAAL,CAAiBL,IAAII,GAAJ,CAAjB,CAAL,EAAiC;iBACxBA,GAAP,IAAcJ,IAAII,GAAJ,CAAd;;OAFS,CAIXE,IAJW,CAIN,IAJM,CAAb;KAFF,EAOG,IAPH;WASOV,MAAP;GAbW;YAgBH,kBAASA,MAAT,EAAiB;SACpBC,IAAL,CAAUJ,UAAUK,IAAV,CAAeC,SAAf,EAA0B,CAA1B,CAAV,EAAwC,UAASC,GAAT,EAAc;UAC9CC,OAAO,KAAKC,QAAL,CAAcF,GAAd,IAAqBG,OAAOF,IAAP,CAAYD,GAAZ,CAArB,GAAwC,EAArD;WACKR,OAAL,CAAa,UAASY,GAAT,EAAc;YACrB,KAAKC,WAAL,CAAiBT,OAAOQ,GAAP,CAAjB,CAAJ,EAAmC;iBAC1BA,GAAP,IAAcJ,IAAII,GAAJ,CAAd;;OAFS,CAIXE,IAJW,CAIN,IAJM,CAAb;KAFF,EAOG,IAPH;WASOV,MAAP;GA1BW;WA6BJ,mBAAW;QACZW,SAASd,UAAUK,IAAV,CAAeC,SAAf,CAAf;WACO,YAAW;UACZS,OAAOf,UAAUK,IAAV,CAAeC,SAAf,CAAX;WACK,IAAIU,IAAIF,OAAOnB,MAAP,GAAgB,CAA7B,EAAgCqB,KAAK,CAArC,EAAwCA,GAAxC,EAA6C;eACpC,CAACF,OAAOE,CAAP,EAAUC,KAAV,CAAgB,IAAhB,EAAsBF,IAAtB,CAAD,CAAP;;aAEKA,KAAK,CAAL,CAAP;KALF;GA/BW;QAwCP,cAASR,GAAT,EAAcW,GAAd,EAAmBC,KAAnB,EAA0B;QAC1B,CAACZ,GAAL,EAAU;;;QAINX,YAAYW,IAAIR,OAAhB,IAA2BQ,IAAIR,OAAJ,KAAgBH,QAA/C,EAAyD;UACnDG,OAAJ,CAAYmB,GAAZ,EAAiBC,KAAjB;KADF,MAEO,IAAIZ,IAAIZ,MAAJ,KAAeY,IAAIZ,MAAJ,GAAa,CAAhC,EAAmC;UACpCgB,YAAJ;UACIS,UAAJ;WACKT,MAAM,CAAN,EAASS,IAAIb,IAAIZ,MAAtB,EAA8BgB,MAAMS,CAApC,EAAuCT,KAAvC,EAA8C;YACxCA,OAAOJ,GAAP,IAAcW,IAAIb,IAAJ,CAASc,KAAT,EAAgBZ,IAAII,GAAJ,CAAhB,EAA0BA,GAA1B,MAAmC,KAAKU,KAA1D,EAAiE;;;;KAJ9D,MAQA;WACA,IAAMV,IAAX,IAAkBJ,GAAlB,EAAuB;YACjBW,IAAIb,IAAJ,CAASc,KAAT,EAAgBZ,IAAII,IAAJ,CAAhB,EAA0BA,IAA1B,MAAmC,KAAKU,KAA5C,EAAmD;;;;;GAzD5C;SAgEN,eAASC,GAAT,EAAc;eACRA,GAAX,EAAgB,CAAhB;GAjEW;YAqEH,kBAASC,IAAT,EAAeC,SAAf,EAA0BC,eAA1B,EAA2C;QAC/CC,gBAAJ;WAEO,YAAW;UACVnB,MAAM,IAAZ;UACMQ,OAAOT,SAAb;eACSqB,OAAT,GAAmB;kBACP,IAAV;YACI,CAACF,eAAL,EAAsBF,KAAKN,KAAL,CAAWV,GAAX,EAAgBQ,IAAhB;;UAGlBa,UAAUH,mBAAmB,CAACC,OAApC;mBAEaA,OAAb;gBACUG,WAAWF,OAAX,EAAoBH,SAApB,CAAV;UAEII,OAAJ,EAAa;aACNX,KAAL,CAAWV,GAAX,EAAgBQ,IAAhB;;KAdJ;GAxEW;WA2FJ,iBAASR,GAAT,EAAc;QACjBA,IAAIuB,OAAR,EAAiB,OAAOvB,IAAIuB,OAAJ,EAAP;WACV9B,UAAUK,IAAV,CAAeE,GAAf,CAAP;GA7FW;eAgGA,qBAASA,GAAT,EAAc;WAClBA,QAAQwB,SAAf;GAjGW;UAoGL,gBAASxB,GAAT,EAAc;WACbA,QAAQ,IAAf;GArGW;;;;;;;;;IAwGN,UAASA,GAAT,EAAc;WACZyB,MAAMzB,GAAN,CAAP;GADF,CAxGa;WA4GJV,MAAMoC,OAAN,IAAiB,UAAS1B,GAAT,EAAc;WAC/BA,IAAI2B,WAAJ,KAAoBrC,KAA3B;GA7GW;YAgHH,kBAASU,GAAT,EAAc;WACfA,QAAQG,OAAOH,GAAP,CAAf;GAjHW;YAoHH,kBAASA,GAAT,EAAc;WACfA,QAAQA,MAAM,CAArB;GArHW;YAwHH,kBAASA,GAAT,EAAc;WACfA,QAAQA,MAAM,EAArB;GAzHW;aA4HF,mBAASA,GAAT,EAAc;WAChBA,QAAQ,KAAR,IAAiBA,QAAQ,IAAhC;GA7HW;cAgID,oBAASA,GAAT,EAAc;WACjBA,eAAe4B,QAAtB;;CAjIJ;;ACNA,IAAMC,kBAAkB;AAEtB;UACUC,OAAOC,QADjB;eAEe;oBACK;YACR,cAASC,QAAT,EAAmB;YACjBC,OAAOD,SAASE,KAAT,CAAe,oCAAf,CAAb;YACID,SAAS,IAAb,EAAmB;iBACV,KAAP;;eAGK;iBACE,KADF;eAEAE,SACH,OACAF,KAAK,CAAL,EAAQ1D,QAAR,EADA,GACqB0D,KAAK,CAAL,EAAQ1D,QAAR,EADrB,GAEA0D,KAAK,CAAL,EAAQ1D,QAAR,EAFA,GAEqB0D,KAAK,CAAL,EAAQ1D,QAAR,EAFrB,GAGA0D,KAAK,CAAL,EAAQ1D,QAAR,EAHA,GAGqB0D,KAAK,CAAL,EAAQ1D,QAAR,EAJlB,EAIsC,CAJtC;SAFP;OAPY;aAkBPA;KAnBE;kBAsBG;YACN,cAASyD,QAAT,EAAmB;YACjBC,OAAOD,SAASE,KAAT,CAAe,mBAAf,CAAb;YACID,SAAS,IAAb,EAAmB;iBACV,KAAP;;eAGK;iBACE,KADF;eAEAE,SAAS,OAAOF,KAAK,CAAL,EAAQ1D,QAAR,EAAhB,EAAoC,CAApC;SAFP;OAPU;aAaLA;KAnCE;aAsCF;YACD,cAASyD,QAAT,EAAmB;YACjBC,OAAOD,SAASE,KAAT,CAAe,6CAAf,CAAb;YACID,SAAS,IAAb,EAAmB;iBACV,KAAP;;eAGK;iBACE,KADF;aAEFG,WAAWH,KAAK,CAAL,CAAX,CAFE;aAGFG,WAAWH,KAAK,CAAL,CAAX,CAHE;aAIFG,WAAWH,KAAK,CAAL,CAAX;SAJL;OAPK;aAeA1D;KArDE;cAwDD;YACF,cAASyD,QAAT,EAAmB;YACjBC,OAAOD,SAASE,KAAT,CAAe,0DAAf,CAAb;YACID,SAAS,IAAb,EAAmB;iBACV,KAAP;;eAGK;iBACE,KADF;aAEFG,WAAWH,KAAK,CAAL,CAAX,CAFE;aAGFG,WAAWH,KAAK,CAAL,CAAX,CAHE;aAIFG,WAAWH,KAAK,CAAL,CAAX,CAJE;aAKFG,WAAWH,KAAK,CAAL,CAAX;SALL;OAPM;aAgBD1D;;;CA5ES;AAkFtB;UACUuD,OAAOO,QADjB;eAGe;SAEN;YACG,cAASL,QAAT,EAAmB;eAChB;iBACE,KADF;eAEAA,QAFA;0BAGW;SAHlB;OAFC;aASI,eAAS9D,KAAT,EAAgB;eACdA,MAAMiB,GAAb;;;;CAjGc;AA0GtB;UACU2C,OAAOJ,OADjB;eAEe;eACA;YACH,cAASM,QAAT,EAAmB;YACnBA,SAAS5C,MAAT,KAAoB,CAAxB,EAA2B;iBAClB,KAAP;;eAGK;iBACE,KADF;aAEF4C,SAAS,CAAT,CAFE;aAGFA,SAAS,CAAT,CAHE;aAIFA,SAAS,CAAT;SAJL;OANO;aAcF,eAAS9D,KAAT,EAAgB;eACd,CAACA,MAAMM,CAAP,EAAUN,MAAMS,CAAhB,EAAmBT,MAAMU,CAAzB,CAAP;;KAhBO;gBAoBC;YACJ,cAASoD,QAAT,EAAmB;YACnBA,SAAS5C,MAAT,KAAoB,CAAxB,EAA2B,OAAO,KAAP;eACpB;iBACE,KADF;aAEF4C,SAAS,CAAT,CAFE;aAGFA,SAAS,CAAT,CAHE;aAIFA,SAAS,CAAT,CAJE;aAKFA,SAAS,CAAT;SALL;OAHQ;aAYH,eAAS9D,KAAT,EAAgB;eACd,CAACA,MAAMM,CAAP,EAAUN,MAAMS,CAAhB,EAAmBT,MAAMU,CAAzB,EAA4BV,MAAMW,CAAlC,CAAP;;;;CA7Ic;AAoJtB;UACUiD,OAAO5B,QADjB;eAEe;cAED;YACF,cAAS8B,QAAT,EAAmB;YACnBF,OAAOO,QAAP,CAAgBL,SAASxD,CAAzB,KACFsD,OAAOO,QAAP,CAAgBL,SAASrD,CAAzB,CADE,IAEFmD,OAAOO,QAAP,CAAgBL,SAASpD,CAAzB,CAFE,IAGFkD,OAAOO,QAAP,CAAgBL,SAASnD,CAAzB,CAHF,EAG+B;iBACtB;mBACE,KADF;eAEFmD,SAASxD,CAFP;eAGFwD,SAASrD,CAHP;eAIFqD,SAASpD,CAJP;eAKFoD,SAASnD;WALd;;eAQK,KAAP;OAdM;aAiBD,eAASX,KAAT,EAAgB;eACd;aACFA,MAAMM,CADJ;aAEFN,MAAMS,CAFJ;aAGFT,MAAMU,CAHJ;aAIFV,MAAMW;SAJX;;KApBO;aA6BF;YACD,cAASmD,QAAT,EAAmB;YACnBF,OAAOO,QAAP,CAAgBL,SAASxD,CAAzB,KACFsD,OAAOO,QAAP,CAAgBL,SAASrD,CAAzB,CADE,IAEFmD,OAAOO,QAAP,CAAgBL,SAASpD,CAAzB,CAFF,EAE+B;iBACtB;mBACE,KADF;eAEFoD,SAASxD,CAFP;eAGFwD,SAASrD,CAHP;eAIFqD,SAASpD;WAJd;;eAOK,KAAP;OAZK;aAeA,eAASV,KAAT,EAAgB;eACd;aACFA,MAAMM,CADJ;aAEFN,MAAMS,CAFJ;aAGFT,MAAMU;SAHX;;KA7CO;cAqDD;YACF,cAASoD,QAAT,EAAmB;YACnBF,OAAOO,QAAP,CAAgBL,SAASlD,CAAzB,KACFgD,OAAOO,QAAP,CAAgBL,SAASjD,CAAzB,CADE,IAEF+C,OAAOO,QAAP,CAAgBL,SAAS/C,CAAzB,CAFE,IAGF6C,OAAOO,QAAP,CAAgBL,SAASnD,CAAzB,CAHF,EAG+B;iBACtB;mBACE,KADF;eAEFmD,SAASlD,CAFP;eAGFkD,SAASjD,CAHP;eAIFiD,SAAS/C,CAJP;eAKF+C,SAASnD;WALd;;eAQK,KAAP;OAdM;aAiBD,eAASX,KAAT,EAAgB;eACd;aACFA,MAAMY,CADJ;aAEFZ,MAAMa,CAFJ;aAGFb,MAAMe,CAHJ;aAIFf,MAAMW;SAJX;;KAvEO;aAgFF;YACD,cAASmD,QAAT,EAAmB;YACnBF,OAAOO,QAAP,CAAgBL,SAASlD,CAAzB,KACFgD,OAAOO,QAAP,CAAgBL,SAASjD,CAAzB,CADE,IAEF+C,OAAOO,QAAP,CAAgBL,SAAS/C,CAAzB,CAFF,EAE+B;iBACtB;mBACE,KADF;eAEF+C,SAASlD,CAFP;eAGFkD,SAASjD,CAHP;eAIFiD,SAAS/C;WAJd;;eAOK,KAAP;OAZK;aAeA,eAASf,KAAT,EAAgB;eACd;aACFA,MAAMY,CADJ;aAEFZ,MAAMa,CAFJ;aAGFb,MAAMe;SAHX;;;;CAtPc,CAAxB;AAiQA,IAAIqD,eAAJ;AACA,IAAIC,iBAAJ;AAEA,IAAMC,YAAY,SAAZA,SAAY,GAAW;aAChB,KAAX;MAEMR,WAAWjC,UAAUX,MAAV,GAAmB,CAAnB,GAAuB0C,OAAOP,OAAP,CAAexB,SAAf,CAAvB,GAAmDA,UAAU,CAAV,CAApE;SACOF,IAAP,CAAYgC,eAAZ,EAA6B,UAASY,MAAT,EAAiB;QACxCA,OAAOC,MAAP,CAAcV,QAAd,CAAJ,EAA6B;aACpBnC,IAAP,CAAY4C,OAAOE,WAAnB,EAAgC,UAASC,UAAT,EAAqBtE,cAArB,EAAqC;iBAC1DsE,WAAWC,IAAX,CAAgBb,QAAhB,CAAT;YAEIO,aAAa,KAAb,IAAsBD,WAAW,KAArC,EAA4C;qBAC/BA,MAAX;iBACOhE,cAAP,GAAwBA,cAAxB;iBACOsE,UAAP,GAAoBA,UAApB;iBACOd,OAAOhB,KAAd;;OAPJ;aAWOgB,OAAOhB,KAAd;;GAbJ;SAiBOyB,QAAP;CArBF;;ACvQA,IAAIO,qBAAJ;AAEA,IAAMC,YAAY;cACJ,oBAASjE,CAAT,EAAYC,CAAZ,EAAeE,CAAf,EAAkB;QACtB+D,KAAKvE,KAAKwE,KAAL,CAAWnE,IAAI,EAAf,IAAqB,CAAhC;QAEMoE,IAAIpE,IAAI,EAAJ,GAASL,KAAKwE,KAAL,CAAWnE,IAAI,EAAf,CAAnB;QACMqE,IAAIlE,KAAK,MAAMF,CAAX,CAAV;QACMqE,IAAInE,KAAK,MAAOiE,IAAInE,CAAhB,CAAV;QACMsE,IAAIpE,KAAK,MAAO,CAAC,MAAMiE,CAAP,IAAYnE,CAAxB,CAAV;QAEMuE,IAAI,CACR,CAACrE,CAAD,EAAIoE,CAAJ,EAAOF,CAAP,CADQ,EAER,CAACC,CAAD,EAAInE,CAAJ,EAAOkE,CAAP,CAFQ,EAGR,CAACA,CAAD,EAAIlE,CAAJ,EAAOoE,CAAP,CAHQ,EAIR,CAACF,CAAD,EAAIC,CAAJ,EAAOnE,CAAP,CAJQ,EAKR,CAACoE,CAAD,EAAIF,CAAJ,EAAOlE,CAAP,CALQ,EAMR,CAACA,CAAD,EAAIkE,CAAJ,EAAOC,CAAP,CANQ,EAORJ,EAPQ,CAAV;WASO;SACFM,EAAE,CAAF,IAAO,GADL;SAEFA,EAAE,CAAF,IAAO,GAFL;SAGFA,EAAE,CAAF,IAAO;KAHZ;GAlBc;cAyBJ,oBAAS9E,CAAT,EAAYG,CAAZ,EAAeC,CAAf,EAAkB;QACtB2E,MAAM9E,KAAK8E,GAAL,CAAS/E,CAAT,EAAYG,CAAZ,EAAeC,CAAf,CAAZ;QACM4E,MAAM/E,KAAK+E,GAAL,CAAShF,CAAT,EAAYG,CAAZ,EAAeC,CAAf,CAAZ;QACM6E,QAAQD,MAAMD,GAApB;QACIzE,UAAJ;QACIC,UAAJ;QAEIyE,QAAQ,CAAZ,EAAe;UACTC,QAAQD,GAAZ;KADF,MAEO;aACE;WACFE,GADE;WAEF,CAFE;WAGF;OAHL;;QAOElF,MAAMgF,GAAV,EAAe;UACT,CAAC7E,IAAIC,CAAL,IAAU6E,KAAd;KADF,MAEO,IAAI9E,MAAM6E,GAAV,EAAe;UAChB,IAAI,CAAC5E,IAAIJ,CAAL,IAAUiF,KAAlB;KADK,MAEA;UACD,IAAI,CAACjF,IAAIG,CAAL,IAAU8E,KAAlB;;SAEG,CAAL;QACI3E,IAAI,CAAR,EAAW;WACJ,CAAL;;WAGK;SACFA,IAAI,GADF;SAEFC,CAFE;SAGFyE,MAAM;KAHX;GAtDc;cA6DJ,oBAAShF,CAAT,EAAYG,CAAZ,EAAeC,CAAf,EAAkB;QACxBO,MAAM,KAAKwE,kBAAL,CAAwB,CAAxB,EAA2B,CAA3B,EAA8BnF,CAA9B,CAAV;UACM,KAAKmF,kBAAL,CAAwBxE,GAAxB,EAA6B,CAA7B,EAAgCR,CAAhC,CAAN;UACM,KAAKgF,kBAAL,CAAwBxE,GAAxB,EAA6B,CAA7B,EAAgCP,CAAhC,CAAN;WACOO,GAAP;GAjEc;sBAoEI,4BAASA,GAAT,EAAcyE,cAAd,EAA8B;WACxCzE,OAAQyE,iBAAiB,CAA1B,GAAgC,IAAvC;GArEc;sBAwEI,4BAASzE,GAAT,EAAcyE,cAAd,EAA8BC,KAA9B,EAAqC;WAChDA,UAAUf,eAAec,iBAAiB,CAA1C,IAAgDzE,MAAM,EAAE,QAAQ2D,YAAV,CAA7D;;CAzEJ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ICGMgB;mBACU;;SACPzF,OAAL,GAAemE,UAAU9B,KAAV,CAAgB,IAAhB,EAAsBX,SAAtB,CAAf;QAEI,KAAK1B,OAAL,KAAiB,KAArB,EAA4B;YACpB,IAAI0F,KAAJ,CAAU,qCAAV,CAAN;;SAGG1F,OAAL,CAAaQ,CAAb,GAAiB,KAAKR,OAAL,CAAaQ,CAAb,IAAkB,CAAnC;;;;+BAGS;aACFmF,cAAc,IAAd,CAAP;;;;kCAGY;aACLA,cAAc,IAAd,EAAoB,IAApB,CAAP;;;;iCAGW;aACJ,KAAK3F,OAAL,CAAauE,UAAb,CAAwBqB,KAAxB,CAA8B,IAA9B,CAAP;;;;;AAIJ,SAASC,kBAAT,CAA4BtE,MAA5B,EAAoCuE,SAApC,EAA+CC,iBAA/C,EAAkE;SACzDC,cAAP,CAAsBzE,MAAtB,EAA8BuE,SAA9B,EAAyC;SAClC,kBAAW;UACV,KAAK9F,OAAL,CAAaiG,KAAb,KAAuB,KAA3B,EAAkC;eACzB,KAAKjG,OAAL,CAAa8F,SAAb,CAAP;;YAGII,cAAN,CAAqB,IAArB,EAA2BJ,SAA3B,EAAsCC,iBAAtC;aAEO,KAAK/F,OAAL,CAAa8F,SAAb,CAAP;KARqC;SAWlC,gBAASlF,CAAT,EAAY;UACX,KAAKZ,OAAL,CAAaiG,KAAb,KAAuB,KAA3B,EAAkC;cAC1BC,cAAN,CAAqB,IAArB,EAA2BJ,SAA3B,EAAsCC,iBAAtC;aACK/F,OAAL,CAAaiG,KAAb,GAAqB,KAArB;;WAGGjG,OAAL,CAAa8F,SAAb,IAA0BlF,CAA1B;;GAjBJ;;AAsBF,SAASuF,kBAAT,CAA4B5E,MAA5B,EAAoCuE,SAApC,EAA+C;SACtCE,cAAP,CAAsBzE,MAAtB,EAA8BuE,SAA9B,EAAyC;SAClC,kBAAW;UACV,KAAK9F,OAAL,CAAaiG,KAAb,KAAuB,KAA3B,EAAkC;eACzB,KAAKjG,OAAL,CAAa8F,SAAb,CAAP;;YAGIM,cAAN,CAAqB,IAArB;aAEO,KAAKpG,OAAL,CAAa8F,SAAb,CAAP;KARqC;SAWlC,gBAASlF,CAAT,EAAY;UACX,KAAKZ,OAAL,CAAaiG,KAAb,KAAuB,KAA3B,EAAkC;cAC1BG,cAAN,CAAqB,IAArB;aACKpG,OAAL,CAAaiG,KAAb,GAAqB,KAArB;;WAGGjG,OAAL,CAAa8F,SAAb,IAA0BlF,CAA1B;;GAjBJ;;AAuBF6E,MAAMS,cAAN,GAAuB,UAASrG,KAAT,EAAgBiG,SAAhB,EAA2BC,iBAA3B,EAA8C;MAC/DlG,MAAMG,OAAN,CAAciG,KAAd,KAAwB,KAA5B,EAAmC;UAC3BjG,OAAN,CAAc8F,SAAd,IAA2BO,UAAKC,kBAAL,CAAwBzG,MAAMG,OAAN,CAAcc,GAAtC,EAA2CiF,iBAA3C,CAA3B;GADF,MAEO,IAAIlG,MAAMG,OAAN,CAAciG,KAAd,KAAwB,KAA5B,EAAmC;WACjCM,MAAP,CAAc1G,MAAMG,OAApB,EAA6BqG,UAAKG,UAAL,CAAgB3G,MAAMG,OAAN,CAAcS,CAA9B,EAAiCZ,MAAMG,OAAN,CAAcU,CAA/C,EAAkDb,MAAMG,OAAN,CAAcY,CAAhE,CAA7B;GADK,MAEA;UACC,IAAI8E,KAAJ,CAAU,uBAAV,CAAN;;CANJ;AAUAD,MAAMW,cAAN,GAAuB,UAASvG,KAAT,EAAgB;MAC/BoE,SAASoC,UAAKI,UAAL,CAAgB5G,MAAMM,CAAtB,EAAyBN,MAAMS,CAA/B,EAAkCT,MAAMU,CAAxC,CAAf;SAEOgG,MAAP,CAAc1G,MAAMG,OAApB,EACE;OACKiE,OAAOvD,CADZ;OAEKuD,OAAOrD;GAHd;MAMI,CAAC6C,OAAOL,KAAP,CAAaa,OAAOxD,CAApB,CAAL,EAA6B;UACrBT,OAAN,CAAcS,CAAd,GAAkBwD,OAAOxD,CAAzB;GADF,MAEO,IAAIgD,OAAOzB,WAAP,CAAmBnC,MAAMG,OAAN,CAAcS,CAAjC,CAAJ,EAAyC;UACxCT,OAAN,CAAcS,CAAd,GAAkB,CAAlB;;CAZJ;AAgBAgF,MAAMiB,UAAN,GAAmB,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,EAAgB,GAAhB,EAAqB,GAArB,EAA0B,GAA1B,EAA+B,KAA/B,EAAsC,GAAtC,CAAnB;AAEAb,mBAAmBJ,MAAMvE,SAAzB,EAAoC,GAApC,EAAyC,CAAzC;AACA2E,mBAAmBJ,MAAMvE,SAAzB,EAAoC,GAApC,EAAyC,CAAzC;AACA2E,mBAAmBJ,MAAMvE,SAAzB,EAAoC,GAApC,EAAyC,CAAzC;AAEAiF,mBAAmBV,MAAMvE,SAAzB,EAAoC,GAApC;AACAiF,mBAAmBV,MAAMvE,SAAzB,EAAoC,GAApC;AACAiF,mBAAmBV,MAAMvE,SAAzB,EAAoC,GAApC;AAEAY,OAAOkE,cAAP,CAAsBP,MAAMvE,SAA5B,EAAuC,GAAvC,EAA4C;OACrC,kBAAW;WACP,KAAKlB,OAAL,CAAaQ,CAApB;GAFwC;OAKrC,gBAASI,CAAT,EAAY;SACVZ,OAAL,CAAaQ,CAAb,GAAiBI,CAAjB;;CANJ;AAUAkB,OAAOkE,cAAP,CAAsBP,MAAMvE,SAA5B,EAAuC,KAAvC,EAA8C;OACvC,kBAAW;QACV,KAAKlB,OAAL,CAAaiG,KAAb,KAAuB,KAA3B,EAAkC;WAC3BjG,OAAL,CAAac,GAAb,GAAmBuF,UAAKM,UAAL,CAAgB,KAAKxG,CAArB,EAAwB,KAAKG,CAA7B,EAAgC,KAAKC,CAArC,CAAnB;WACKP,OAAL,CAAaiG,KAAb,GAAqB,KAArB;;WAGK,KAAKjG,OAAL,CAAac,GAApB;GAP0C;OAUvC,gBAASF,CAAT,EAAY;SACVZ,OAAL,CAAaiG,KAAb,GAAqB,KAArB;SACKjG,OAAL,CAAac,GAAb,GAAmBF,CAAnB;;CAZJ;;ICpHMgG;sBACQC,MAAZ,EAAoBC,QAApB,EAA8B;;SACvBC,YAAL,GAAoBF,OAAOC,QAAP,CAApB;SAMKE,UAAL,GAAkBC,SAASC,aAAT,CAAuB,KAAvB,CAAlB;SAMKL,MAAL,GAAcA,MAAd;SAMKC,QAAL,GAAgBA,QAAhB;SAOKK,UAAL,GAAkBhE,SAAlB;SAOKiE,gBAAL,GAAwBjE,SAAxB;;;;6BAWOT,KAAK;WACPyE,UAAL,GAAkBzE,GAAlB;aACO,IAAP;;;;mCAYaA,KAAK;WACb0E,gBAAL,GAAwB1E,GAAxB;aACO,IAAP;;;;6BAQO2E,UAAU;WACZR,MAAL,CAAY,KAAKC,QAAjB,IAA6BO,QAA7B;UACI,KAAKF,UAAT,EAAqB;aACdA,UAAL,CAAgB1F,IAAhB,CAAqB,IAArB,EAA2B4F,QAA3B;;WAGGC,aAAL;aACO,IAAP;;;;+BAQS;aACF,KAAKT,MAAL,CAAY,KAAKC,QAAjB,CAAP;;;;oCAQc;aACP,IAAP;;;;iCAMW;aACJ,KAAKC,YAAL,KAAsB,KAAKQ,QAAL,EAA7B;;;;;;ACzGJ,IAAMC,YAAY;cACJ,CAAC,QAAD,CADI;eAEH,CAAC,OAAD,EAAU,WAAV,EAAuB,WAAvB,EAAoC,SAApC,EAA+C,WAA/C,CAFG;kBAGA,CAAC,SAAD;CAHlB;AAMA,IAAMC,gBAAgB,EAAtB;AACAhE,OAAOjC,IAAP,CAAYgG,SAAZ,EAAuB,UAAS5G,CAAT,EAAY8G,CAAZ,EAAe;SAC7BlG,IAAP,CAAYZ,CAAZ,EAAe,UAAS+G,CAAT,EAAY;kBACXA,CAAd,IAAmBD,CAAnB;GADF;CADF;AAMA,IAAME,mBAAmB,iBAAzB;AAEA,SAASC,gBAAT,CAA0BC,GAA1B,EAA+B;MACzBA,QAAQ,GAAR,IAAerE,OAAOzB,WAAP,CAAmB8F,GAAnB,CAAnB,EAA4C;WACnC,CAAP;;MAGIjE,QAAQiE,IAAIjE,KAAJ,CAAU+D,gBAAV,CAAd;MAEI,CAACnE,OAAOsE,MAAP,CAAclE,KAAd,CAAL,EAA2B;WAClBE,WAAWF,MAAM,CAAN,CAAX,CAAP;;SAKK,CAAP;;AAOF,IAAMmE,MAAM;kBAOM,wBAASC,IAAT,EAAeC,UAAf,EAA2B;QACrCD,SAAS9E,SAAT,IAAsB8E,KAAKE,KAAL,KAAehF,SAAzC,EAAoD;SAE/CiF,aAAL,GAAqBF,aAAa,YAAW;aACpC,KAAP;KADmB,GAEjB,YAAW,EAFf;SAKKC,KAAL,CAAWE,aAAX,GAA2BH,aAAa,MAAb,GAAsB,MAAjD;SACKC,KAAL,CAAWG,eAAX,GAA6BJ,aAAa,MAAb,GAAsB,MAAnD;SACKK,YAAL,GAAoBL,aAAa,IAAb,GAAoB,KAAxC;GAjBQ;kBA0BM,wBAASD,IAAT,EAAeO,GAAf,EAAoBC,IAApB,EAA0B;QACpCC,WAAWD,IAAf;QACIE,aAAaH,GAAjB;QAEI/E,OAAOzB,WAAP,CAAmB2G,UAAnB,CAAJ,EAAoC;mBACrB,IAAb;;QAGElF,OAAOzB,WAAP,CAAmB0G,QAAnB,CAAJ,EAAkC;iBACrB,IAAX;;SAGGP,KAAL,CAAWS,QAAX,GAAsB,UAAtB;QAEID,UAAJ,EAAgB;WACTR,KAAL,CAAWU,IAAX,GAAkB,CAAlB;WACKV,KAAL,CAAWW,KAAX,GAAmB,CAAnB;;QAEEJ,QAAJ,EAAc;WACPP,KAAL,CAAWY,GAAX,GAAiB,CAAjB;WACKZ,KAAL,CAAWa,MAAX,GAAoB,CAApB;;GA9CM;aAwDC,mBAASf,IAAT,EAAegB,SAAf,EAA0BC,IAA1B,EAAgCC,GAAhC,EAAqC;QACxCC,SAASF,QAAQ,EAAvB;QACMG,YAAY5B,cAAcwB,SAAd,CAAlB;QACI,CAACI,SAAL,EAAgB;YACR,IAAI3D,KAAJ,CAAU,gBAAgBuD,SAAhB,GAA4B,iBAAtC,CAAN;;QAEIK,MAAMrC,SAASsC,WAAT,CAAqBF,SAArB,CAAZ;YACQA,SAAR;WACO,aAAL;;cAEQG,UAAUJ,OAAOK,CAAP,IAAYL,OAAOI,OAAnB,IAA8B,CAA9C;cACME,UAAUN,OAAOO,CAAP,IAAYP,OAAOM,OAAnB,IAA8B,CAA9C;cACIE,cAAJ,CAAmBX,SAAnB,EAA8BG,OAAOS,OAAP,IAAkB,KAAhD,EACET,OAAOU,UAAP,IAAqB,IADvB,EAC6BC,MAD7B,EACqCX,OAAOY,UAAP,IAAqB,CAD1D,EAEE,CAFF;WAAA;iBAAA;iBAAA;eAAA,EAMS,KANT,EAMgB,KANhB,EAMuB,KANvB,EAM8B,CAN9B,EAMiC,IANjC;;;WASG,gBAAL;;cAEQC,OAAOX,IAAIY,iBAAJ,IAAyBZ,IAAIa,YAA1C,CADF;iBAESC,QAAP,CAAgBhB,MAAhB,EAAwB;wBACV,IADU;qBAEb,KAFa;oBAGd,KAHc;sBAIZ,KAJY;qBAKb,KALa;qBAMbjG,SANa;sBAOZA;WAPZ;eASK8F,SAAL,EAAgBG,OAAOS,OAAP,IAAkB,KAAlC,EACET,OAAOU,UADT,EACqBC,MADrB,EAEEX,OAAOiB,OAFT,EAEkBjB,OAAOkB,MAFzB,EAGElB,OAAOmB,QAHT,EAGmBnB,OAAOoB,OAH1B,EAIEpB,OAAOqB,OAJT,EAIkBrB,OAAOsB,QAJzB;;;;;cASIC,SAAJ,CAAc1B,SAAd,EAAyBG,OAAOS,OAAP,IAAkB,KAA3C,EAAkDT,OAAOU,UAAP,IAAqB,IAAvE;;;;WAIGM,QAAP,CAAgBd,GAAhB,EAAqBH,GAArB;SACKyB,aAAL,CAAmBtB,GAAnB;GAvGQ;QAiHJ,cAASrB,IAAT,EAAe4C,KAAf,EAAsBlI,IAAtB,EAA4BmI,OAA5B,EAAqC;QACnCC,OAAOD,WAAW,KAAxB;QACI7C,KAAK+C,gBAAT,EAA2B;WACpBA,gBAAL,CAAsBH,KAAtB,EAA6BlI,IAA7B,EAAmCoI,IAAnC;KADF,MAEO,IAAI9C,KAAKgD,WAAT,EAAsB;WACtBA,WAAL,CAAiB,OAAOJ,KAAxB,EAA+BlI,IAA/B;;WAEKqF,GAAP;GAxHQ;UAkIF,gBAASC,IAAT,EAAe4C,KAAf,EAAsBlI,IAAtB,EAA4BmI,OAA5B,EAAqC;QACrCC,OAAOD,WAAW,KAAxB;QACI7C,KAAKiD,mBAAT,EAA8B;WACvBA,mBAAL,CAAyBL,KAAzB,EAAgClI,IAAhC,EAAsCoI,IAAtC;KADF,MAEO,IAAI9C,KAAKkD,WAAT,EAAsB;WACtBA,WAAL,CAAiB,OAAON,KAAxB,EAA+BlI,IAA/B;;WAEKqF,GAAP;GAzIQ;YAiJA,kBAASC,IAAT,EAAeoB,SAAf,EAA0B;QAC9BpB,KAAKoB,SAAL,KAAmBlG,SAAvB,EAAkC;WAC3BkG,SAAL,GAAiBA,SAAjB;KADF,MAEO,IAAIpB,KAAKoB,SAAL,KAAmBA,SAAvB,EAAkC;UACjC+B,UAAUnD,KAAKoB,SAAL,CAAegC,KAAf,CAAqB,IAArB,CAAhB;UACID,QAAQE,OAAR,CAAgBjC,SAAhB,MAA+B,CAAC,CAApC,EAAuC;gBAC7BkC,IAAR,CAAalC,SAAb;aACKA,SAAL,GAAiB+B,QAAQI,IAAR,CAAa,GAAb,EAAkBC,OAAlB,CAA0B,MAA1B,EAAkC,EAAlC,EAAsCA,OAAtC,CAA8C,MAA9C,EAAsD,EAAtD,CAAjB;;;WAGGzD,GAAP;GA3JQ;eAmKG,qBAASC,IAAT,EAAeoB,SAAf,EAA0B;QACjCA,SAAJ,EAAe;UACTpB,KAAKoB,SAAL,KAAmBA,SAAvB,EAAkC;aAC3BqC,eAAL,CAAqB,OAArB;OADF,MAEO;YACCN,UAAUnD,KAAKoB,SAAL,CAAegC,KAAf,CAAqB,IAArB,CAAhB;YACMM,QAAQP,QAAQE,OAAR,CAAgBjC,SAAhB,CAAd;YACIsC,UAAU,CAAC,CAAf,EAAkB;kBACRC,MAAR,CAAeD,KAAf,EAAsB,CAAtB;eACKtC,SAAL,GAAiB+B,QAAQI,IAAR,CAAa,GAAb,CAAjB;;;KARN,MAWO;WACAnC,SAAL,GAAiBlG,SAAjB;;WAEK6E,GAAP;GAlLQ;YAqLA,kBAASC,IAAT,EAAeoB,SAAf,EAA0B;WAC3B,IAAIwC,MAAJ,CAAW,eAAexC,SAAf,GAA2B,YAAtC,EAAoDzF,IAApD,CAAyDqE,KAAKoB,SAA9D,KAA4E,KAAnF;GAtLQ;YA6LA,kBAASpB,IAAT,EAAe;QACjBE,QAAQ2D,iBAAiB7D,IAAjB,CAAd;WAEOJ,iBAAiBM,MAAM,mBAAN,CAAjB,IACLN,iBAAiBM,MAAM,oBAAN,CAAjB,CADK,GAELN,iBAAiBM,MAAM,cAAN,CAAjB,CAFK,GAGLN,iBAAiBM,MAAM,eAAN,CAAjB,CAHK,GAILN,iBAAiBM,MAAM4D,KAAvB,CAJF;GAhMQ;aA2MC,mBAAS9D,IAAT,EAAe;QAClBE,QAAQ2D,iBAAiB7D,IAAjB,CAAd;WAEOJ,iBAAiBM,MAAM,kBAAN,CAAjB,IACLN,iBAAiBM,MAAM,qBAAN,CAAjB,CADK,GAELN,iBAAiBM,MAAM,aAAN,CAAjB,CAFK,GAGLN,iBAAiBM,MAAM,gBAAN,CAAjB,CAHK,GAILN,iBAAiBM,MAAM6D,MAAvB,CAJF;GA9MQ;aAyNC,mBAASC,EAAT,EAAa;QAClBhE,OAAOgE,EAAX;QACMC,SAAS,EAAErD,MAAM,CAAR,EAAWE,KAAK,CAAhB,EAAf;QACId,KAAKkE,YAAT,EAAuB;SAClB;eACMtD,IAAP,IAAeZ,KAAKmE,UAApB;eACOrD,GAAP,IAAcd,KAAKoE,SAAnB;eACOpE,KAAKkE,YAAZ;OAHF,QAISlE,IAJT;;WAMKiE,MAAP;GAnOQ;YA2OA,kBAASjE,IAAT,EAAe;WAChBA,SAAShB,SAASqF,aAAlB,KAAoCrE,KAAKsE,IAAL,IAAatE,KAAKuE,IAAtD,CAAP;;CA5OJ;;IC1BMC;;6BACQ5F,MAAZ,EAAoBC,QAApB,EAA8B;;sIACtBD,MADsB,EACdC,QADc;QAGtB4F,cAAN;WACKC,MAAL,GAAc,OAAKpF,QAAL,EAAd;WAEKqF,UAAL,GAAkB3F,SAASC,aAAT,CAAuB,OAAvB,CAAlB;WACK0F,UAAL,CAAgBC,YAAhB,CAA6B,MAA7B,EAAqC,UAArC;aAESC,QAAT,GAAoB;YACZC,QAAN,CAAe,CAACL,MAAMC,MAAtB;;QAGE1K,IAAJ,CAAS,OAAK2K,UAAd,EAA0B,QAA1B,EAAoCE,QAApC,EAA8C,KAA9C;WAEK9F,UAAL,CAAgBgG,WAAhB,CAA4B,OAAKJ,UAAjC;WAGKtF,aAAL;;;;;6BAGO1G,GAAG;UACJsD,yIAA0BtD,CAA1B,CAAN;UACI,KAAKwG,gBAAT,EAA2B;aACpBA,gBAAL,CAAsB3F,IAAtB,CAA2B,IAA3B,EAAiC,KAAK8F,QAAL,EAAjC;;WAEGoF,MAAL,GAAc,KAAKpF,QAAL,EAAd;aACOrD,QAAP;;;;oCAGc;UACV,KAAKqD,QAAL,OAAoB,IAAxB,EAA8B;aACvBqF,UAAL,CAAgBC,YAAhB,CAA6B,SAA7B,EAAwC,SAAxC;aACKD,UAAL,CAAgBK,OAAhB,GAA0B,IAA1B;aACKN,MAAL,GAAc,IAAd;OAHF,MAIO;aACAC,UAAL,CAAgBK,OAAhB,GAA0B,KAA1B;aACKN,MAAL,GAAc,KAAd;;;;;;EAtC0B/F;;ICI1BsG;;4BACQrG,MAAZ,EAAoBC,QAApB,EAA8BqG,IAA9B,EAAoC;;oIAC5BtG,MAD4B,EACpBC,QADoB;QAG9BsG,UAAUD,IAAd;QAEMT,cAAN;WAMKW,QAAL,GAAgBpG,SAASC,aAAT,CAAuB,QAAvB,CAAhB;QAEIzD,OAAOJ,OAAP,CAAe+J,OAAf,CAAJ,EAA6B;UACrBE,MAAM,EAAZ;aACO9L,IAAP,CAAY4L,OAAZ,EAAqB,UAASG,OAAT,EAAkB;YACjCA,OAAJ,IAAeA,OAAf;OADF;gBAGUD,GAAV;;WAGK9L,IAAP,CAAY4L,OAAZ,EAAqB,UAAS5H,KAAT,EAAgBzD,GAAhB,EAAqB;UAClCyL,MAAMvG,SAASC,aAAT,CAAuB,QAAvB,CAAZ;UACIuG,SAAJ,GAAgB1L,GAAhB;UACI8K,YAAJ,CAAiB,OAAjB,EAA0BrH,KAA1B;YACM6H,QAAN,CAAeL,WAAf,CAA2BQ,GAA3B;KAJF;WAQKlG,aAAL;QAEIrF,IAAJ,CAAS,OAAKoL,QAAd,EAAwB,QAAxB,EAAkC,YAAW;UACrCK,eAAe,KAAKN,OAAL,CAAa,KAAKO,aAAlB,EAAiCnI,KAAtD;YACMuH,QAAN,CAAeW,YAAf;KAFF;WAKK1G,UAAL,CAAgBgG,WAAhB,CAA4B,OAAKK,QAAjC;;;;;6BAGOzM,GAAG;UACJsD,uIAA0BtD,CAA1B,CAAN;UAEI,KAAKwG,gBAAT,EAA2B;aACpBA,gBAAL,CAAsB3F,IAAtB,CAA2B,IAA3B,EAAiC,KAAK8F,QAAL,EAAjC;;aAEKrD,QAAP;;;;oCAGc;UACV8D,IAAI4F,QAAJ,CAAa,KAAKP,QAAlB,CAAJ,EAAiC,OAAO,IAAP,CADnB;WAETA,QAAL,CAAc7H,KAAd,GAAsB,KAAK+B,QAAL,EAAtB;;;;;EAnD2BX;;ICJzBiH;;4BACQhH,MAAZ,EAAoBC,QAApB,EAA8B;;oIACtBD,MADsB,EACdC,QADc;QAGtB4F,cAAN;aAESI,QAAT,GAAoB;YACZC,QAAN,CAAeL,MAAMoB,OAAN,CAActI,KAA7B;;aAGOuI,MAAT,GAAkB;UACZrB,MAAMtF,gBAAV,EAA4B;cACpBA,gBAAN,CAAuB3F,IAAvB,CAA4BiL,KAA5B,EAAmCA,MAAMnF,QAAN,EAAnC;;;WAICuG,OAAL,GAAe7G,SAASC,aAAT,CAAuB,OAAvB,CAAf;WACK4G,OAAL,CAAajB,YAAb,CAA0B,MAA1B,EAAkC,MAAlC;QAEI5K,IAAJ,CAAS,OAAK6L,OAAd,EAAuB,OAAvB,EAAgChB,QAAhC;QACI7K,IAAJ,CAAS,OAAK6L,OAAd,EAAuB,QAAvB,EAAiChB,QAAjC;QACI7K,IAAJ,CAAS,OAAK6L,OAAd,EAAuB,MAAvB,EAA+BC,MAA/B;QACI9L,IAAJ,CAAS,OAAK6L,OAAd,EAAuB,SAAvB,EAAkC,UAASnG,CAAT,EAAY;UACxCA,EAAE8C,OAAF,KAAc,EAAlB,EAAsB;aACfuD,IAAL;;KAFJ;WAMK1G,aAAL;WAEKN,UAAL,CAAgBgG,WAAhB,CAA4B,OAAKc,OAAjC;;;;;oCAGc;UAGV,CAAC9F,IAAI4F,QAAJ,CAAa,KAAKE,OAAlB,CAAL,EAAiC;aAC1BA,OAAL,CAAatI,KAAb,GAAqB,KAAK+B,QAAL,EAArB;;;;;;EArCyBX;;ACR/B,SAASqH,WAAT,CAAqBxE,CAArB,EAAwB;MAChByE,KAAKzE,EAAEvJ,QAAF,EAAX;MACIgO,GAAG5C,OAAH,CAAW,GAAX,IAAkB,CAAC,CAAvB,EAA0B;WACjB4C,GAAGnN,MAAH,GAAYmN,GAAG5C,OAAH,CAAW,GAAX,CAAZ,GAA8B,CAArC;;SAGK,CAAP;;IAeI6C;;4BACQtH,MAAZ,EAAoBC,QAApB,EAA8BsC,MAA9B,EAAsC;;mIAC9BvC,MAD8B,EACtBC,QADsB;QAG9BsH,UAAUhF,UAAU,EAA1B;UAEKiF,KAAL,GAAaD,QAAQlJ,GAArB;UACKoJ,KAAL,GAAaF,QAAQjJ,GAArB;UACKoJ,MAAL,GAAcH,QAAQI,IAAtB;QAEI/K,OAAOzB,WAAP,CAAmB,MAAKuM,MAAxB,CAAJ,EAAqC;UAC/B,MAAKxH,YAAL,KAAsB,CAA1B,EAA6B;cACtB0H,aAAL,GAAqB,CAArB,CAD2B;OAA7B,MAEO;cAEAA,aAAL,GAAqBrO,KAAKsO,GAAL,CAAS,EAAT,EAAatO,KAAKwE,KAAL,CAAWxE,KAAKuO,GAAL,CAASvO,KAAKwO,GAAL,CAAS,MAAK7H,YAAd,CAAT,IAAwC3G,KAAKyO,IAAxD,CAAb,IAA8E,EAAnG;;KALJ,MAOO;YACAJ,aAAL,GAAqB,MAAKF,MAA1B;;UAGGO,WAAL,GAAmBb,YAAY,MAAKQ,aAAjB,CAAnB;;;;;6BAGO7N,GAAG;UACNmO,KAAKnO,CAAT;UAEI,KAAKyN,KAAL,KAAelL,SAAf,IAA4B4L,KAAK,KAAKV,KAA1C,EAAiD;aAC1C,KAAKA,KAAV;OADF,MAEO,IAAI,KAAKC,KAAL,KAAenL,SAAf,IAA4B4L,KAAK,KAAKT,KAA1C,EAAiD;aACjD,KAAKA,KAAV;;UAGE,KAAKC,MAAL,KAAgBpL,SAAhB,IAA6B4L,KAAK,KAAKR,MAAV,KAAqB,CAAtD,EAAyD;aAClDnO,KAAKC,KAAL,CAAW0O,KAAK,KAAKR,MAArB,IAA+B,KAAKA,MAAzC;;yIAGoBQ,EAAtB;;;;wBAUEC,UAAU;WACPX,KAAL,GAAaW,QAAb;aACO,IAAP;;;;wBAUEC,UAAU;WACPX,KAAL,GAAaW,QAAb;aACO,IAAP;;;;yBAaGC,WAAW;WACTX,MAAL,GAAcW,SAAd;WACKT,aAAL,GAAqBS,SAArB;WACKJ,WAAL,GAAmBb,YAAYiB,SAAZ,CAAnB;aACO,IAAP;;;;EA9E2BtI;;ACpB/B,SAASuI,cAAT,CAAwB3J,KAAxB,EAA+B4J,QAA/B,EAAyC;MACjCC,QAAQjP,KAAKsO,GAAL,CAAS,EAAT,EAAaU,QAAb,CAAd;SACOhP,KAAKC,KAAL,CAAWmF,QAAQ6J,KAAnB,IAA4BA,KAAnC;;IAiBIC;;+BACQzI,MAAZ,EAAoBC,QAApB,EAA8BsC,MAA9B,EAAsC;;0IAC9BvC,MAD8B,EACtBC,QADsB,EACZsC,MADY;WAG/BmG,qBAAL,GAA6B,KAA7B;QAEM7C,cAAN;QAMI8C,cAAJ;aAES1C,QAAT,GAAoB;UACZ2C,YAAY1L,WAAW2I,MAAMoB,OAAN,CAActI,KAAzB,CAAlB;UACI,CAAC/B,OAAOL,KAAP,CAAaqM,SAAb,CAAL,EAA8B;cACtB1C,QAAN,CAAe0C,SAAf;;;aAIKC,QAAT,GAAoB;UACdhD,MAAMtF,gBAAV,EAA4B;cACpBA,gBAAN,CAAuB3F,IAAvB,CAA4BiL,KAA5B,EAAmCA,MAAMnF,QAAN,EAAnC;;;aAIKwG,MAAT,GAAkB;;;aAIT4B,WAAT,CAAqBhI,CAArB,EAAwB;UAChBiI,OAAOJ,QAAQ7H,EAAE+B,OAAvB;YACMqD,QAAN,CAAeL,MAAMnF,QAAN,KAAmBqI,OAAOlD,MAAM+B,aAA/C;cAEQ9G,EAAE+B,OAAV;;aAGOmG,SAAT,GAAqB;UACfC,MAAJ,CAAW/F,MAAX,EAAmB,WAAnB,EAAgC4F,WAAhC;UACIG,MAAJ,CAAW/F,MAAX,EAAmB,SAAnB,EAA8B8F,SAA9B;;;aAIOE,WAAT,CAAqBpI,CAArB,EAAwB;UAClB1F,IAAJ,CAAS8H,MAAT,EAAiB,WAAjB,EAA8B4F,WAA9B;UACI1N,IAAJ,CAAS8H,MAAT,EAAiB,SAAjB,EAA4B8F,SAA5B;cACQlI,EAAE+B,OAAV;;WAGGoE,OAAL,GAAe7G,SAASC,aAAT,CAAuB,OAAvB,CAAf;WACK4G,OAAL,CAAajB,YAAb,CAA0B,MAA1B,EAAkC,MAAlC;QAII5K,IAAJ,CAAS,OAAK6L,OAAd,EAAuB,QAAvB,EAAiChB,QAAjC;QACI7K,IAAJ,CAAS,OAAK6L,OAAd,EAAuB,MAAvB,EAA+BC,MAA/B;QACI9L,IAAJ,CAAS,OAAK6L,OAAd,EAAuB,WAAvB,EAAoCiC,WAApC;QACI9N,IAAJ,CAAS,OAAK6L,OAAd,EAAuB,SAAvB,EAAkC,UAASnG,CAAT,EAAY;UAExCA,EAAE8C,OAAF,KAAc,EAAlB,EAAsB;cACd8E,qBAAN,GAA8B,IAA9B;aACKvB,IAAL;cACMuB,qBAAN,GAA8B,KAA9B;;;KALJ;WAUKjI,aAAL;WAEKN,UAAL,CAAgBgG,WAAhB,CAA4B,OAAKc,OAAjC;;;;;oCAGc;WACTA,OAAL,CAAatI,KAAb,GAAqB,KAAK+J,qBAAL,GAA6B,KAAKhI,QAAL,EAA7B,GAA+C4H,eAAe,KAAK5H,QAAL,EAAf,EAAgC,KAAKuH,WAArC,CAApE;;;;;EA1E8BX;;ACpBlC,SAASb,GAAT,CAAa1M,CAAb,EAAgBoP,EAAhB,EAAoBC,EAApB,EAAwBC,EAAxB,EAA4BC,EAA5B,EAAgC;SACvBD,KAAK,CAACC,KAAKD,EAAN,KAAa,CAACtP,IAAIoP,EAAL,KAAYC,KAAKD,EAAjB,CAAb,CAAZ;;IAmBII;;kCACQvJ,MAAZ,EAAoBC,QAApB,EAA8B5B,GAA9B,EAAmCC,GAAnC,EAAwCqJ,IAAxC,EAA8C;;gJACtC3H,MADsC,EAC9BC,QAD8B,EACpB,EAAE5B,KAAKA,GAAP,EAAYC,KAAKA,GAAjB,EAAsBqJ,MAAMA,IAA5B,EADoB;QAGtC9B,cAAN;WAEK2D,YAAL,GAAoBpJ,SAASC,aAAT,CAAuB,KAAvB,CAApB;WACKoJ,YAAL,GAAoBrJ,SAASC,aAAT,CAAuB,KAAvB,CAApB;QAEIjF,IAAJ,CAAS,OAAKoO,YAAd,EAA4B,WAA5B,EAAyCN,WAAzC;QACI9N,IAAJ,CAAS,OAAKoO,YAAd,EAA4B,YAA5B,EAA0CE,YAA1C;QAEIC,QAAJ,CAAa,OAAKH,YAAlB,EAAgC,QAAhC;QACIG,QAAJ,CAAa,OAAKF,YAAlB,EAAgC,WAAhC;aAESP,WAAT,CAAqBpI,CAArB,EAAwB;eACb2E,aAAT,CAAuB0B,IAAvB;UAEI/L,IAAJ,CAAS8H,MAAT,EAAiB,WAAjB,EAA8B4F,WAA9B;UACI1N,IAAJ,CAAS8H,MAAT,EAAiB,SAAjB,EAA4B8F,SAA5B;kBAEYlI,CAAZ;;aAGOgI,WAAT,CAAqBhI,CAArB,EAAwB;QACpB8I,cAAF;UAEMC,SAAShE,MAAM2D,YAAN,CAAmBM,qBAAnB,EAAf;YAEM5D,QAAN,CACEO,IAAI3F,EAAE6B,OAAN,EAAekH,OAAO7H,IAAtB,EAA4B6H,OAAO5H,KAAnC,EAA0C4D,MAAM2B,KAAhD,EAAuD3B,MAAM4B,KAA7D,CADF;aAIO,KAAP;;aAGOuB,SAAT,GAAqB;UACfC,MAAJ,CAAW/F,MAAX,EAAmB,WAAnB,EAAgC4F,WAAhC;UACIG,MAAJ,CAAW/F,MAAX,EAAmB,SAAnB,EAA8B8F,SAA9B;UACInD,MAAMtF,gBAAV,EAA4B;cACpBA,gBAAN,CAAuB3F,IAAvB,CAA4BiL,KAA5B,EAAmCA,MAAMnF,QAAN,EAAnC;;;aAIKgJ,YAAT,CAAsB5I,CAAtB,EAAyB;UACnBA,EAAEiJ,OAAF,CAAU7P,MAAV,KAAqB,CAAzB,EAA4B;;;UACxBkB,IAAJ,CAAS8H,MAAT,EAAiB,WAAjB,EAA8B8G,WAA9B;UACI5O,IAAJ,CAAS8H,MAAT,EAAiB,UAAjB,EAA6B+G,UAA7B;kBACYnJ,CAAZ;;aAGOkJ,WAAT,CAAqBlJ,CAArB,EAAwB;UAChB6B,UAAU7B,EAAEiJ,OAAF,CAAU,CAAV,EAAapH,OAA7B;UACMkH,SAAShE,MAAM2D,YAAN,CAAmBM,qBAAnB,EAAf;YAEM5D,QAAN,CACEO,IAAI9D,OAAJ,EAAakH,OAAO7H,IAApB,EAA0B6H,OAAO5H,KAAjC,EAAwC4D,MAAM2B,KAA9C,EAAqD3B,MAAM4B,KAA3D,CADF;;aAKOwC,UAAT,GAAsB;UAChBhB,MAAJ,CAAW/F,MAAX,EAAmB,WAAnB,EAAgC8G,WAAhC;UACIf,MAAJ,CAAW/F,MAAX,EAAmB,UAAnB,EAA+B+G,UAA/B;UACIpE,MAAMtF,gBAAV,EAA4B;cACpBA,gBAAN,CAAuB3F,IAAvB,CAA4BiL,KAA5B,EAAmCA,MAAMnF,QAAN,EAAnC;;;WAICD,aAAL;WAEK+I,YAAL,CAAkBrD,WAAlB,CAA8B,OAAKsD,YAAnC;WACKtJ,UAAL,CAAgBgG,WAAhB,CAA4B,OAAKqD,YAAjC;;;;;oCAGc;UACRU,MAAM,CAAC,KAAKxJ,QAAL,KAAkB,KAAK8G,KAAxB,KAAkC,KAAKC,KAAL,GAAa,KAAKD,KAApD,CAAZ;WACKiC,YAAL,CAAkBnI,KAAlB,CAAwB4D,KAAxB,GAAgCgF,MAAM,GAAN,GAAY,GAA5C;;;;;EA5EiC5C;;ICZ/B6C;;8BACQnK,MAAZ,EAAoBC,QAApB,EAA8BmK,IAA9B,EAAoC;;wIAC5BpK,MAD4B,EACpBC,QADoB;QAG5B4F,cAAN;WAEKwE,QAAL,GAAgBjK,SAASC,aAAT,CAAuB,KAAvB,CAAhB;WACKgK,QAAL,CAAczD,SAAd,GAA0BwD,SAAS9N,SAAT,GAAqB,MAArB,GAA8B8N,IAAxD;QAEIhP,IAAJ,CAAS,OAAKiP,QAAd,EAAwB,OAAxB,EAAiC,UAASvJ,CAAT,EAAY;QACzC8I,cAAF;YACMU,IAAN;aACO,KAAP;KAHF;QAMIX,QAAJ,CAAa,OAAKU,QAAlB,EAA4B,QAA5B;WAEKlK,UAAL,CAAgBgG,WAAhB,CAA4B,OAAKkE,QAAjC;;;;;2BAGK;UACD,KAAK/J,UAAT,EAAqB;aACdA,UAAL,CAAgB1F,IAAhB,CAAqB,IAArB;;WAEG8F,QAAL,GAAgB9F,IAAhB,CAAqB,KAAKoF,MAA1B;UACI,KAAKO,gBAAT,EAA2B;aACpBA,gBAAL,CAAsB3F,IAAtB,CAA2B,IAA3B,EAAiC,KAAK8F,QAAL,EAAjC;;;;;EA1B2BX;;ICA3BwK;;2BACQvK,MAAZ,EAAoBC,QAApB,EAA8B;;kIACtBD,MADsB,EACdC,QADc;WAGvBuK,OAAL,GAAe,IAAI5L,KAAJ,CAAU,OAAK8B,QAAL,EAAV,CAAf;WACK+J,MAAL,GAAc,IAAI7L,KAAJ,CAAU,CAAV,CAAd;QAEMiH,cAAN;WAEK1F,UAAL,GAAkBC,SAASC,aAAT,CAAuB,KAAvB,CAAlB;QAEIqK,cAAJ,CAAmB,OAAKvK,UAAxB,EAAoC,KAApC;WAEKwK,UAAL,GAAkBvK,SAASC,aAAT,CAAuB,KAAvB,CAAlB;WACKsK,UAAL,CAAgBnI,SAAhB,GAA4B,UAA5B;WAEKoI,kBAAL,GAA0BxK,SAASC,aAAT,CAAuB,KAAvB,CAA1B;WACKuK,kBAAL,CAAwBpI,SAAxB,GAAoC,kBAApC;WAEKqI,YAAL,GAAoBzK,SAASC,aAAT,CAAuB,KAAvB,CAApB;WACKwK,YAAL,CAAkBrI,SAAlB,GAA8B,YAA9B;WACKsI,mBAAL,GAA2B,YAA3B;WAEKC,UAAL,GAAkB3K,SAASC,aAAT,CAAuB,KAAvB,CAAlB;WACK0K,UAAL,CAAgBvI,SAAhB,GAA4B,UAA5B;WAEKwI,WAAL,GAAmB5K,SAASC,aAAT,CAAuB,KAAvB,CAAnB;WACK2K,WAAL,CAAiBxI,SAAjB,GAA6B,WAA7B;WAEKyE,OAAL,GAAe7G,SAASC,aAAT,CAAuB,OAAvB,CAAf;WACK4G,OAAL,CAAavB,IAAb,GAAoB,MAApB;WACKuF,kBAAL,GAA0B,YAA1B;QAEI7P,IAAJ,CAAS,OAAK6L,OAAd,EAAuB,SAAvB,EAAkC,UAASnG,CAAT,EAAY;UACxCA,EAAE8C,OAAF,KAAc,EAAlB,EAAsB;eACbhJ,IAAP,CAAY,IAAZ;;KAFJ;QAMIQ,IAAJ,CAAS,OAAK6L,OAAd,EAAuB,MAAvB,EAA+BC,MAA/B;QAEI9L,IAAJ,CAAS,OAAKuP,UAAd,EAA0B,WAA1B,EAAuC,YAAkB;UAEpDhB,QADH,CACY,IADZ,EACkB,MADlB,EAEGvO,IAFH,CAEQ8H,MAFR,EAEgB,SAFhB,EAE2B,YAAkB;YACrCgI,WAAJ,CAAgBrF,MAAM8E,UAAtB,EAAkC,MAAlC;OAHJ;KADF;QAQIvP,IAAJ,CAAS,OAAKuP,UAAd,EAA0B,YAA1B,EAAwC,YAAkB;UAErDhB,QADH,CACY,IADZ,EACkB,MADlB,EAEGvO,IAFH,CAEQ8H,MAFR,EAEgB,UAFhB,EAE4B,YAAkB;YACtCgI,WAAJ,CAAgBrF,MAAM8E,UAAtB,EAAkC,MAAlC;OAHJ;KADF;QAQMQ,aAAa/K,SAASC,aAAT,CAAuB,KAAvB,CAAnB;WAEOX,MAAP,CAAc,OAAKiL,UAAL,CAAgBrJ,KAA9B,EAAqC;aAC5B,OAD4B;cAE3B,OAF2B;eAG1B,KAH0B;uBAIlB,MAJkB;iBAKxB;KALb;WAQO5B,MAAP,CAAc,OAAKmL,YAAL,CAAkBvJ,KAAhC,EAAuC;gBAC3B,UAD2B;aAE9B,MAF8B;cAG7B,MAH6B;cAI7B,OAAKwJ,mBAAL,IAA4B,OAAKN,OAAL,CAAazQ,CAAb,GAAiB,GAAjB,GAAuB,MAAvB,GAAgC,MAA5D,CAJ6B;iBAK1B,6BAL0B;oBAMvB,MANuB;cAO7B;KAPV;WAUO2F,MAAP,CAAc,OAAKqL,UAAL,CAAgBzJ,KAA9B,EAAqC;gBACzB,UADyB;aAE5B,MAF4B;cAG3B,KAH2B;mBAItB,gBAJsB;cAK3B;KALV;WAQO5B,MAAP,CAAc,OAAKkL,kBAAL,CAAwBtJ,KAAtC,EAA6C;aACpC,OADoC;cAEnC,OAFmC;cAGnC,gBAHmC;mBAI9B,KAJ8B;eAKlC,cALkC;cAMnC;KANV;WASO5B,MAAP,CAAcyL,WAAW7J,KAAzB,EAAgC;aACvB,MADuB;cAEtB,MAFsB;kBAGlB;KAHd;mBAMe6J,UAAf,EAA2B,KAA3B,EAAkC,eAAlC,EAAmD,MAAnD;WAEOzL,MAAP,CAAc,OAAKsL,WAAL,CAAiB1J,KAA/B,EAAsC;aAC7B,MAD6B;cAE5B,OAF4B;cAG5B,gBAH4B;cAI5B,WAJ4B;gBAK1B,UAL0B;WAM/B,KAN+B;aAO7B;KAPT;gBAUY,OAAK0J,WAAjB;WAEOtL,MAAP,CAAc,OAAKuH,OAAL,CAAa3F,KAA3B,EAAkC;eACvB,MADuB;iBAGrB,QAHqB;aAMzB,MANyB;cAOxB,CAPwB;kBAQpB,MARoB;kBASpB,OAAK2J,kBAAL,GAA0B;KATxC;QAYI7P,IAAJ,CAAS,OAAKwP,kBAAd,EAAkC,WAAlC,EAA+CQ,SAA/C;QACIhQ,IAAJ,CAAS,OAAKwP,kBAAd,EAAkC,YAAlC,EAAgDQ,SAAhD;QAEIhQ,IAAJ,CAAS,OAAKyP,YAAd,EAA4B,WAA5B,EAAyCO,SAAzC;QACIhQ,IAAJ,CAAS,OAAKyP,YAAd,EAA4B,YAA5B,EAA0CO,SAA1C;QAEIhQ,IAAJ,CAAS,OAAK4P,WAAd,EAA2B,WAA3B,EAAwCK,UAAxC;QACIjQ,IAAJ,CAAS,OAAK4P,WAAd,EAA2B,YAA3B,EAAyCK,UAAzC;aAESD,SAAT,CAAmBtK,CAAnB,EAAsB;YACdA,CAAN;UACI1F,IAAJ,CAAS8H,MAAT,EAAiB,WAAjB,EAA8BoI,KAA9B;UACIlQ,IAAJ,CAAS8H,MAAT,EAAiB,WAAjB,EAA8BoI,KAA9B;UACIlQ,IAAJ,CAAS8H,MAAT,EAAiB,SAAjB,EAA4BqI,SAA5B;UACInQ,IAAJ,CAAS8H,MAAT,EAAiB,UAAjB,EAA6BqI,SAA7B;;aAGOF,UAAT,CAAoBvK,CAApB,EAAuB;WAChBA,CAAL;UACI1F,IAAJ,CAAS8H,MAAT,EAAiB,WAAjB,EAA8BsI,IAA9B;UACIpQ,IAAJ,CAAS8H,MAAT,EAAiB,WAAjB,EAA8BsI,IAA9B;UACIpQ,IAAJ,CAAS8H,MAAT,EAAiB,SAAjB,EAA4BuI,QAA5B;UACIrQ,IAAJ,CAAS8H,MAAT,EAAiB,UAAjB,EAA6BuI,QAA7B;;aAGOF,SAAT,GAAqB;UACftC,MAAJ,CAAW/F,MAAX,EAAmB,WAAnB,EAAgCoI,KAAhC;UACIrC,MAAJ,CAAW/F,MAAX,EAAmB,WAAnB,EAAgCoI,KAAhC;UACIrC,MAAJ,CAAW/F,MAAX,EAAmB,SAAnB,EAA8BqI,SAA9B;UACItC,MAAJ,CAAW/F,MAAX,EAAmB,UAAnB,EAA+BqI,SAA/B;;;aAIOE,QAAT,GAAoB;UACdxC,MAAJ,CAAW/F,MAAX,EAAmB,WAAnB,EAAgCsI,IAAhC;UACIvC,MAAJ,CAAW/F,MAAX,EAAmB,WAAnB,EAAgCsI,IAAhC;UACIvC,MAAJ,CAAW/F,MAAX,EAAmB,SAAnB,EAA8BuI,QAA9B;UACIxC,MAAJ,CAAW/F,MAAX,EAAmB,UAAnB,EAA+BuI,QAA/B;;;aAIOvE,MAAT,GAAkB;UACV3L,IAAI+B,UAAU,KAAKqB,KAAf,CAAV;UACIpD,MAAM,KAAV,EAAiB;cACTiP,OAAN,CAAcrR,OAAd,GAAwBoC,CAAxB;cACM2K,QAAN,CAAeL,MAAM2E,OAAN,CAAckB,UAAd,EAAf;OAFF,MAGO;aACA/M,KAAL,GAAakH,MAAM2E,OAAN,CAAcnR,QAAd,EAAb;;;aAIKwP,QAAT,GAAoB;UACdhD,MAAMtF,gBAAV,EAA4B;cACpBA,gBAAN,CAAuB3F,IAAvB,CAA4BiL,KAA5B,EAAmCA,MAAM2E,OAAN,CAAckB,UAAd,EAAnC;;;WAICd,kBAAL,CAAwBzE,WAAxB,CAAoCgF,UAApC;WACKR,UAAL,CAAgBxE,WAAhB,CAA4B,OAAK0E,YAAjC;WACKF,UAAL,CAAgBxE,WAAhB,CAA4B,OAAKyE,kBAAjC;WACKD,UAAL,CAAgBxE,WAAhB,CAA4B,OAAK6E,WAAjC;WACKA,WAAL,CAAiB7E,WAAjB,CAA6B,OAAK4E,UAAlC;WAEK5K,UAAL,CAAgBgG,WAAhB,CAA4B,OAAKc,OAAjC;WACK9G,UAAL,CAAgBgG,WAAhB,CAA4B,OAAKwE,UAAjC;WAEKlK,aAAL;aAES6K,KAAT,CAAexK,CAAf,EAAkB;UACZA,EAAE4E,IAAF,CAAOjB,OAAP,CAAe,OAAf,MAA4B,CAAC,CAAjC,EAAoC;UAAImF,cAAF;;UAEhC+B,YAAY9F,MAAM+E,kBAAN,CAAyBd,qBAAzB,EAAlB;iBAC8BhJ,EAAEiJ,OAAF,IAAajJ,EAAEiJ,OAAF,CAAU,CAAV,CAAd,IAA+BjJ,CAJ5C;UAIR6B,OAJQ,QAIRA,OAJQ;UAICE,OAJD,QAICA,OAJD;UAKZhJ,IAAI,CAAC8I,UAAUgJ,UAAU3J,IAArB,KAA8B2J,UAAU1J,KAAV,GAAkB0J,UAAU3J,IAA1D,CAAR;UACIjI,IAAI,IAAI,CAAC8I,UAAU8I,UAAUzJ,GAArB,KAA6ByJ,UAAUxJ,MAAV,GAAmBwJ,UAAUzJ,GAA1D,CAAZ;UAEInI,IAAI,CAAR,EAAW;YACL,CAAJ;OADF,MAEO,IAAIA,IAAI,CAAR,EAAW;YACZ,CAAJ;;UAGEF,IAAI,CAAR,EAAW;YACL,CAAJ;OADF,MAEO,IAAIA,IAAI,CAAR,EAAW;YACZ,CAAJ;;YAGI2Q,OAAN,CAAczQ,CAAd,GAAkBA,CAAlB;YACMyQ,OAAN,CAAc3Q,CAAd,GAAkBA,CAAlB;YAEMqM,QAAN,CAAeL,MAAM2E,OAAN,CAAckB,UAAd,EAAf;aAGO,KAAP;;aAGOF,IAAT,CAAc1K,CAAd,EAAiB;UACXA,EAAE4E,IAAF,CAAOjB,OAAP,CAAe,OAAf,MAA4B,CAAC,CAAjC,EAAoC;UAAImF,cAAF;;UAEhC+B,YAAY9F,MAAMmF,WAAN,CAAkBlB,qBAAlB,EAAlB;kBACqBhJ,EAAEiJ,OAAF,IAAajJ,EAAEiJ,OAAF,CAAU,CAAV,CAAd,IAA+BjJ,CAJpC;UAIP+B,OAJO,SAIPA,OAJO;UAKXjJ,IAAI,IAAI,CAACiJ,UAAU8I,UAAUzJ,GAArB,KAA6ByJ,UAAUxJ,MAAV,GAAmBwJ,UAAUzJ,GAA1D,CAAZ;UAEItI,IAAI,CAAR,EAAW;YACL,CAAJ;OADF,MAEO,IAAIA,IAAI,CAAR,EAAW;YACZ,CAAJ;;YAGI4Q,OAAN,CAAc5Q,CAAd,GAAkBA,IAAI,GAAtB;YAEMsM,QAAN,CAAeL,MAAM2E,OAAN,CAAckB,UAAd,EAAf;aAEO,KAAP;;;;;;oCAIY;UACRnQ,IAAI+B,UAAU,KAAKoD,QAAL,EAAV,CAAV;UAEInF,MAAM,KAAV,EAAiB;YACXqQ,WAAW,KAAf;eAIOjR,IAAP,CAAYiE,MAAMiB,UAAlB,EAA8B,UAASZ,SAAT,EAAoB;cAC5C,CAACrC,OAAOzB,WAAP,CAAmBI,EAAE0D,SAAF,CAAnB,CAAD,IAAqC,CAACrC,OAAOzB,WAAP,CAAmB,KAAKqP,OAAL,CAAarR,OAAb,CAAqB8F,SAArB,CAAnB,CAAtC,IACF1D,EAAE0D,SAAF,MAAiB,KAAKuL,OAAL,CAAarR,OAAb,CAAqB8F,SAArB,CADnB,EACoD;uBACvC,IAAX;mBACO,EAAP,CAFkD;;SAFtD,EAMG,IANH;YAUI2M,QAAJ,EAAc;iBACLlM,MAAP,CAAc,KAAK8K,OAAL,CAAarR,OAA3B,EAAoCoC,CAApC;;;aAIGmE,MAAP,CAAc,KAAK+K,MAAL,CAAYtR,OAA1B,EAAmC,KAAKqR,OAAL,CAAarR,OAAhD;WAEKsR,MAAL,CAAY9Q,CAAZ,GAAgB,CAAhB;UAEMkS,OAAQ,KAAKrB,OAAL,CAAazQ,CAAb,GAAiB,GAAjB,IAAwB,KAAKyQ,OAAL,CAAa3Q,CAAb,GAAiB,GAA1C,GAAiD,GAAjD,GAAuD,CAApE;UACMiS,QAAQ,MAAMD,IAApB;aAEOnM,MAAP,CAAc,KAAKmL,YAAL,CAAkBvJ,KAAhC,EAAuC;oBACzB,MAAM,KAAKkJ,OAAL,CAAa3Q,CAAnB,GAAuB,CAAvB,GAA2B,IADF;mBAE1B,OAAO,IAAI,KAAK2Q,OAAL,CAAazQ,CAAxB,IAA6B,CAA7B,GAAiC,IAFP;yBAGpB,KAAK0Q,MAAL,CAAYsB,WAAZ,EAHoB;gBAI7B,KAAKjB,mBAAL,GAA2B,MAA3B,GAAoCe,IAApC,GAA2C,GAA3C,GAAiDA,IAAjD,GAAwD,GAAxD,GAA8DA,IAA9D,GAAqE;OAJ/E;WAOKd,UAAL,CAAgBzJ,KAAhB,CAAsB0K,SAAtB,GAAkC,CAAC,IAAI,KAAKxB,OAAL,CAAa5Q,CAAb,GAAiB,GAAtB,IAA6B,GAA7B,GAAmC,IAArE;WAEK6Q,MAAL,CAAY5Q,CAAZ,GAAgB,CAAhB;WACK4Q,MAAL,CAAY1Q,CAAZ,GAAgB,CAAhB;qBAEe,KAAK6Q,kBAApB,EAAwC,MAAxC,EAAgD,MAAhD,EAAwD,KAAKH,MAAL,CAAYsB,WAAZ,EAAxD;WAEK9E,OAAL,CAAatI,KAAb,GAAqB,KAAK6L,OAAL,CAAanR,QAAb,EAArB;aAEOqG,MAAP,CAAc,KAAKuH,OAAL,CAAa3F,KAA3B,EAAkC;yBACf,KAAKkJ,OAAL,CAAauB,WAAb,EADe;eAEzB,SAASF,IAAT,GAAgB,GAAhB,GAAsBA,IAAtB,GAA6B,GAA7B,GAAmCA,IAAnC,GAA0C,GAFjB;oBAGpB,KAAKZ,kBAAL,GAA0B,OAA1B,GAAoCa,KAApC,GAA4C,GAA5C,GAAkDA,KAAlD,GAA0D,GAA1D,GAAgEA,KAAhE,GAAwE;OAHtF;;;;EAlS0B/L;AA0S9B,IAAMkM,UAAU,CAAC,OAAD,EAAU,KAAV,EAAiB,UAAjB,EAA6B,MAA7B,EAAqC,EAArC,CAAhB;AAEA,SAASC,cAAT,CAAwB9K,IAAxB,EAA8BwB,CAA9B,EAAiCjJ,CAAjC,EAAoCD,CAApC,EAAuC;OAChC4H,KAAL,CAAW6K,UAAX,GAAwB,EAAxB;SACOxR,IAAP,CAAYsR,OAAZ,EAAqB,UAASG,MAAT,EAAiB;SAC/B9K,KAAL,CAAW+K,OAAX,IAAsB,iBAAiBD,MAAjB,GAA0B,kBAA1B,GAA+CxJ,CAA/C,GAAmD,IAAnD,GAA0DjJ,CAA1D,GAA8D,OAA9D,GAAwED,CAAxE,GAA4E,UAAlG;GADF;;AAKF,SAAS4S,WAAT,CAAqBlL,IAArB,EAA2B;OACpBE,KAAL,CAAW6K,UAAX,GAAwB,EAAxB;OACK7K,KAAL,CAAW+K,OAAX,IAAsB,oIAAtB;OACK/K,KAAL,CAAW+K,OAAX,IAAsB,iIAAtB;OACK/K,KAAL,CAAW+K,OAAX,IAAsB,4HAAtB;OACK/K,KAAL,CAAW+K,OAAX,IAAsB,6HAAtB;OACK/K,KAAL,CAAW+K,OAAX,IAAsB,yHAAtB;;;ACpUF,IAAME,MAAM;QACJ,cAASC,GAAT,EAAcC,KAAd,EAAqB;QACnBC,MAAMD,SAASrM,QAArB;QACMuM,OAAOD,IAAIrM,aAAJ,CAAkB,MAAlB,CAAb;SACKqF,IAAL,GAAY,UAAZ;SACKkH,GAAL,GAAW,YAAX;SACKjH,IAAL,GAAY6G,GAAZ;QACIK,oBAAJ,CAAyB,MAAzB,EAAiC,CAAjC,EAAoC1G,WAApC,CAAgDwG,IAAhD;GAPQ;UAUF,gBAASG,UAAT,EAAqBL,KAArB,EAA4B;QAC5BC,MAAMD,SAASrM,QAArB;QACM2M,WAAW3M,SAASC,aAAT,CAAuB,OAAvB,CAAjB;aACSqF,IAAT,GAAgB,UAAhB;aACSkB,SAAT,GAAqBkG,UAArB;QACME,OAAON,IAAIG,oBAAJ,CAAyB,MAAzB,EAAiC,CAAjC,CAAb;QACI;WACG1G,WAAL,CAAiB4G,QAAjB;KADF,CAEE,OAAOjM,CAAP,EAAU;;;CAlBhB;;ACbA,IAAMmM,2rBAAN;;ACqBA,IAAMC,oBAAoB,SAApBA,iBAAoB,CAASlN,MAAT,EAAiBC,QAAjB,EAA2B;MAC7CC,eAAeF,OAAOC,QAAP,CAArB;MAGIrD,OAAOJ,OAAP,CAAe3B,UAAU,CAAV,CAAf,KAAgC+B,OAAO5B,QAAP,CAAgBH,UAAU,CAAV,CAAhB,CAApC,EAAmE;WAC1D,IAAIwL,gBAAJ,CAAqBrG,MAArB,EAA6BC,QAA7B,EAAuCpF,UAAU,CAAV,CAAvC,CAAP;;MAIE+B,OAAOO,QAAP,CAAgB+C,YAAhB,CAAJ,EAAmC;QAE7BtD,OAAOO,QAAP,CAAgBtC,UAAU,CAAV,CAAhB,KAAiC+B,OAAOO,QAAP,CAAgBtC,UAAU,CAAV,CAAhB,CAArC,EAAoE;UAE9D+B,OAAOO,QAAP,CAAgBtC,UAAU,CAAV,CAAhB,CAAJ,EAAmC;eAC1B,IAAI0O,sBAAJ,CAA2BvJ,MAA3B,EAAmCC,QAAnC,EACLpF,UAAU,CAAV,CADK,EACSA,UAAU,CAAV,CADT,EACuBA,UAAU,CAAV,CADvB,CAAP;;aAIK,IAAI0O,sBAAJ,CAA2BvJ,MAA3B,EAAmCC,QAAnC,EAA6CpF,UAAU,CAAV,CAA7C,EAA2DA,UAAU,CAAV,CAA3D,CAAP;;QAIE+B,OAAOO,QAAP,CAAgBtC,UAAU,CAAV,CAAhB,CAAJ,EAAmC;aAC1B,IAAI4N,mBAAJ,CAAwBzI,MAAxB,EAAgCC,QAAhC,EACL,EAAE5B,KAAKxD,UAAU,CAAV,CAAP,EAAqByD,KAAKzD,UAAU,CAAV,CAA1B,EAAwC8M,MAAM9M,UAAU,CAAV,CAA9C,EADK,CAAP;;WAGK,IAAI4N,mBAAJ,CAAwBzI,MAAxB,EAAgCC,QAAhC,EAA0C,EAAE5B,KAAKxD,UAAU,CAAV,CAAP,EAAqByD,KAAKzD,UAAU,CAAV,CAA1B,EAA1C,CAAP;;MAGE+B,OAAOC,QAAP,CAAgBqD,YAAhB,CAAJ,EAAmC;WAC1B,IAAI8G,gBAAJ,CAAqBhH,MAArB,EAA6BC,QAA7B,CAAP;;MAGErD,OAAOuQ,UAAP,CAAkBjN,YAAlB,CAAJ,EAAqC;WAC5B,IAAIiK,kBAAJ,CAAuBnK,MAAvB,EAA+BC,QAA/B,EAAyC,EAAzC,CAAP;;MAGErD,OAAOwQ,SAAP,CAAiBlN,YAAjB,CAAJ,EAAoC;WAC3B,IAAI0F,iBAAJ,CAAsB5F,MAAtB,EAA8BC,QAA9B,CAAP;;SAGK,IAAP;CAzCF;;ACRA,SAASoN,qBAAT,CAA+BC,QAA/B,EAAyC;aAC5BA,QAAX,EAAqB,OAAO,EAA5B;;AAGF,8BAAepK,OAAOmK,qBAAP,IACXnK,OAAOqK,2BADI,IAEXrK,OAAOsK,wBAFI,IAGXtK,OAAOuK,sBAHI,IAIXvK,OAAOwK,uBAJI,IAKXL,qBALJ;;ICDMM;yBACU;;SACPC,iBAAL,GAAyBxN,SAASC,aAAT,CAAuB,KAAvB,CAAzB;WACOX,MAAP,CAAc,KAAKkO,iBAAL,CAAuBtM,KAArC,EAA4C;uBACzB,iBADyB;WAErC,CAFqC;YAGpC,CAHoC;eAIjC,MAJiC;cAKlC,MALkC;eAMjC,CANiC;wBAOxB,qBAPwB;kBAQ9B;KARd;QAWIuM,cAAJ,CAAmB,KAAKD,iBAAxB;SACKA,iBAAL,CAAuBtM,KAAvB,CAA6BS,QAA7B,GAAwC,OAAxC;SAEK5B,UAAL,GAAkBC,SAASC,aAAT,CAAuB,KAAvB,CAAlB;WACOX,MAAP,CAAc,KAAKS,UAAL,CAAgBmB,KAA9B,EAAqC;gBACzB,OADyB;eAE1B,MAF0B;cAG3B,MAH2B;eAI1B,CAJ0B;wBAKjB,sDALiB;kBAMvB;KANd;aAUSwM,IAAT,CAAc3H,WAAd,CAA0B,KAAKyH,iBAA/B;aACSE,IAAT,CAAc3H,WAAd,CAA0B,KAAKhG,UAA/B;QAEM0F,QAAQ,IAAd;QACIzK,IAAJ,CAAS,KAAKwS,iBAAd,EAAiC,OAAjC,EAA0C,YAAW;YAC7CG,IAAN;KADF;;;;2BAKK;UACClI,QAAQ,IAAd;WAEK+H,iBAAL,CAAuBtM,KAAvB,CAA6B0M,OAA7B,GAAuC,OAAvC;WAEK7N,UAAL,CAAgBmB,KAAhB,CAAsB0M,OAAtB,GAAgC,OAAhC;WACK7N,UAAL,CAAgBmB,KAAhB,CAAsB2M,OAAtB,GAAgC,CAAhC;WAEK9N,UAAL,CAAgBmB,KAAhB,CAAsB4M,eAAtB,GAAwC,YAAxC;WAEKC,MAAL;aAEOC,KAAP,CAAa,YAAW;cAChBR,iBAAN,CAAwBtM,KAAxB,CAA8B2M,OAA9B,GAAwC,CAAxC;cACM9N,UAAN,CAAiBmB,KAAjB,CAAuB2M,OAAvB,GAAiC,CAAjC;cACM9N,UAAN,CAAiBmB,KAAjB,CAAuB4M,eAAvB,GAAyC,UAAzC;OAHF;;;;2BAUK;UACCrI,QAAQ,IAAd;UAEMkI,OAAO,SAAPA,IAAO,GAAW;cAChB5N,UAAN,CAAiBmB,KAAjB,CAAuB0M,OAAvB,GAAiC,MAAjC;cACMJ,iBAAN,CAAwBtM,KAAxB,CAA8B0M,OAA9B,GAAwC,MAAxC;YAEI/E,MAAJ,CAAWpD,MAAM1F,UAAjB,EAA6B,qBAA7B,EAAoD4N,IAApD;YACI9E,MAAJ,CAAWpD,MAAM1F,UAAjB,EAA6B,eAA7B,EAA8C4N,IAA9C;YACI9E,MAAJ,CAAWpD,MAAM1F,UAAjB,EAA6B,gBAA7B,EAA+C4N,IAA/C;OANF;UASI3S,IAAJ,CAAS,KAAK+E,UAAd,EAA0B,qBAA1B,EAAiD4N,IAAjD;UACI3S,IAAJ,CAAS,KAAK+E,UAAd,EAA0B,eAA1B,EAA2C4N,IAA3C;UACI3S,IAAJ,CAAS,KAAK+E,UAAd,EAA0B,gBAA1B,EAA4C4N,IAA5C;WAEKH,iBAAL,CAAuBtM,KAAvB,CAA6B2M,OAA7B,GAAuC,CAAvC;WAEK9N,UAAL,CAAgBmB,KAAhB,CAAsB2M,OAAtB,GAAgC,CAAhC;WACK9N,UAAL,CAAgBmB,KAAhB,CAAsB4M,eAAtB,GAAwC,YAAxC;;;;6BAGO;WACF/N,UAAL,CAAgBmB,KAAhB,CAAsBU,IAAtB,GAA6BkB,OAAOmL,UAAP,GAAoB,CAApB,GAAwBlN,IAAImN,QAAJ,CAAa,KAAKnO,UAAlB,IAAgC,CAAxD,GAA4D,IAAzF;WACKA,UAAL,CAAgBmB,KAAhB,CAAsBY,GAAtB,GAA4BgB,OAAOqL,WAAP,GAAqB,CAArB,GAAyBpN,IAAIqN,SAAJ,CAAc,KAAKrO,UAAnB,IAAiC,CAA1D,GAA8D,IAA1F;;;;;;;;ACtEJoM,IAAIkC,MAAJ,CAAWC,UAAX;AAGA,IAAMC,gBAAgB,IAAtB;AAEA,IAAMC,gBAAgB,EAAtB;AAGA,IAAMC,sBAAsB,EAA5B;AAEA,IAAMC,8BAA8B,SAApC;AAEA,IAAMC,yBAA0B,YAAW;MACrC;WACK,CAAC,CAAC7L,OAAO8L,YAAhB;GADF,CAEE,OAAOlO,CAAP,EAAU;WACH,KAAP;;CAJ4B,EAAhC;AAQA,IAAImO,sBAAJ;AAGA,IAAIC,kBAAkB,IAAtB;AAGA,IAAIC,2BAAJ;AAGA,IAAIpB,OAAO,KAAX;AAGA,IAAMqB,eAAe,EAArB;AA2BA,IAAMC,MAAM,SAANA,GAAM,CAAShN,IAAT,EAAe;MACnBwD,QAAQ,IAAd;MAEItD,SAASF,QAAQ,EAArB;OAMKlC,UAAL,GAAkBC,SAASC,aAAT,CAAuB,KAAvB,CAAlB;OACKiP,IAAL,GAAYlP,SAASC,aAAT,CAAuB,IAAvB,CAAZ;OACKF,UAAL,CAAgBgG,WAAhB,CAA4B,KAAKmJ,IAAjC;MAEI3F,QAAJ,CAAa,KAAKxJ,UAAlB,EAA8BwO,aAA9B;OAMKY,SAAL,GAAiB,EAAjB;OAEKC,aAAL,GAAqB,EAArB;OAMKC,mBAAL,GAA2B,EAA3B;OAoBKC,sCAAL,GAA8C,EAA9C;OAEKC,WAAL,GAAmB,EAAnB;WAGS/S,OAAO2G,QAAP,CAAgBhB,MAAhB,EAAwB;gBACnB,KADmB;eAEpB,IAFoB;WAGxB8M,IAAIO;GAHJ,CAAT;WAMShT,OAAO2G,QAAP,CAAgBhB,MAAhB,EAAwB;eACpBA,OAAOsN,SADa;cAErBtN,OAAOsN;GAFV,CAAT;MAKI,CAACjT,OAAOzB,WAAP,CAAmBoH,OAAOuN,IAA1B,CAAL,EAAsC;QAEhCvN,OAAOwN,MAAX,EAAmB;aACVD,IAAP,CAAYC,MAAZ,GAAqBxN,OAAOwN,MAA5B;;GAHJ,MAKO;WACED,IAAP,GAAc,EAAEC,QAAQjB,2BAAV,EAAd;;MAGElS,OAAOzB,WAAP,CAAmBoH,OAAOyN,MAA1B,KAAqCzN,OAAO0N,QAAhD,EAA0D;iBAC3CvL,IAAb,CAAkB,IAAlB;;SAIKwL,SAAP,GAAmBtT,OAAOzB,WAAP,CAAmBoH,OAAOyN,MAA1B,KAAqCzN,OAAO2N,SAA/D;MAEI3N,OAAOsN,SAAP,IAAoBjT,OAAOzB,WAAP,CAAmBoH,OAAO4N,UAA1B,CAAxB,EAA+D;WACtDA,UAAP,GAAoB,IAApB;;MAMEC,kBACFrB,0BACAC,aAAaqB,OAAb,CAAqBC,oBAAoB,IAApB,EAA0B,SAA1B,CAArB,MAA+D,MAFjE;MAIIC,2BAAJ;MACIC,iBAAJ;SAEOC,gBAAP,CAAwB,IAAxB;;YAOY;WACD,kBAAW;eACPlO,OAAOyN,MAAd;;KAPN;gBAWc;WACL,kBAAW;eACPzN,OAAO4N,UAAd;;KAbN;eAqBa;WACJ,kBAAW;eACP5N,OAAOsN,SAAd;;KAvBN;gBA+Bc;WACL,kBAAW;eACPtN,OAAOmO,UAAd;;KAjCN;YAyCU;WACD,kBAAW;YACV7K,MAAMmK,MAAV,EAAkB;iBACTnK,MAAM8K,OAAN,GAAgBZ,MAAvB;;eAGKxN,OAAOuN,IAAP,CAAYC,MAAnB;OANI;WASD,gBAAShW,CAAT,EAAY;YACX8L,MAAMmK,MAAV,EAAkB;gBACVW,OAAN,GAAgBZ,MAAhB,GAAyBhW,CAAzB;SADF,MAEO;iBACE+V,IAAP,CAAYC,MAAZ,GAAqBhW,CAArB;;6BAEmB,IAArB;cACM6W,MAAN;;KAzDN;WAiES;WACA,kBAAW;eACPrO,OAAO2C,KAAd;OAFG;WAIA,gBAASnL,CAAT,EAAY;eACRmL,KAAP,GAAenL,CAAf;iBACS8L,KAAT,EAAgB9L,CAAhB;;KAvEN;UAgFQ;WACC,kBAAW;eACPwI,OAAOsO,IAAd;OAFE;WAIC,gBAAS9W,CAAT,EAAY;eAER8W,IAAP,GAAc9W,CAAd;YACIyW,QAAJ,EAAc;mBACH5J,SAAT,GAAqBrE,OAAOsO,IAA5B;;;KAxFR;YAiGU;WACD,kBAAW;eACPtO,OAAOuO,MAAd;OAFI;WAID,gBAAS/W,CAAT,EAAY;eACR+W,MAAP,GAAgB/W,CAAhB;YACIwI,OAAOuO,MAAX,EAAmB;cACbnH,QAAJ,CAAa9D,MAAMyJ,IAAnB,EAAyBD,IAAI0B,YAA7B;SADF,MAEO;cACD7F,WAAJ,CAAgBrF,MAAMyJ,IAAtB,EAA4BD,IAAI0B,YAAhC;;aAKGC,QAAL;YAEInL,MAAMoL,aAAV,EAAyB;gBACjBA,aAAN,CAAoBrK,SAApB,GAAgC7M,IAAIsV,IAAI6B,SAAR,GAAoB7B,IAAI8B,WAAxD;;;KAlHR;UA2HQ;WACC,kBAAW;eACP5O,OAAOuN,IAAd;;KA7HN;qBAsImB;WAEV,kBAAW;eACPM,eAAP;OAHa;WAKV,gBAASlM,IAAT,EAAe;YACd6K,sBAAJ,EAA4B;4BACR7K,IAAlB;cACIA,IAAJ,EAAU;gBACJ9I,IAAJ,CAAS8H,MAAT,EAAiB,QAAjB,EAA2BqN,kBAA3B;WADF,MAEO;gBACDtH,MAAJ,CAAW/F,MAAX,EAAmB,QAAnB,EAA6BqN,kBAA7B;;uBAEWa,OAAb,CAAqBd,oBAAoBzK,KAApB,EAA2B,SAA3B,CAArB,EAA4D3B,IAA5D;;;;GArJV;MA4JItH,OAAOzB,WAAP,CAAmBoH,OAAOyN,MAA1B,CAAJ,EAAuC;SAChCc,MAAL,GAAcvO,OAAOuO,MAAP,IAAiB,KAA/B;QAEInH,QAAJ,CAAa,KAAKxJ,UAAlB,EAA8BkP,IAAIgC,UAAlC;QACI3G,cAAJ,CAAmB,KAAKvK,UAAxB,EAAoC,KAApC;QAGI4O,sBAAJ,EAA4B;UACtBqB,eAAJ,EAAqB;cACbA,eAAN,GAAwB,IAAxB;YAEMkB,WAAWtC,aAAaqB,OAAb,CAAqBC,oBAAoB,IAApB,EAA0B,KAA1B,CAArB,CAAjB;YAEIgB,QAAJ,EAAc;iBACLxB,IAAP,GAAcyB,KAAKC,KAAL,CAAWF,QAAX,CAAd;;;;SAKDL,aAAL,GAAqB7Q,SAASC,aAAT,CAAuB,KAAvB,CAArB;SACK4Q,aAAL,CAAmBrK,SAAnB,GAA+ByI,IAAI8B,WAAnC;QACIxH,QAAJ,CAAa,KAAKsH,aAAlB,EAAiC5B,IAAIoC,kBAArC;QACIlP,OAAOmO,UAAX,EAAuB;UACjB/G,QAAJ,CAAa,KAAKsH,aAAlB,EAAiC5B,IAAIqC,eAArC;WACKvR,UAAL,CAAgBwR,YAAhB,CAA6B,KAAKV,aAAlC,EAAiD,KAAK9Q,UAAL,CAAgByR,UAAhB,CAA2B,CAA3B,CAAjD;KAFF,MAGO;UACDjI,QAAJ,CAAa,KAAKsH,aAAlB,EAAiC5B,IAAIwC,kBAArC;WACK1R,UAAL,CAAgBgG,WAAhB,CAA4B,KAAK8K,aAAjC;;QAGE7V,IAAJ,CAAS,KAAK6V,aAAd,EAA6B,OAA7B,EAAsC,YAAW;YACzCH,MAAN,GAAe,CAACjL,MAAMiL,MAAtB;KADF;GA9BF,MAkCO;QACDvO,OAAOuO,MAAP,KAAkBxU,SAAtB,EAAiC;aACxBwU,MAAP,GAAgB,IAAhB;;QAGIgB,eAAe1R,SAAS2R,cAAT,CAAwBxP,OAAOsO,IAA/B,CAArB;QACIlH,QAAJ,CAAamI,YAAb,EAA2B,iBAA3B;eAEWE,OAAOnM,KAAP,EAAciM,YAAd,CAAX;QAEMG,eAAe,SAAfA,YAAe,CAASnR,CAAT,EAAY;QAC7B8I,cAAF;YACMkH,MAAN,GAAe,CAACjL,MAAMiL,MAAtB;aACO,KAAP;KAHF;QAMInH,QAAJ,CAAa,KAAK2F,IAAlB,EAAwBD,IAAI0B,YAA5B;QAEIpH,QAAJ,CAAa6G,QAAb,EAAuB,OAAvB;QACIpV,IAAJ,CAASoV,QAAT,EAAmB,OAAnB,EAA4ByB,YAA5B;QAEI,CAAC1P,OAAOuO,MAAZ,EAAoB;WACbA,MAAL,GAAc,KAAd;;;MAIAvO,OAAOsN,SAAX,EAAsB;QAChBjT,OAAOzB,WAAP,CAAmBoH,OAAOyN,MAA1B,CAAJ,EAAuC;UACjCd,eAAJ,EAAqB;6BACE9O,SAASC,aAAT,CAAuB,KAAvB,CAArB;YACIsJ,QAAJ,CAAawF,kBAAb,EAAiCR,aAAjC;YACIhF,QAAJ,CAAawF,kBAAb,EAAiCE,IAAI6C,0BAArC;iBACSpE,IAAT,CAAc3H,WAAd,CAA0BgJ,kBAA1B;0BACkB,KAAlB;;yBAIiBhJ,WAAnB,CAA+B,KAAKhG,UAApC;UAGIwJ,QAAJ,CAAa,KAAKxJ,UAAlB,EAA8BkP,IAAI8C,gBAAlC;;QAKE,CAAC,KAAKnC,MAAV,EAAkB;eACPnK,KAAT,EAAgBtD,OAAO2C,KAAvB;;;OAICkN,eAAL,GAAuB,YAAW;UAC1BC,iBAAN;GADF;MAIIjX,IAAJ,CAAS8H,MAAT,EAAiB,QAAjB,EAA2B,KAAKkP,eAAhC;MACIhX,IAAJ,CAAS,KAAKkU,IAAd,EAAoB,qBAApB,EAA2C,KAAK8C,eAAhD;MACIhX,IAAJ,CAAS,KAAKkU,IAAd,EAAoB,eAApB,EAAqC,KAAK8C,eAA1C;MACIhX,IAAJ,CAAS,KAAKkU,IAAd,EAAoB,gBAApB,EAAsC,KAAK8C,eAA3C;OACKpB,QAAL;MAEIzO,OAAO2N,SAAX,EAAsB;oBACJ,IAAhB;;uBAGmB,8BAAW;QAC1BnB,0BAA0BC,aAAaqB,OAAb,CAAqBC,oBAAoBzK,KAApB,EAA2B,SAA3B,CAArB,MAAgE,MAA9F,EAAsG;mBACvFuL,OAAb,CAAqBd,oBAAoBzK,KAApB,EAA2B,KAA3B,CAArB,EAAwD0L,KAAKe,SAAL,CAAezM,MAAM0M,aAAN,EAAf,CAAxD;;GAFJ;OAOKC,4BAAL,GAAoCjC,kBAApC;WAESkC,UAAT,GAAsB;QACdC,OAAO7M,MAAM8K,OAAN,EAAb;SACKzL,KAAL,IAAc,CAAd;WACOkJ,KAAP,CAAa,YAAW;WACjBlJ,KAAL,IAAc,CAAd;KADF;;MAKE,CAAC3C,OAAOyN,MAAZ,EAAoB;;;CA5WtB;AAiXAX,IAAIsD,UAAJ,GAAiB,YAAW;SACnB,CAAC5E,IAAR;SACOpT,IAAP,CAAYyU,YAAZ,EAA0B,UAASwD,GAAT,EAAc;QAClCzS,UAAJ,CAAemB,KAAf,CAAqB0M,OAArB,GAA+BD,OAAO,MAAP,GAAgB,EAA/C;GADF;CAFF;AAOAsB,IAAI8C,gBAAJ,GAAuB,GAAvB;AACA9C,IAAI6C,0BAAJ,GAAiC,IAAjC;AACA7C,IAAIgC,UAAJ,GAAiB,MAAjB;AACAhC,IAAIwD,oBAAJ,GAA2B,IAA3B;AACAxD,IAAIyD,cAAJ,GAAqB,oBAArB;AACAzD,IAAI0B,YAAJ,GAAmB,QAAnB;AACA1B,IAAIoC,kBAAJ,GAAyB,cAAzB;AACApC,IAAIqC,eAAJ,GAAsB,WAAtB;AACArC,IAAIwC,kBAAJ,GAAyB,cAAzB;AACAxC,IAAI0D,UAAJ,GAAiB,MAAjB;AAEA1D,IAAIO,aAAJ,GAAoB,GAApB;AACAP,IAAI8B,WAAJ,GAAkB,gBAAlB;AACA9B,IAAI6B,SAAJ,GAAgB,eAAhB;AAEA7B,IAAI2D,eAAJ,GAAsB,UAASlS,CAAT,EAAY;MAC5BV,SAASqF,aAAT,CAAuBC,IAAvB,KAAgC,MAAhC,KACD5E,EAAEmS,KAAF,KAAYrE,aAAZ,IAA6B9N,EAAE8C,OAAF,KAAcgL,aAD1C,CAAJ,EAC8D;QACxD+D,UAAJ;;CAHJ;AAMAxR,IAAI/F,IAAJ,CAAS8H,MAAT,EAAiB,SAAjB,EAA4BmM,IAAI2D,eAAhC,EAAiD,KAAjD;AAEApW,OAAO8C,MAAP,CACE2P,IAAIhV,SADN;AAIE;OAyBO,aAAS2F,MAAT,EAAiBC,QAAjB,EAA2B;WACvBiT,KACL,IADK,EAELlT,MAFK,EAGLC,QAHK,EAIL;mBACe7F,MAAMC,SAAN,CAAgBG,KAAhB,CAAsBI,IAAtB,CAA2BC,SAA3B,EAAsC,CAAtC;KALV,CAAP;GA1BJ;YAwDY,kBAASmF,MAAT,EAAiBC,QAAjB,EAA2B;WAC5BiT,KACL,IADK,EAELlT,MAFK,EAGLC,QAHK,EAIL;aACS;KALJ,CAAP;GAzDJ;UAwEU,gBAASkT,UAAT,EAAqB;SAEtB7D,IAAL,CAAU8D,WAAV,CAAsBD,WAAWE,IAAjC;SACK7D,aAAL,CAAmBzK,MAAnB,CAA0B,KAAKyK,aAAL,CAAmB/K,OAAnB,CAA2B0O,UAA3B,CAA1B,EAAkE,CAAlE;QACMtN,QAAQ,IAAd;WACOuI,KAAP,CAAa,YAAW;YAChB4C,QAAN;KADF;GA7EJ;WAuFW,mBAAW;QACd,KAAKhB,MAAT,EAAiB;YACT,IAAInR,KAAJ,CACJ,0DACA,uDAFI,CAAN;;QAME,KAAKgR,SAAT,EAAoB;yBACCuD,WAAnB,CAA+B,KAAKjT,UAApC;;QAGI0F,QAAQ,IAAd;WACOlL,IAAP,CAAY,KAAK4U,SAAjB,EAA4B,UAAS+D,SAAT,EAAoB;YACxCC,YAAN,CAAmBD,SAAnB;KADF;QAIIrK,MAAJ,CAAW/F,MAAX,EAAmB,SAAnB,EAA8BmM,IAAI2D,eAAlC,EAAmD,KAAnD;oBAEgB,IAAhB;GA1GJ;aAqHa,mBAASnC,IAAT,EAAe;QAGpB,KAAKtB,SAAL,CAAesB,IAAf,MAAyBvU,SAA7B,EAAwC;YAChC,IAAIuC,KAAJ,CAAU,iDACd,SADc,GACFgS,IADE,GACK,GADf,CAAN;;QAII2C,eAAe,EAAE3C,MAAMA,IAAR,EAAcb,QAAQ,IAAtB,EAArB;iBAKaH,SAAb,GAAyB,KAAKA,SAA9B;QAGI,KAAKC,IAAL;SACGA,IAAL,CAAU2D,OADR;SAEG3D,IAAL,CAAU2D,OAAV,CAAkB5C,IAAlB,CAFF,EAE2B;mBAEZC,MAAb,GAAsB,KAAKhB,IAAL,CAAU2D,OAAV,CAAkB5C,IAAlB,EAAwBC,MAA9C;mBAGahB,IAAb,GAAoB,KAAKA,IAAL,CAAU2D,OAAV,CAAkB5C,IAAlB,CAApB;;QAGI+B,MAAM,IAAIvD,GAAJ,CAAQmE,YAAR,CAAZ;SACKjE,SAAL,CAAesB,IAAf,IAAuB+B,GAAvB;QAEMc,KAAK1B,OAAO,IAAP,EAAaY,IAAIzS,UAAjB,CAAX;QACIwJ,QAAJ,CAAa+J,EAAb,EAAiB,QAAjB;WACOd,GAAP;GApJJ;gBA4JgB,sBAASe,MAAT,EAAiB;SACxBrE,IAAL,CAAU8D,WAAV,CAAsBO,OAAOxT,UAAP,CAAkByT,aAAxC;WAEO,KAAKrE,SAAL,CAAeoE,OAAO9C,IAAtB,CAAP;QAGI,KAAKf,IAAL;SACGA,IAAL,CAAU2D,OADR;SAEG3D,IAAL,CAAU2D,OAAV,CAAkBE,OAAO9C,IAAzB,CAFF,EAEkC;aACzB,KAAKf,IAAL,CAAU2D,OAAV,CAAkBE,OAAO9C,IAAzB,CAAP;;oBAGc8C,MAAhB;QAEM9N,QAAQ,IAAd;WAEOlL,IAAP,CAAYgZ,OAAOpE,SAAnB,EAA8B,UAAS+D,SAAT,EAAoB;aACzCC,YAAP,CAAoBD,SAApB;KADF;WAIOlF,KAAP,CAAa,YAAW;YAChB4C,QAAN;KADF;GAhLJ;QAwLQ,gBAAW;SACVF,MAAL,GAAc,KAAd;GAzLJ;SA+LS,iBAAW;SACXA,MAAL,GAAc,IAAd;GAhMJ;QAsMQ,gBAAW;SACV3Q,UAAL,CAAgBmB,KAAhB,CAAsB0M,OAAtB,GAAgC,MAAhC;GAvMJ;QA6MQ,gBAAW;SACV7N,UAAL,CAAgBmB,KAAhB,CAAsB0M,OAAtB,GAAgC,EAAhC;GA9MJ;YAkNY,oBAAW;QAEb0E,OAAO,KAAK/B,OAAL,EAAb;QACI+B,KAAKvC,UAAT,EAAqB;UACbjO,MAAMf,IAAI0S,SAAJ,CAAcnB,KAAKpD,IAAnB,EAAyBpN,GAArC;UACItI,IAAI,CAAR;aAEOe,IAAP,CAAY+X,KAAKpD,IAAL,CAAUsC,UAAtB,EAAkC,UAASkC,IAAT,EAAe;YAC3C,EAAEpB,KAAK7C,SAAL,IAAkBiE,SAASpB,KAAKqB,UAAlC,CAAJ,EAAmD;eAC5C5S,IAAIqN,SAAJ,CAAcsF,IAAd,CAAL;;OAFJ;UAMI5Q,OAAOqL,WAAP,GAAqBrM,GAArB,GAA2B2M,mBAA3B,GAAiDjV,CAArD,EAAwD;YAClD+P,QAAJ,CAAa+I,KAAKvS,UAAlB,EAA8BkP,IAAIyD,cAAlC;aACKxD,IAAL,CAAUhO,KAAV,CAAgB6D,MAAhB,GAAyBjC,OAAOqL,WAAP,GAAqBrM,GAArB,GAA2B2M,mBAA3B,GAAiD,IAA1E;OAFF,MAGO;YACD3D,WAAJ,CAAgBwH,KAAKvS,UAArB,EAAiCkP,IAAIyD,cAArC;aACKxD,IAAL,CAAUhO,KAAV,CAAgB6D,MAAhB,GAAyB,MAAzB;;;QAIAuN,KAAKsB,eAAT,EAA0B;aACjB5F,KAAP,CAAa,YAAW;aACjB4F,eAAL,CAAqB1S,KAArB,CAA2B6D,MAA3B,GAAoCuN,KAAKpD,IAAL,CAAU2E,YAAV,GAAyB,IAA7D;OADF;;QAKEvB,KAAKzB,aAAT,EAAwB;WACjBA,aAAL,CAAmB3P,KAAnB,CAAyB4D,KAAzB,GAAiCwN,KAAKxN,KAAL,GAAa,IAA9C;;GA/ON;qBAmPqBtI,OAAOsX,QAAP,CAAgB,YAAW;SAAOlD,QAAL;GAA7B,EAAiD,EAAjD,CAnPrB;YA+PY,oBAAW;QACfpU,OAAOzB,WAAP,CAAmB8T,aAAnB,CAAJ,EAAuC;sBACrB,IAAItB,WAAJ,EAAhB;oBACcxN,UAAd,CAAyByG,SAAzB,GAAqCuN,kBAArC;;QAGE,KAAKnE,MAAT,EAAiB;YACT,IAAInR,KAAJ,CAAU,gDAAV,CAAN;;QAGIgH,QAAQ,IAAd;WAEOlL,IAAP,CAAYP,MAAMC,SAAN,CAAgBG,KAAhB,CAAsBI,IAAtB,CAA2BC,SAA3B,CAAZ,EAAmD,UAASmF,MAAT,EAAiB;UAC9D6F,MAAM4J,mBAAN,CAA0BvV,MAA1B,KAAqC,CAAzC,EAA4C;oBAC9B2L,KAAZ;;UAEEA,MAAM4J,mBAAN,CAA0BhL,OAA1B,CAAkCzE,MAAlC,MAA8C,CAAC,CAAnD,EAAsD;cAC9CyP,mBAAN,CAA0B/K,IAA1B,CAA+B1E,MAA/B;;KALJ;QASI,KAAK6P,SAAT,EAAoB;eAET,IAAT,EAAe,KAAK3K,KAApB;;GAtRN;WA8RW,mBAAW;QACd0N,MAAM,IAAV;WACOA,IAAI5C,MAAX,EAAmB;YACX4C,IAAI5C,MAAV;;WAEK4C,GAAP;GAnSJ;iBA2SiB,yBAAW;QAClBvV,WAAW,KAAKyS,IAAtB;aACSgB,MAAT,GAAkB,KAAKA,MAAvB;QAGI,KAAKrB,mBAAL,CAAyBvV,MAAzB,GAAkC,CAAtC,EAAyC;eAC9B6V,MAAT,GAAkB,KAAKA,MAAvB;UAEI,CAAC1S,SAAS+W,UAAd,EAA0B;iBACfA,UAAT,GAAsB,EAAtB;;eAGOA,UAAT,CAAoB,KAAKrE,MAAzB,IAAmCsE,iBAAiB,IAAjB,CAAnC;;aAGOZ,OAAT,GAAmB,EAAnB;WACO9Y,IAAP,CAAY,KAAK4U,SAAjB,EAA4B,UAAS7I,OAAT,EAAkBxL,GAAlB,EAAuB;eACxCuY,OAAT,CAAiBvY,GAAjB,IAAwBwL,QAAQ6L,aAAR,EAAxB;KADF;WAIOlV,QAAP;GA/TJ;QAkUQ,gBAAW;QACX,CAAC,KAAKyS,IAAL,CAAUsE,UAAf,EAA2B;WACpBtE,IAAL,CAAUsE,UAAV,GAAuB,EAAvB;;SAGGtE,IAAL,CAAUsE,UAAV,CAAqB,KAAKrE,MAA1B,IAAoCsE,iBAAiB,IAAjB,CAApC;uBACmB,IAAnB,EAAyB,KAAzB;SACK7B,4BAAL;GAzUJ;UA4UU,gBAAS8B,UAAT,EAAqB;QACvB,CAAC,KAAKxE,IAAL,CAAUsE,UAAf,EAA2B;WAEpBtE,IAAL,CAAUsE,UAAV,GAAuB,EAAvB;WACKtE,IAAL,CAAUsE,UAAV,CAAqBtF,2BAArB,IAAoDuF,iBAAiB,IAAjB,EAAuB,IAAvB,CAApD;;SAGGvE,IAAL,CAAUsE,UAAV,CAAqBE,UAArB,IAAmCD,iBAAiB,IAAjB,CAAnC;SACKtE,MAAL,GAAcuE,UAAd;oBACgB,IAAhB,EAAsBA,UAAtB,EAAkC,IAAlC;SACK9B,4BAAL;GAtVJ;UAyVU,gBAASI,GAAT,EAAc;WACbjY,IAAP,CAAY,KAAK6U,aAAjB,EAAgC,UAAS2D,UAAT,EAAqB;UAE/C,CAAC,KAAKxC,OAAL,GAAeb,IAAf,CAAoBsE,UAAzB,EAAqC;mBACxBlO,QAAX,CAAoBiN,WAAWjT,YAA/B;OADF,MAEO;yBACY0S,OAAO,KAAKjC,OAAL,EAAxB,EAAwCwC,UAAxC;;UAIEA,WAAW5S,gBAAf,EAAiC;mBACpBA,gBAAX,CAA4B3F,IAA5B,CAAiCuY,UAAjC,EAA6CA,WAAWzS,QAAX,EAA7C;;KAVJ,EAYG,IAZH;WAcO/F,IAAP,CAAY,KAAK4U,SAAjB,EAA4B,UAASoE,MAAT,EAAiB;aACpC/C,MAAP,CAAc+C,MAAd;KADF;QAII,CAACf,GAAL,EAAU;yBACW,KAAKjC,OAAL,EAAnB,EAAmC,KAAnC;;GA7WN;UAiXU,gBAASwC,UAAT,EAAqB;QACrB/P,OAAO,KAAKuM,WAAL,CAAiBzV,MAAjB,KAA4B,CAAzC;SACKyV,WAAL,CAAiBjL,IAAjB,CAAsByO,UAAtB;QACI/P,IAAJ,EAAU;qBACO,KAAKuM,WAApB;;GArXN;iBAyXiB,yBAAW;WACjBhV,IAAP,CAAY,KAAK6U,aAAjB,EAAgC,UAAS2D,UAAT,EAAqB;iBACxC1S,aAAX;KADF;WAGO9F,IAAP,CAAY,KAAK4U,SAAjB,EAA4B,UAASoE,MAAT,EAAiB;aACpClT,aAAP;KADF;;CAjYN;AAiZA,SAASuR,MAAT,CAAgBY,GAAhB,EAAqB2B,MAArB,EAA6BC,QAA7B,EAAuC;MAC/Bd,KAAKtT,SAASC,aAAT,CAAuB,IAAvB,CAAX;MACIkU,MAAJ,EAAY;OACPpO,WAAH,CAAeoO,MAAf;;MAGEC,QAAJ,EAAc;QACRlF,IAAJ,CAASqC,YAAT,CAAsB+B,EAAtB,EAA0Bc,QAA1B;GADF,MAEO;QACDlF,IAAJ,CAASnJ,WAAT,CAAqBuN,EAArB;;MAEE1C,QAAJ;SACO0C,EAAP;;AAGF,SAASe,eAAT,CAAyB7B,GAAzB,EAA8B;MACxB3J,MAAJ,CAAW/F,MAAX,EAAmB,QAAnB,EAA6B0P,IAAIR,eAAjC;MAEIQ,IAAIJ,4BAAR,EAAsC;QAChCvJ,MAAJ,CAAW/F,MAAX,EAAmB,QAAnB,EAA6B0P,IAAIJ,4BAAjC;;;AAIJ,SAASkC,kBAAT,CAA4B9B,GAA5B,EAAiC+B,QAAjC,EAA2C;MACnChO,MAAMiM,IAAIgC,eAAJ,CAAoBhC,IAAIgC,eAAJ,CAAoB9N,aAAxC,CAAZ;MAEI6N,QAAJ,EAAc;QACR/N,SAAJ,GAAgBD,IAAIhI,KAAJ,GAAY,GAA5B;GADF,MAEO;QACDiI,SAAJ,GAAgBD,IAAIhI,KAApB;;;AAIJ,SAASkW,iBAAT,CAA2BjC,GAA3B,EAAgCc,EAAhC,EAAoCP,UAApC,EAAgD;aACnCE,IAAX,GAAkBK,EAAlB;aACWoB,KAAX,GAAmBlC,GAAnB;SAEOlT,MAAP,CAAcyT,UAAd,EAA6D;aAKlD,iBAAS5M,QAAT,EAAkB;UACrB1L,UAAUX,MAAV,GAAmB,CAAvB,EAA0B;YAClB6a,cAAc5B,WAAWE,IAAX,CAAgB2B,kBAApC;mBACWC,MAAX;eAEO/B,KACLN,GADK,EAELO,WAAWnT,MAFN,EAGLmT,WAAWlT,QAHN,EAIL;kBACU8U,WADV;uBAEe,CAACnY,OAAOP,OAAP,CAAexB,SAAf,CAAD;SANV,CAAP;;UAWE+B,OAAOJ,OAAP,CAAe+J,QAAf,KAA2B3J,OAAO5B,QAAP,CAAgBuL,QAAhB,CAA/B,EAAyD;YACjDwO,eAAc5B,WAAWE,IAAX,CAAgB2B,kBAApC;mBACWC,MAAX;eAEO/B,KACLN,GADK,EAELO,WAAWnT,MAFN,EAGLmT,WAAWlT,QAHN,EAIL;kBACU8U,YADV;uBAEe,CAACxO,QAAD;SANV,CAAP;;KAzBuD;UA0CrD,cAASsK,KAAT,EAAe;iBACRwC,IAAX,CAAgB6B,iBAAhB,CAAkCA,iBAAlC,CAAoDtO,SAApD,GAAgEiK,KAAhE;aACOsC,UAAP;KA5CyD;YAmDnD,kBAAW;iBACN2B,KAAX,CAAiBK,MAAjB,CAAwBhC,UAAxB;aACOA,UAAP;KArDyD;YA4DnD,kBAAW;iBACN2B,KAAX,CAAiBG,MAAjB,CAAwB9B,UAAxB;aACOA,UAAP;;GA9DJ;MAmEIA,sBAAsB5J,sBAA1B,EAAkD;QAC1C6L,MAAM,IAAI3M,mBAAJ,CAAwB0K,WAAWnT,MAAnC,EAA2CmT,WAAWlT,QAAtD,EACV,EAAE5B,KAAK8U,WAAW3L,KAAlB,EAAyBlJ,KAAK6U,WAAW1L,KAAzC,EAAgDE,MAAMwL,WAAWzL,MAAjE,EADU,CAAZ;WAGO/M,IAAP,CAAY,CAAC,eAAD,EAAkB,UAAlB,EAA8B,gBAA9B,EAAgD,MAAhD,EAAwD,KAAxD,EAA+D,KAA/D,CAAZ,EAAmF,UAAS0a,MAAT,EAAiB;UAC5FC,KAAKnC,WAAWkC,MAAX,CAAX;UACME,KAAKH,IAAIC,MAAJ,CAAX;iBACWA,MAAX,IAAqBD,IAAIC,MAAJ,IAAc,YAAW;YACtC/Z,OAAOlB,MAAMC,SAAN,CAAgBG,KAAhB,CAAsBI,IAAtB,CAA2BC,SAA3B,CAAb;WACGW,KAAH,CAAS4Z,GAAT,EAAc9Z,IAAd;eACOga,GAAG9Z,KAAH,CAAS2X,UAAT,EAAqB7X,IAArB,CAAP;OAHF;KAHF;QAUIqO,QAAJ,CAAa+J,EAAb,EAAiB,YAAjB;eACWvT,UAAX,CAAsBwR,YAAtB,CAAmCyD,IAAIjV,UAAvC,EAAmDgT,WAAWhT,UAAX,CAAsB+U,iBAAzE;GAfF,MAgBO,IAAI/B,sBAAsB1K,mBAA1B,EAA+C;QAC9CnP,IAAI,SAAJA,CAAI,CAASkc,QAAT,EAAmB;UAEvB5Y,OAAOO,QAAP,CAAgBgW,WAAW3L,KAA3B,KAAqC5K,OAAOO,QAAP,CAAgBgW,WAAW1L,KAA3B,CAAzC,EAA4E;YAIpEgO,UAAUtC,WAAWE,IAAX,CAAgB6B,iBAAhB,CAAkCA,iBAAlC,CAAoDtO,SAApE;YACM8O,eAAevC,WAAW2B,KAAX,CAAiBnF,WAAjB,CAA6BlL,OAA7B,CAAqC0O,UAArC,IAAmD,CAAC,CAAzE;mBAEW8B,MAAX;YACMU,gBAAgBzC,KACpBN,GADoB,EAEpBO,WAAWnT,MAFS,EAGpBmT,WAAWlT,QAHS,EAIpB;kBACUkT,WAAWE,IAAX,CAAgB2B,kBAD1B;uBAEe,CAAC7B,WAAW3L,KAAZ,EAAmB2L,WAAW1L,KAA9B,EAAqC0L,WAAWzL,MAAhD;SANK,CAAtB;sBAUcmJ,IAAd,CAAmB4E,OAAnB;YACIC,YAAJ,EAAkBC,cAAcR,MAAd;eAEXQ,aAAP;;aAGKH,QAAP;KA1BF;eA6BWnX,GAAX,GAAiBzB,OAAOgZ,OAAP,CAAetc,CAAf,EAAkB6Z,WAAW9U,GAA7B,CAAjB;eACWC,GAAX,GAAiB1B,OAAOgZ,OAAP,CAAetc,CAAf,EAAkB6Z,WAAW7U,GAA7B,CAAjB;GA/BK,MAgCA,IAAI6U,sBAAsBvN,iBAA1B,EAA6C;QAC9CxK,IAAJ,CAASsY,EAAT,EAAa,OAAb,EAAsB,YAAW;UAC3BmC,SAAJ,CAAc1C,WAAWpN,UAAzB,EAAqC,OAArC;KADF;QAII3K,IAAJ,CAAS+X,WAAWpN,UAApB,EAAgC,OAAhC,EAAyC,UAASjF,CAAT,EAAY;QACjDgV,eAAF,GADmD;KAArD;GALK,MAQA,IAAI3C,sBAAsBhJ,kBAA1B,EAA8C;QAC/C/O,IAAJ,CAASsY,EAAT,EAAa,OAAb,EAAsB,YAAW;UAC3BmC,SAAJ,CAAc1C,WAAW9I,QAAzB,EAAmC,OAAnC;KADF;QAIIjP,IAAJ,CAASsY,EAAT,EAAa,WAAb,EAA0B,YAAW;UAC/B/J,QAAJ,CAAawJ,WAAW9I,QAAxB,EAAkC,OAAlC;KADF;QAIIjP,IAAJ,CAASsY,EAAT,EAAa,UAAb,EAAyB,YAAW;UAC9BxI,WAAJ,CAAgBiI,WAAW9I,QAA3B,EAAqC,OAArC;KADF;GATK,MAYA,IAAI8I,sBAAsB5I,eAA1B,EAA2C;QAC5CZ,QAAJ,CAAa+J,EAAb,EAAiB,OAAjB;eACWjT,aAAX,GAA2B7D,OAAOgZ,OAAP,CAAe,UAAS3U,GAAT,EAAc;SACnDK,KAAH,CAASyU,eAAT,GAA2B5C,WAAW3I,OAAX,CAAmBnR,QAAnB,EAA3B;aACO4H,GAAP;KAFyB,EAGxBkS,WAAW1S,aAHa,CAA3B;eAKWA,aAAX;;aAGSyF,QAAX,GAAsBtJ,OAAOgZ,OAAP,CAAe,UAAS3U,GAAT,EAAc;QAC7C2R,IAAIjC,OAAJ,GAAciE,eAAd,IAAiCzB,WAAW6C,UAAX,EAArC,EAA8D;yBACzCpD,IAAIjC,OAAJ,EAAnB,EAAkC,IAAlC;;WAGK1P,GAAP;GALoB,EAMnBkS,WAAWjN,QANQ,CAAtB;;AASF,SAAS+P,gBAAT,CAA0BrD,GAA1B,EAA+BO,UAA/B,EAA2C;MAEnCT,OAAOE,IAAIjC,OAAJ,EAAb;MAIMuF,eAAexD,KAAKjD,mBAAL,CAAyBhL,OAAzB,CAAiC0O,WAAWnT,MAA5C,CAArB;MAGIkW,iBAAiB,CAAC,CAAtB,EAAyB;QAEnBC,gBAAgBzD,KAAKhD,sCAAL,CAA4CwG,YAA5C,CAApB;QAIIC,kBAAkB7Z,SAAtB,EAAiC;sBACf,EAAhB;WACKoT,sCAAL,CAA4CwG,YAA5C,IACEC,aADF;;kBAKYhD,WAAWlT,QAAzB,IAAqCkT,UAArC;QAGIT,KAAK5C,IAAL,IAAa4C,KAAK5C,IAAL,CAAUsE,UAA3B,EAAuC;UAC/BgC,YAAY1D,KAAK5C,IAAL,CAAUsE,UAA5B;UAGIrE,eAAJ;UAEIqG,UAAUxD,IAAI7C,MAAd,CAAJ,EAA2B;iBAChBqG,UAAUxD,IAAI7C,MAAd,CAAT;OADF,MAEO,IAAIqG,UAAUtH,2BAAV,CAAJ,EAA4C;iBAExCsH,UAAUtH,2BAAV,CAAT;OAFK,MAGA;;;UAMHiB,OAAOmG,YAAP,KAAwBnG,OAAOmG,YAAP,EAAqB/C,WAAWlT,QAAhC,MAA8C3D,SAA1E,EAAqF;YAE7EqC,QAAQoR,OAAOmG,YAAP,EAAqB/C,WAAWlT,QAAhC,CAAd;mBAGWC,YAAX,GAA0BvB,KAA1B;mBACWuH,QAAX,CAAoBvH,KAApB;;;;;AAMR,SAASuU,IAAT,CAAaN,GAAb,EAAkB5S,MAAlB,EAA0BC,QAA1B,EAAoCsC,MAApC,EAA4C;MACtCvC,OAAOC,QAAP,MAAqB3D,SAAzB,EAAoC;UAC5B,IAAIuC,KAAJ,cAAqBmB,MAArB,2BAAiDC,QAAjD,OAAN;;MAGEkT,mBAAJ;MAEI5Q,OAAOvJ,KAAX,EAAkB;iBACH,IAAIuR,eAAJ,CAAoBvK,MAApB,EAA4BC,QAA5B,CAAb;GADF,MAEO;QACCoW,cAAc,CAACrW,MAAD,EAASC,QAAT,EAAmBqW,MAAnB,CAA0B/T,OAAO8T,WAAjC,CAApB;iBACanJ,kBAAkB1R,KAAlB,CAAwBoX,GAAxB,EAA6ByD,WAA7B,CAAb;;MAGE9T,OAAOgU,MAAP,YAAyBxW,UAA7B,EAAyC;WAChCwW,MAAP,GAAgBhU,OAAOgU,MAAP,CAAclD,IAA9B;;mBAGeT,GAAjB,EAAsBO,UAAtB;MAEIxJ,QAAJ,CAAawJ,WAAWhT,UAAxB,EAAoC,GAApC;MAEM0Q,OAAOzQ,SAASC,aAAT,CAAuB,MAAvB,CAAb;MACIsJ,QAAJ,CAAakH,IAAb,EAAmB,eAAnB;OACKjK,SAAL,GAAiBuM,WAAWlT,QAA5B;MAEMuW,YAAYpW,SAASC,aAAT,CAAuB,KAAvB,CAAlB;YACU8F,WAAV,CAAsB0K,IAAtB;YACU1K,WAAV,CAAsBgN,WAAWhT,UAAjC;MAEMuT,KAAK1B,OAAOY,GAAP,EAAY4D,SAAZ,EAAuBjU,OAAOgU,MAA9B,CAAX;MAEI5M,QAAJ,CAAa+J,EAAb,EAAiBrE,IAAIwD,oBAArB;MACIM,sBAAsB5I,eAA1B,EAA2C;QACrCZ,QAAJ,CAAa+J,EAAb,EAAiB,OAAjB;GADF,MAEO;QACD/J,QAAJ,CAAa+J,EAAb,UAAwBP,WAAWzS,QAAX,EAAxB;;oBAGgBkS,GAAlB,EAAuBc,EAAvB,EAA2BP,UAA3B;MAEI3D,aAAJ,CAAkB9K,IAAlB,CAAuByO,UAAvB;SAEOA,UAAP;;AAGF,SAAS7C,mBAAT,CAA6BsC,GAA7B,EAAkC1X,GAAlC,EAAuC;SAE9BkF,SAASqW,QAAT,CAAkB9Q,IAAlB,GAAyB,GAAzB,GAA+BzK,GAAtC;;AAGF,SAASwb,eAAT,CAAyB9D,GAAzB,EAA8B/B,IAA9B,EAAoC8F,WAApC,EAAiD;MACzChQ,MAAMvG,SAASC,aAAT,CAAuB,QAAvB,CAAZ;MACIuG,SAAJ,GAAgBiK,IAAhB;MACIlS,KAAJ,GAAYkS,IAAZ;MACI+D,eAAJ,CAAoBzO,WAApB,CAAgCQ,GAAhC;MACIgQ,WAAJ,EAAiB;QACX/B,eAAJ,CAAoB9N,aAApB,GAAoC8L,IAAIgC,eAAJ,CAAoB1a,MAApB,GAA6B,CAAjE;;;AAIJ,SAAS0c,eAAT,CAAyBhE,GAAzB,EAA8BiE,OAA9B,EAAuC;UAC7BvV,KAAR,CAAc0M,OAAd,GAAwB4E,IAAIxC,eAAJ,GAAsB,OAAtB,GAAgC,MAAxD;;AAGF,SAAS0G,WAAT,CAAqBlE,GAArB,EAA0B;MAClBmE,MAAMnE,IAAImB,UAAJ,GAAiB3T,SAASC,aAAT,CAAuB,IAAvB,CAA7B;MAEIsJ,QAAJ,CAAaiJ,IAAIzS,UAAjB,EAA6B,UAA7B;MAEImP,IAAJ,CAASqC,YAAT,CAAsBoF,GAAtB,EAA2BnE,IAAItD,IAAJ,CAAS0H,UAApC;MAEIrN,QAAJ,CAAaoN,GAAb,EAAkB,UAAlB;MAEME,QAAQ7W,SAASC,aAAT,CAAuB,MAAvB,CAAd;QACMuG,SAAN,GAAkB,QAAlB;MACI+C,QAAJ,CAAasN,KAAb,EAAoB,cAApB;MAGMC,SAAS9W,SAASC,aAAT,CAAuB,MAAvB,CAAf;SACOuG,SAAP,GAAmB,MAAnB;MACI+C,QAAJ,CAAauN,MAAb,EAAqB,QAArB;MACIvN,QAAJ,CAAauN,MAAb,EAAqB,MAArB;MAEMC,UAAU/W,SAASC,aAAT,CAAuB,MAAvB,CAAhB;UACQuG,SAAR,GAAoB,KAApB;MACI+C,QAAJ,CAAawN,OAAb,EAAsB,QAAtB;MACIxN,QAAJ,CAAawN,OAAb,EAAsB,SAAtB;MAEMC,UAAUhX,SAASC,aAAT,CAAuB,MAAvB,CAAhB;UACQuG,SAAR,GAAoB,QAApB;MACI+C,QAAJ,CAAayN,OAAb,EAAsB,QAAtB;MACIzN,QAAJ,CAAayN,OAAb,EAAsB,QAAtB;MAEMC,SAASzE,IAAIgC,eAAJ,GAAsBxU,SAASC,aAAT,CAAuB,QAAvB,CAArC;MAEIuS,IAAI9C,IAAJ,IAAY8C,IAAI9C,IAAJ,CAASsE,UAAzB,EAAqC;WAC5BzZ,IAAP,CAAYiY,IAAI9C,IAAJ,CAASsE,UAArB,EAAiC,UAASzV,KAAT,EAAgBzD,GAAhB,EAAqB;sBACpC0X,GAAhB,EAAqB1X,GAArB,EAA0BA,QAAQ0X,IAAI7C,MAAtC;KADF;GADF,MAIO;oBACW6C,GAAhB,EAAqB9D,2BAArB,EAAkD,KAAlD;;MAGE1T,IAAJ,CAASic,MAAT,EAAiB,QAAjB,EAA2B,YAAW;SAC/B,IAAIvS,QAAQ,CAAjB,EAAoBA,QAAQ8N,IAAIgC,eAAJ,CAAoB1a,MAAhD,EAAwD4K,OAAxD,EAAiE;UAC3D8P,eAAJ,CAAoB9P,KAApB,EAA2B8B,SAA3B,GAAuCgM,IAAIgC,eAAJ,CAAoB9P,KAApB,EAA2BnG,KAAlE;;QAGEoR,MAAJ,GAAa,KAAKpR,KAAlB;GALF;MAQIwH,WAAJ,CAAgBkR,MAAhB;MACIlR,WAAJ,CAAgB8Q,KAAhB;MACI9Q,WAAJ,CAAgB+Q,MAAhB;MACI/Q,WAAJ,CAAgBgR,OAAhB;MACIhR,WAAJ,CAAgBiR,OAAhB;MAEIrI,sBAAJ,EAA4B;QACpB8H,UAAUzW,SAASkX,cAAT,CAAwB,kBAAxB,CAAhB;QACMC,uBAAuBnX,SAASkX,cAAT,CAAwB,kBAAxB,CAA7B;QACME,cAAcpX,SAASkX,cAAT,CAAwB,iBAAxB,CAApB;gBAEYhW,KAAZ,CAAkB0M,OAAlB,GAA4B,OAA5B;QAEIgB,aAAaqB,OAAb,CAAqBC,oBAAoBsC,GAApB,EAAyB,SAAzB,CAArB,MAA8D,MAAlE,EAA0E;2BACnD5M,YAArB,CAAkC,SAAlC,EAA6C,SAA7C;;oBAGc4M,GAAhB,EAAqBiE,OAArB;QAGIzb,IAAJ,CAASmc,oBAAT,EAA+B,QAA/B,EAAyC,YAAW;UAC9CnH,eAAJ,GAAsB,CAACwC,IAAIxC,eAA3B;sBACgBwC,GAAhB,EAAqBiE,OAArB;KAFF;;MAMIY,yBAAyBrX,SAASkX,cAAT,CAAwB,oBAAxB,CAA/B;MAEIlc,IAAJ,CAASqc,sBAAT,EAAiC,SAAjC,EAA4C,UAAS3W,CAAT,EAAY;QAClDA,EAAE6C,OAAF,KAAc7C,EAAEmS,KAAF,KAAY,EAAZ,IAAkBnS,EAAE8C,OAAF,KAAc,EAA9C,CAAJ,EAAuD;oBACvCmK,IAAd;;GAFJ;MAMI3S,IAAJ,CAAS6b,KAAT,EAAgB,OAAhB,EAAyB,YAAW;2BACXrQ,SAAvB,GAAmC2K,KAAKe,SAAL,CAAeM,IAAIL,aAAJ,EAAf,EAAoCjW,SAApC,EAA+C,CAA/C,CAAnC;kBACcob,IAAd;2BACuBC,KAAvB;2BACuBN,MAAvB;GAJF;MAOIjc,IAAJ,CAAS8b,MAAT,EAAiB,OAAjB,EAA0B,YAAW;QAC/BU,IAAJ;GADF;MAIIxc,IAAJ,CAAS+b,OAAT,EAAkB,OAAlB,EAA2B,YAAW;QAC9B7C,aAAauD,OAAO,0BAAP,CAAnB;QACIvD,UAAJ,EAAgB;UACVwD,MAAJ,CAAWxD,UAAX;;GAHJ;MAOIlZ,IAAJ,CAASgc,OAAT,EAAkB,OAAlB,EAA2B,YAAW;QAChCxG,MAAJ;GADF;;AAOF,SAASmH,eAAT,CAAyBnF,GAAzB,EAA8B;MACxBoF,gBAAJ;MAEIhE,eAAJ,GAAsB5T,SAASC,aAAT,CAAuB,KAAvB,CAAtB;SAEOX,MAAP,CAAckT,IAAIoB,eAAJ,CAAoB1S,KAAlC,EAAyC;WAEhC,KAFgC;gBAG3B,MAH2B;YAI/B,OAJ+B;YAK/B,WAL+B;cAM7B;GANZ;WAWS2W,IAAT,CAAcnX,CAAd,EAAiB;MACb8I,cAAF;QAEI1E,KAAJ,IAAa8S,UAAUlX,EAAE6B,OAAzB;QACIqO,QAAJ;cACUlQ,EAAE6B,OAAZ;WAEO,KAAP;;WAGOuV,QAAT,GAAoB;QACdhN,WAAJ,CAAgB0H,IAAI3B,aAApB,EAAmC5B,IAAI0D,UAAvC;QACI9J,MAAJ,CAAW/F,MAAX,EAAmB,WAAnB,EAAgC+U,IAAhC;QACIhP,MAAJ,CAAW/F,MAAX,EAAmB,SAAnB,EAA8BgV,QAA9B;;WAGOC,SAAT,CAAmBrX,CAAnB,EAAsB;MAClB8I,cAAF;cAEU9I,EAAE6B,OAAZ;QAEIgH,QAAJ,CAAaiJ,IAAI3B,aAAjB,EAAgC5B,IAAI0D,UAApC;QACI3X,IAAJ,CAAS8H,MAAT,EAAiB,WAAjB,EAA8B+U,IAA9B;QACI7c,IAAJ,CAAS8H,MAAT,EAAiB,SAAjB,EAA4BgV,QAA5B;WAEO,KAAP;;MAGE9c,IAAJ,CAASwX,IAAIoB,eAAb,EAA8B,WAA9B,EAA2CmE,SAA3C;MACI/c,IAAJ,CAASwX,IAAI3B,aAAb,EAA4B,WAA5B,EAAyCkH,SAAzC;MAEIhY,UAAJ,CAAewR,YAAf,CAA4BiB,IAAIoB,eAAhC,EAAiDpB,IAAIzS,UAAJ,CAAe+U,iBAAhE;;AAGF,SAASkD,QAAT,CAAkBxF,GAAlB,EAAuByF,CAAvB,EAA0B;MACpBlY,UAAJ,CAAemB,KAAf,CAAqB4D,KAArB,GAA6BmT,IAAI,IAAjC;MAGIzF,IAAImB,UAAJ,IAAkBnB,IAAI/C,SAA1B,EAAqC;QAC/BkE,UAAJ,CAAezS,KAAf,CAAqB4D,KAArB,GAA6BmT,IAAI,IAAjC;;MAEEzF,IAAI3B,aAAR,EAAuB;QACjBA,aAAJ,CAAkB3P,KAAlB,CAAwB4D,KAAxB,GAAgCmT,IAAI,IAApC;;;AAIJ,SAAShE,gBAAT,CAA0BzB,GAA1B,EAA+B0F,gBAA/B,EAAiD;MACzCjb,WAAW,EAAjB;SAGO1C,IAAP,CAAYiY,IAAInD,mBAAhB,EAAqC,UAASxO,GAAT,EAAc6D,KAAd,EAAqB;QAClDyT,cAAc,EAApB;QAGMpC,gBACJvD,IAAIlD,sCAAJ,CAA2C5K,KAA3C,CADF;WAIOnK,IAAP,CAAYwb,aAAZ,EAA2B,UAAShD,UAAT,EAAqBlT,QAArB,EAA+B;kBAC5CA,QAAZ,IAAwBqY,mBAAmBnF,WAAWjT,YAA9B,GAA6CiT,WAAWzS,QAAX,EAArE;KADF;aAKSoE,KAAT,IAAkByT,WAAlB;GAbF;SAgBOlb,QAAP;;AAGF,SAASmb,oBAAT,CAA8B5F,GAA9B,EAAmC;OAC5B,IAAI9N,QAAQ,CAAjB,EAAoBA,QAAQ8N,IAAIgC,eAAJ,CAAoB1a,MAAhD,EAAwD4K,OAAxD,EAAiE;QAC3D8N,IAAIgC,eAAJ,CAAoB9P,KAApB,EAA2BnG,KAA3B,KAAqCiU,IAAI7C,MAA7C,EAAqD;UAC/C6E,eAAJ,CAAoB9N,aAApB,GAAoChC,KAApC;;;;AAKN,SAAS2T,cAAT,CAAwBC,eAAxB,EAAyC;MACnCA,gBAAgBxe,MAAhB,KAA2B,CAA/B,EAAkC;4BACVU,IAAtB,CAA2BsI,MAA3B,EAAmC,YAAW;qBAC7BwV,eAAf;KADF;;SAKK/d,IAAP,CAAY+d,eAAZ,EAA6B,UAASta,CAAT,EAAY;MACrCqC,aAAF;GADF;;;AC91CK,IAAMzH,QAAQ;SACZ4F,KADY;QAEbY,SAFa;aAGRlC;CAHN;AAMP,AAAO,IAAMqb,cAAc;cACb5Y,UADa;qBAEN6F,iBAFM;oBAGPS,gBAHO;oBAIPW,gBAJO;oBAKPM,gBALO;uBAMJmB,mBANI;0BAODc,sBAPC;sBAQLY,kBARK;mBASRI;CATZ;AAYP,AAAO,IAAMpJ,QAAM,EAAEA,KAAKyX,GAAP,EAAZ;AAEP,AAAO,IAAMhG,MAAM,EAAEvD,KAAKwJ,GAAP,EAAZ;AAEP,AAAO,IAAMxJ,QAAMwJ,GAAZ;AAEP,YAAe;cAAA;0BAAA;YAAA;UAAA;;CAAf;;;;;"}
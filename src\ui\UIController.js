export class UIController {
    constructor(app) {
        this.app = app;
        this.searchInput = null;
        this.controlButtons = {};
        this.suggestionsList = null;

        this.init();
        this.addStyles();
    }
    
    init() {
        this.setupSearchBox();
        this.setupControlButtons();
        this.setupKeyboardShortcuts();
    }
    
    setupSearchBox() {
        this.searchInput = document.getElementById('search-input');
        
        if (this.searchInput) {
            // 搜索功能
            this.searchInput.addEventListener('keypress', (event) => {
                if (event.key === 'Enter') {
                    this.handleSearch(this.searchInput.value);
                }
            });
            
            // 自动完成建议
            this.searchInput.addEventListener('input', (event) => {
                this.showSearchSuggestions(event.target.value);
            });
        }
    }
    
    setupControlButtons() {
        // 重置视图按钮
        const resetButton = document.getElementById('reset-view');
        if (resetButton) {
            resetButton.addEventListener('click', () => {
                this.app.resetView();
            });
            this.controlButtons.reset = resetButton;
        }
        
        // 大气层切换按钮
        const atmosphereButton = document.getElementById('toggle-atmosphere');
        if (atmosphereButton) {
            atmosphereButton.addEventListener('click', () => {
                this.app.settings.showAtmosphere = !this.app.settings.showAtmosphere;
                if (this.app.atmosphere) {
                    this.app.atmosphere.setVisible(this.app.settings.showAtmosphere);
                }
                this.updateButtonState(atmosphereButton, this.app.settings.showAtmosphere);
            });
            this.controlButtons.atmosphere = atmosphereButton;
        }
        
        // 云层切换按钮
        const cloudsButton = document.getElementById('toggle-clouds');
        if (cloudsButton) {
            cloudsButton.addEventListener('click', () => {
                this.app.settings.showClouds = !this.app.settings.showClouds;
                if (this.app.cloudLayer) {
                    this.app.cloudLayer.setVisible(this.app.settings.showClouds);
                }
                this.updateButtonState(cloudsButton, this.app.settings.showClouds);
            });
            this.controlButtons.clouds = cloudsButton;
        }
        
        // 星空切换按钮
        const starsButton = document.getElementById('toggle-stars');
        if (starsButton) {
            starsButton.addEventListener('click', () => {
                this.app.settings.showStars = !this.app.settings.showStars;
                if (this.app.starField) {
                    this.app.starField.setVisible(this.app.settings.showStars);
                }
                this.updateButtonState(starsButton, this.app.settings.showStars);
            });
            this.controlButtons.stars = starsButton;
        }
        
        // 标签切换按钮
        const labelsButton = document.getElementById('toggle-labels');
        if (labelsButton) {
            labelsButton.addEventListener('click', () => {
                this.app.settings.showLabels = !this.app.settings.showLabels;
                if (this.app.locationMarkers) {
                    this.app.locationMarkers.setVisible(this.app.settings.showLabels);
                }
                this.updateButtonState(labelsButton, this.app.settings.showLabels);
            });
            this.controlButtons.labels = labelsButton;
        }
        
        // 初始化按钮状态
        this.updateAllButtonStates();
    }
    
    setupKeyboardShortcuts() {
        document.addEventListener('keydown', (event) => {
            // 防止在输入框中触发快捷键
            if (event.target.tagName === 'INPUT') return;
            
            switch(event.key.toLowerCase()) {
                case 'h':
                    this.toggleHelp();
                    break;
                case 'f':
                    this.toggleFullscreen();
                    break;
                case '1':
                    this.app.settings.showAtmosphere = !this.app.settings.showAtmosphere;
                    if (this.app.atmosphere) {
                        this.app.atmosphere.setVisible(this.app.settings.showAtmosphere);
                    }
                    this.updateButtonState(this.controlButtons.atmosphere, this.app.settings.showAtmosphere);
                    break;
                case '2':
                    this.app.settings.showClouds = !this.app.settings.showClouds;
                    if (this.app.cloudLayer) {
                        this.app.cloudLayer.setVisible(this.app.settings.showClouds);
                    }
                    this.updateButtonState(this.controlButtons.clouds, this.app.settings.showClouds);
                    break;
                case '3':
                    this.app.settings.showStars = !this.app.settings.showStars;
                    if (this.app.starField) {
                        this.app.starField.setVisible(this.app.settings.showStars);
                    }
                    this.updateButtonState(this.controlButtons.stars, this.app.settings.showStars);
                    break;
                case '4':
                    this.app.settings.showLabels = !this.app.settings.showLabels;
                    if (this.app.locationMarkers) {
                        this.app.locationMarkers.setVisible(this.app.settings.showLabels);
                    }
                    this.updateButtonState(this.controlButtons.labels, this.app.settings.showLabels);
                    break;
            }
        });
    }
    
    handleSearch(query) {
        if (!query.trim()) return;
        
        // 搜索位置
        const location = this.findLocation(query);
        if (location) {
            // 聚焦到找到的位置
            this.app.cameraController.focusOnLatLng(location.lat, location.lng);
            
            // 高亮标记
            if (this.app.locationMarkers) {
                this.app.locationMarkers.highlightMarker(location.name);
                setTimeout(() => {
                    this.app.locationMarkers.clearHighlights();
                }, 3000);
            }
            
            this.showNotification(`已定位到: ${location.name}`);
        } else {
            this.showNotification(`未找到位置: ${query}`, 'error');
        }
        
        // 清空搜索框
        this.searchInput.value = '';
    }
    
    findLocation(query) {
        if (!this.app.locationMarkers) return null;
        
        const locations = this.app.locationMarkers.locations;
        const lowerQuery = query.toLowerCase();
        
        return locations.find(location => 
            location.name.toLowerCase().includes(lowerQuery) ||
            location.name.toLowerCase() === lowerQuery
        );
    }
    
    showSearchSuggestions(query) {
        if (query.length < 2) {
            this.hideSuggestions();
            return;
        }

        const suggestions = this.app.locationMarkers?.locations.filter(location =>
            location.name.toLowerCase().includes(query.toLowerCase())
        ) || [];

        if (suggestions.length === 0) {
            this.hideSuggestions();
            return;
        }

        this.createSuggestionsList(suggestions.slice(0, 5)); // 最多显示5个建议
    }

    createSuggestionsList(suggestions) {
        this.hideSuggestions();

        this.suggestionsList = document.createElement('div');
        this.suggestionsList.className = 'suggestions-list';
        this.suggestionsList.style.cssText = `
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: rgba(0, 0, 0, 0.9);
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 10px;
            margin-top: 5px;
            backdrop-filter: blur(10px);
            z-index: 1000;
        `;

        suggestions.forEach((suggestion, index) => {
            const item = document.createElement('div');
            item.className = 'suggestion-item';
            item.textContent = suggestion.name;
            item.style.cssText = `
                padding: 10px 15px;
                cursor: pointer;
                transition: background 0.2s ease;
                border-bottom: ${index < suggestions.length - 1 ? '1px solid rgba(255, 255, 255, 0.1)' : 'none'};
            `;

            item.addEventListener('mouseenter', () => {
                item.style.background = 'rgba(66, 133, 244, 0.3)';
            });

            item.addEventListener('mouseleave', () => {
                item.style.background = 'transparent';
            });

            item.addEventListener('click', () => {
                this.handleSearch(suggestion.name);
                this.hideSuggestions();
            });

            this.suggestionsList.appendChild(item);
        });

        const searchContainer = document.querySelector('.search-container');
        searchContainer.appendChild(this.suggestionsList);
    }

    hideSuggestions() {
        if (this.suggestionsList) {
            this.suggestionsList.remove();
            this.suggestionsList = null;
        }
    }
    
    updateButtonState(button, isActive) {
        if (!button) return;
        
        if (isActive) {
            button.style.background = 'rgba(66, 133, 244, 0.8)';
            button.style.color = 'white';
        } else {
            button.style.background = 'rgba(255, 255, 255, 0.1)';
            button.style.color = 'rgba(255, 255, 255, 0.8)';
        }
    }
    
    updateAllButtonStates() {
        this.updateButtonState(this.controlButtons.atmosphere, this.app.settings.showAtmosphere);
        this.updateButtonState(this.controlButtons.clouds, this.app.settings.showClouds);
        this.updateButtonState(this.controlButtons.stars, this.app.settings.showStars);
        this.updateButtonState(this.controlButtons.labels, this.app.settings.showLabels);
    }
    
    showNotification(message, type = 'info') {
        // 创建通知元素
        const notification = document.createElement('div');
        notification.style.cssText = `
            position: fixed;
            top: 80px;
            left: 50%;
            transform: translateX(-50%);
            background: ${type === 'error' ? 'rgba(255, 68, 68, 0.9)' : 'rgba(66, 133, 244, 0.9)'};
            color: white;
            padding: 12px 24px;
            border-radius: 25px;
            font-size: 14px;
            z-index: 1000;
            backdrop-filter: blur(10px);
            animation: slideDown 0.3s ease;
        `;
        
        notification.textContent = message;
        document.body.appendChild(notification);
        
        // 3秒后自动移除
        setTimeout(() => {
            notification.style.animation = 'slideUp 0.3s ease';
            setTimeout(() => {
                document.body.removeChild(notification);
            }, 300);
        }, 3000);
    }
    
    toggleHelp() {
        const helpPanel = document.querySelector('.info-panel');
        if (helpPanel) {
            helpPanel.style.display = helpPanel.style.display === 'none' ? 'block' : 'none';
        }
    }
    
    toggleFullscreen() {
        if (!document.fullscreenElement) {
            document.documentElement.requestFullscreen();
        } else {
            document.exitFullscreen();
        }
    }
    
    // 添加CSS动画
    addStyles() {
        const style = document.createElement('style');
        style.textContent = `
            @keyframes slideDown {
                from {
                    opacity: 0;
                    transform: translateX(-50%) translateY(-20px);
                }
                to {
                    opacity: 1;
                    transform: translateX(-50%) translateY(0);
                }
            }
            
            @keyframes slideUp {
                from {
                    opacity: 1;
                    transform: translateX(-50%) translateY(0);
                }
                to {
                    opacity: 0;
                    transform: translateX(-50%) translateY(-20px);
                }
            }
        `;
        document.head.appendChild(style);
    }
}

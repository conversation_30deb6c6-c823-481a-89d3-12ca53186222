$nest-margin: 4px;
$row-height: 27px;

$button-height: 20px;

.dg {

  /** Clear list styles */
  ul {
    list-style: none;
    margin: 0;
    padding: 0;
    width: 100%;
    clear: both;
  }

  /* Auto-place container */
  &.ac {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    height: 0;
    z-index: 0;
  }

  &:not(.ac) .main {
    /** Exclude mains in ac so that we don't hide close button */
    overflow: hidden;
  }

  &.main {

    @include transition(opacity, 0.1s, linear);

    &.taller-than-window {

      overflow-y: auto;

      .close-button {

        opacity: 1;

        /* TODO, these are style notes */
        margin-top: -1px;
        border-top: 1px solid $border-color;

      }

    }

    ul.closed .close-button {
      opacity: 1 !important;
    }

    &:hover .close-button,
    .close-button.drag {
      opacity: 1;
    }

    .close-button {
      /*opacity: 0;*/
      @include transition(opacity, 0.1s, linear);
      border: 0;

      line-height: $button-height - 1;
      height: $button-height;

      /* TODO, these are style notes */
      cursor: pointer;
      text-align: center;
      background-color: #000;
      &.close-top {
        position: relative;
      }
      &.close-bottom {
        position: absolute;
      }
      &:hover {
        background-color: #111;
      }

    }

  }

  /* Auto-placed GUI's */
  &.a {

    float: right;
    margin-right: 15px;
    overflow-y:visible;

    &.has-save > ul {

      &.close-top {
        margin-top: 0;
      }
      &.close-bottom {
        margin-top: $row-height;
      }

      &.closed {
        margin-top: 0;
      }
    }

    .save-row {
      top: 0;
      z-index: 1002;

      &.close-top {
        position: relative;
      }
      &.close-bottom {
        position: fixed;
      }

    }

  }

  li {
    @include transition(height, 0.1s, ease-out);
    @include transition(overflow, 0.1s, linear);
  }

  /* Line items that don't contain folders. */
  li:not(.folder) {
    cursor: auto;
    height: $row-height;
    line-height: $row-height;
    padding: 0 4px 0 5px;
  }

  li.folder {
    padding: 0;
    border-left: $nest-margin solid rgba(0, 0, 0, 0);

  }

  /** Folder names */
  li.title {
    cursor: pointer;
    margin-left: -$nest-margin;
  }

  /** Hides closed items */
  .closed li:not(.title),
  .closed ul li,
  .closed ul li > * {
    height: 0;
    overflow: hidden;
    border: 0;
  }

  /** Controller row */
  .cr {
    clear: both;
    padding-left: 3px;
    height: $row-height;
    overflow: hidden;
  }

  /** Name-half (left) */
  .property-name {
    cursor: default;
    float: left;
    clear: left;
    width: 40%;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  /** Function controllers can use the entire width */
  .cr.function .property-name {
    width: 100%;
  }

  /** Controller-half (right) */
  .c {
    float: left;
    width: 60%;
    position: relative;
  }

  /** Controller placement */
  .c input[type=text] {
    border: 0;
    margin-top: 4px;
    padding: 3px;
    width: 100%;
    float: right;
  }

  /** Shorter number boxes when slider is present. */
  .has-slider input[type=text] {
    width: 30%;
    /*display: none;*/
    margin-left: 0;
  }

  .slider {
    float: left;
    width: 66%;
    margin-left: -5px;
    margin-right: 0;
    height: 19px;
    margin-top: 4px;
  }

  .slider-fg {
    height: 100%;
  }

  .c input[type=checkbox] {
    margin-top: 7px;
  }

  .c select {
    margin-top: 5px;
  }

  /** Ensure the entire boolean and function row shows a hand */
  .cr.function,
  .cr.function .property-name, /* Don't know why I need to be this explicit */
  .cr.function *,
  .cr.boolean,
  .cr.boolean * {
    cursor: pointer;
  }

  /** allow overflow for color selector */
  .cr.color {
    overflow: visible;
  }

  .selector {
    display: none;
    position: absolute;
    margin-left: -9px;
    margin-top: 23px;
    z-index: 10;
  }

  .c:hover .selector,
  .selector.drag {
    display: block;
  }

  li.save-row {

    padding: 0;

    .button {
      display: inline-block;
      padding: 0px 6px;
    }

  }

  &.dialogue {
    background-color: #222;
    width: 460px;
    padding: 15px;
    font-size: 13px;
    line-height: 15px;
  }

}

/* TODO Separate style and structure */
#dg-new-constructor {
  padding: 10px;
  color: #222;
  font-family: Monaco, monospace;
  font-size: 10px;
  border: 0;
  resize: none;
  box-shadow: inset 1px 1px 1px #888;
  word-wrap: break-word;
  margin: 12px 0;
  display: block;
  width: 440px;
  overflow-y: scroll;
  height: 100px;
  position: relative;
}

#dg-local-explain {
  display: none;
  font-size: 11px;
  line-height: 17px;
  border-radius: 3px;
  background-color: #333;
  padding: 8px;
  margin-top: 10px;
  code {
    font-size: 10px;
  }
}

#dat-gui-save-locally {
  display: none;
}

<template>
  <Transition name="slide-panel">
    <div v-if="visible" class="control-panel">
      <div class="panel-header">
        <h3 class="panel-title">控制面板</h3>
        <button class="close-button" @click="$emit('update:visible', false)">
          <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
            <line x1="18" y1="6" x2="6" y2="18" stroke="currentColor" stroke-width="2"/>
            <line x1="6" y1="6" x2="18" y2="18" stroke="currentColor" stroke-width="2"/>
          </svg>
        </button>
      </div>

      <div class="panel-content">
        <!-- 地球设置 -->
        <div class="control-section">
          <h4 class="section-title">地球设置</h4>

          <div class="control-item">
            <label class="control-label">自转速度</label>
            <div class="slider-container">
              <input
                type="range"
                min="0"
                max="0.01"
                step="0.001"
                :value="settings.earthRotation"
                @input="updateSetting('earthRotation', parseFloat($event.target.value))"
                class="slider"
              />
              <span class="slider-value">{{ (settings.earthRotation * 1000).toFixed(1) }}</span>
            </div>
          </div>
        </div>

        <!-- 视觉效果 -->
        <div class="control-section">
          <h4 class="section-title">视觉效果</h4>

          <div class="control-item">
            <label class="control-label">
              <input
                type="checkbox"
                :checked="settings.showAtmosphere"
                @change="updateSetting('showAtmosphere', $event.target.checked)"
                class="checkbox"
              />
              <span class="checkbox-label">大气层</span>
            </label>
          </div>

          <div class="control-item">
            <label class="control-label">
              <input
                type="checkbox"
                :checked="settings.showClouds"
                @change="updateSetting('showClouds', $event.target.checked)"
                class="checkbox"
              />
              <span class="checkbox-label">云层</span>
            </label>
          </div>

          <div class="control-item">
            <label class="control-label">
              <input
                type="checkbox"
                :checked="settings.showStars"
                @change="updateSetting('showStars', $event.target.checked)"
                class="checkbox"
              />
              <span class="checkbox-label">星空</span>
            </label>
          </div>

          <div class="control-item">
            <label class="control-label">
              <input
                type="checkbox"
                :checked="settings.showLabels"
                @change="updateSetting('showLabels', $event.target.checked)"
                class="checkbox"
              />
              <span class="checkbox-label">标签</span>
            </label>
          </div>
        </div>

        <!-- 光照设置 -->
        <div class="control-section">
          <h4 class="section-title">光照设置</h4>

          <div class="control-item">
            <label class="control-label">大气强度</label>
            <div class="slider-container">
              <input
                type="range"
                min="0"
                max="2"
                step="0.1"
                :value="settings.atmosphereIntensity || 1"
                @input="updateSetting('atmosphereIntensity', parseFloat($event.target.value))"
                class="slider"
              />
              <span class="slider-value">{{ (settings.atmosphereIntensity || 1).toFixed(1) }}</span>
            </div>
          </div>

          <div class="control-item">
            <label class="control-label">星空密度</label>
            <div class="slider-container">
              <input
                type="range"
                min="0.1"
                max="2"
                step="0.1"
                :value="settings.starDensity || 1"
                @input="updateSetting('starDensity', parseFloat($event.target.value))"
                class="slider"
              />
              <span class="slider-value">{{ (settings.starDensity || 1).toFixed(1) }}</span>
            </div>
          </div>
        </div>

        <!-- 快捷操作 -->
        <div class="control-section">
          <h4 class="section-title">快捷操作</h4>

          <button class="action-button" @click="resetToDefaults">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
              <path d="M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8" stroke="currentColor" stroke-width="2"/>
              <path d="M21 3v5h-5" stroke="currentColor" stroke-width="2"/>
            </svg>
            重置默认设置
          </button>
        </div>
      </div>
    </div>
  </Transition>
</template>

<script setup>
const props = defineProps(['visible', 'settings'])
const emit = defineEmits(['update:visible', 'update-settings'])

function updateSetting(key, value) {
  emit('update-settings', { [key]: value })
}

function resetToDefaults() {
  emit('update-settings', {
    showAtmosphere: true,
    showClouds: true,
    showStars: true,
    showLabels: true,
    earthRotation: 0.001,
    atmosphereIntensity: 1.0,
    starDensity: 1.0
  })
}
</script>

<style lang="scss" scoped>
.control-panel {
  position: fixed;
  top: 80px;
  right: 20px;
  width: 320px;
  max-height: calc(100vh - 120px);
  background: rgba(0, 0, 0, 0.9);
  backdrop-filter: blur(20px);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-lg);
  z-index: 150;
}

.panel-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;
  border-bottom: 1px solid var(--border-color);

  .panel-title {
    font-size: 16px;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
  }

  .close-button {
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: transparent;
    border: none;
    border-radius: var(--radius-md);
    color: var(--text-secondary);
    cursor: pointer;
    transition: all var(--transition-fast);

    &:hover {
      background: rgba(255, 255, 255, 0.1);
      color: var(--text-primary);
    }
  }
}

.panel-content {
  max-height: calc(100vh - 200px);
  overflow-y: auto;
  padding: 20px;
}

.control-section {
  margin-bottom: 24px;

  &:last-child {
    margin-bottom: 0;
  }

  .section-title {
    font-size: 14px;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0 0 12px 0;
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }
}

.control-item {
  margin-bottom: 16px;

  &:last-child {
    margin-bottom: 0;
  }

  .control-label {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 13px;
    color: var(--text-secondary);
    cursor: pointer;

    .checkbox {
      width: 16px;
      height: 16px;
      accent-color: var(--primary-color);
    }

    .checkbox-label {
      flex: 1;
    }
  }
}

.slider-container {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-top: 8px;

  .slider {
    flex: 1;
    height: 4px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 2px;
    outline: none;
    cursor: pointer;

    &::-webkit-slider-thumb {
      appearance: none;
      width: 16px;
      height: 16px;
      background: var(--primary-color);
      border-radius: 50%;
      cursor: pointer;
    }

    &::-moz-range-thumb {
      width: 16px;
      height: 16px;
      background: var(--primary-color);
      border-radius: 50%;
      border: none;
      cursor: pointer;
    }
  }

  .slider-value {
    font-size: 12px;
    color: var(--text-tertiary);
    font-family: 'Monaco', 'Menlo', monospace;
    min-width: 30px;
    text-align: right;
  }
}

.action-button {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 12px 16px;
  background: rgba(66, 133, 244, 0.2);
  border: 1px solid var(--primary-color);
  border-radius: var(--radius-md);
  color: var(--primary-color);
  font-size: 13px;
  font-weight: 500;
  cursor: pointer;
  transition: all var(--transition-fast);

  &:hover {
    background: rgba(66, 133, 244, 0.3);
    transform: translateY(-1px);
  }
}

// 过渡动画
.slide-panel-enter-active,
.slide-panel-leave-active {
  transition: all 0.3s ease;
}

.slide-panel-enter-from {
  opacity: 0;
  transform: translateX(100%);
}

.slide-panel-leave-to {
  opacity: 0;
  transform: translateX(100%);
}

// 响应式设计
@media (max-width: 768px) {
  .control-panel {
    top: 70px;
    right: 16px;
    left: 16px;
    width: auto;
  }
}
</style>

import * as THREE from 'three';
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls.js';
import { GUI } from 'dat.gui';
import { EarthScene } from './earth/EarthScene.js';
import { AtmosphereEffect } from './effects/AtmosphereEffect.js';
import { StarField } from './effects/StarField.js';
import { CloudLayer } from './effects/CloudLayer.js';
import { LocationMarkers } from './markers/LocationMarkers.js';
import { CameraController } from './controls/CameraController.js';
import { UIController } from './ui/UIController.js';
import { PerformanceMonitor, LODSystem } from './utils/PerformanceMonitor.js';

class GoogleEarthClone {
    constructor() {
        this.scene = null;
        this.camera = null;
        this.renderer = null;
        this.controls = null;
        this.gui = null;
        
        // 场景组件
        this.earthScene = null;
        this.atmosphere = null;
        this.starField = null;
        this.cloudLayer = null;
        this.locationMarkers = null;
        this.cameraController = null;
        this.uiController = null;
        this.performanceMonitor = null;
        this.lodSystem = null;

        // 设置
        this.settings = {
            showAtmosphere: true,
            showClouds: true,
            showStars: true,
            showLabels: true,
            earthRotation: 0.001,
            cloudSpeed: 0.0005
        };
        
        this.init();
    }
    
    async init() {
        try {
            this.setupScene();
            this.setupCamera();
            this.setupRenderer();
            this.setupControls();
            this.setupGUI();
            
            await this.loadComponents();
            
            this.setupEventListeners();
            this.hideLoadingScreen();
            this.animate();
            
        } catch (error) {
            console.error('初始化失败:', error);
            this.showError('加载失败，请刷新页面重试');
        }
    }
    
    setupScene() {
        this.scene = new THREE.Scene();
        this.scene.background = new THREE.Color(0x000000);
    }
    
    setupCamera() {
        this.camera = new THREE.PerspectiveCamera(
            45,
            window.innerWidth / window.innerHeight,
            0.1,
            10000
        );
        this.camera.position.set(0, 0, 5);
    }
    
    setupRenderer() {
        this.renderer = new THREE.WebGLRenderer({
            antialias: true,
            alpha: true
        });
        this.renderer.setSize(window.innerWidth, window.innerHeight);
        this.renderer.setPixelRatio(Math.min(window.devicePixelRatio, 2));
        this.renderer.shadowMap.enabled = true;
        this.renderer.shadowMap.type = THREE.PCFSoftShadowMap;
        this.renderer.toneMapping = THREE.ACESFilmicToneMapping;
        this.renderer.toneMappingExposure = 1.0;
        
        const container = document.getElementById('canvas-container');
        container.appendChild(this.renderer.domElement);
    }
    
    setupControls() {
        this.controls = new OrbitControls(this.camera, this.renderer.domElement);
        this.controls.enableDamping = true;
        this.controls.dampingFactor = 0.05;
        this.controls.minDistance = 1.5;
        this.controls.maxDistance = 50;
        this.controls.enablePan = true;
        this.controls.panSpeed = 0.5;
        this.controls.rotateSpeed = 0.5;
        this.controls.zoomSpeed = 0.8;
    }
    
    setupGUI() {
        this.gui = new GUI();
        this.gui.hide(); // 默认隐藏，可以通过按键显示
        
        const earthFolder = this.gui.addFolder('地球设置');
        earthFolder.add(this.settings, 'earthRotation', 0, 0.01).name('自转速度');
        
        const effectsFolder = this.gui.addFolder('视觉效果');
        effectsFolder.add(this.settings, 'showAtmosphere').name('显示大气层').onChange((value) => {
            if (this.atmosphere) this.atmosphere.setVisible(value);
        });
        effectsFolder.add(this.settings, 'showClouds').name('显示云层').onChange((value) => {
            if (this.cloudLayer) this.cloudLayer.setVisible(value);
        });
        effectsFolder.add(this.settings, 'showStars').name('显示星空').onChange((value) => {
            if (this.starField) this.starField.setVisible(value);
        });
        effectsFolder.add(this.settings, 'showLabels').name('显示标签').onChange((value) => {
            if (this.locationMarkers) this.locationMarkers.setVisible(value);
        });
        effectsFolder.add(this.settings, 'cloudSpeed', 0, 0.005).name('云层速度');
    }
    
    async loadComponents() {
        // 创建地球场景
        this.earthScene = new EarthScene();
        await this.earthScene.init();
        this.scene.add(this.earthScene.getGroup());
        
        // 创建大气层效果
        this.atmosphere = new AtmosphereEffect();
        await this.atmosphere.init();
        this.scene.add(this.atmosphere.getMesh());
        
        // 创建星空背景
        this.starField = new StarField();
        await this.starField.init();
        this.scene.add(this.starField.getPoints());
        
        // 创建云层
        this.cloudLayer = new CloudLayer();
        await this.cloudLayer.init();
        this.scene.add(this.cloudLayer.getMesh());
        
        // 创建位置标记
        this.locationMarkers = new LocationMarkers();
        await this.locationMarkers.init();
        this.scene.add(this.locationMarkers.getGroup());
        
        // 创建相机控制器
        this.cameraController = new CameraController(this.camera, this.controls);
        
        // 创建UI控制器
        this.uiController = new UIController(this);

        // 创建性能监控器
        this.performanceMonitor = new PerformanceMonitor();
        this.lodSystem = new LODSystem();
    }
    
    setupEventListeners() {
        window.addEventListener('resize', this.onWindowResize.bind(this));
        
        // 键盘事件
        window.addEventListener('keydown', (event) => {
            switch(event.code) {
                case 'KeyG':
                    this.gui.domElement.style.display = 
                        this.gui.domElement.style.display === 'none' ? 'block' : 'none';
                    break;
                case 'KeyR':
                    this.resetView();
                    break;
                case 'Space':
                    event.preventDefault();
                    this.settings.earthRotation = this.settings.earthRotation > 0 ? 0 : 0.001;
                    break;
                case 'KeyP':
                    this.performanceMonitor.toggle();
                    break;
            }
        });
        
        // 双击事件 - 聚焦到点击位置
        this.renderer.domElement.addEventListener('dblclick', this.onDoubleClick.bind(this));
    }
    
    onWindowResize() {
        this.camera.aspect = window.innerWidth / window.innerHeight;
        this.camera.updateProjectionMatrix();
        this.renderer.setSize(window.innerWidth, window.innerHeight);
    }
    
    onDoubleClick(event) {
        const mouse = new THREE.Vector2();
        mouse.x = (event.clientX / window.innerWidth) * 2 - 1;
        mouse.y = -(event.clientY / window.innerHeight) * 2 + 1;
        
        const raycaster = new THREE.Raycaster();
        raycaster.setFromCamera(mouse, this.camera);
        
        if (this.earthScene && this.earthScene.getEarthMesh()) {
            const intersects = raycaster.intersectObject(this.earthScene.getEarthMesh());
            if (intersects.length > 0) {
                const point = intersects[0].point;
                this.cameraController.focusOnPoint(point);
            }
        }
    }
    
    resetView() {
        this.cameraController.resetView();
    }
    
    animate() {
        requestAnimationFrame(this.animate.bind(this));
        
        // 更新控制器
        this.controls.update();
        
        // 更新各个组件
        if (this.earthScene) {
            this.earthScene.update(this.settings.earthRotation);
        }
        
        if (this.cloudLayer) {
            this.cloudLayer.update(this.settings.cloudSpeed);
        }
        
        if (this.atmosphere) {
            this.atmosphere.update(this.camera);
        }
        
        if (this.locationMarkers) {
            this.locationMarkers.update(this.camera);
        }

        // 更新性能监控
        if (this.performanceMonitor) {
            this.performanceMonitor.update(this.renderer);
        }

        // 渲染场景
        this.renderer.render(this.scene, this.camera);
    }
    
    hideLoadingScreen() {
        const loadingScreen = document.getElementById('loading-screen');
        loadingScreen.classList.add('hidden');
        setTimeout(() => {
            loadingScreen.style.display = 'none';
        }, 500);
    }
    
    showError(message) {
        const loadingScreen = document.getElementById('loading-screen');
        loadingScreen.innerHTML = `
            <div style="color: #ff4444; text-align: center;">
                <h2>❌ 错误</h2>
                <p>${message}</p>
                <button onclick="location.reload()" style="
                    margin-top: 20px;
                    padding: 10px 20px;
                    background: #4285f4;
                    color: white;
                    border: none;
                    border-radius: 5px;
                    cursor: pointer;
                ">重新加载</button>
            </div>
        `;
    }
}

// 启动应用
new GoogleEarthClone();

import * as THREE from 'three'
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls.js'

export class EarthEngine {
  constructor(container) {
    this.container = container
    this.scene = null
    this.camera = null
    this.renderer = null
    this.controls = null
    this.earth = null
    this.atmosphere = null
    this.clouds = null
    this.stars = null
    this.sunLight = null
    this.animationId = null

    // 回调函数
    this.onLocationUpdate = null
    this.onPerformanceUpdate = null
    this.onLocationClick = null

    // 设置
    this.settings = {
      showAtmosphere: true,
      showClouds: true,
      showStars: true,
      showLabels: true,
      earthRotation: 0.001
    }

    // 绑定animate方法的this上下文
    this.animate = this.animate.bind(this)
  }
  
  async init() {
    try {
      this.setupScene()
      this.setupCamera()
      this.setupRenderer()
      this.setupControls()
      await this.createEarth()
      this.setupLights()
      this.setupEventListeners()
      this.animate()
      console.log('地球引擎初始化成功')
    } catch (error) {
      console.error('地球引擎初始化失败:', error)
      throw error
    }
  }
  
  setupScene() {
    this.scene = new THREE.Scene()
    this.scene.background = new THREE.Color(0x000000)
  }
  
  setupCamera() {
    this.camera = new THREE.PerspectiveCamera(
      45,
      window.innerWidth / window.innerHeight,
      0.1,
      1000
    )
    this.camera.position.set(0, 0, 5)
  }
  
  setupRenderer() {
    this.renderer = new THREE.WebGLRenderer({
      antialias: true,
      alpha: true
    })
    this.renderer.setSize(window.innerWidth, window.innerHeight)
    this.renderer.setPixelRatio(Math.min(window.devicePixelRatio, 2))
    this.renderer.shadowMap.enabled = true
    this.renderer.shadowMap.type = THREE.PCFSoftShadowMap
    
    this.container.appendChild(this.renderer.domElement)
  }
  
  setupControls() {
    this.controls = new OrbitControls(this.camera, this.renderer.domElement)
    this.controls.enableDamping = true
    this.controls.dampingFactor = 0.05
    this.controls.minDistance = 1.5
    this.controls.maxDistance = 50
  }
  
  async createEarth() {
    try {
      // 创建高精度地球几何体
      const geometry = new THREE.SphereGeometry(1, 128, 64)

      // 创建高级地球材质
      await this.createAdvancedEarthMaterial()

      this.earth = new THREE.Mesh(geometry, this.earthMaterial)
      this.earth.castShadow = true
      this.earth.receiveShadow = true
      this.scene.add(this.earth)

      // 创建夜间城市灯光
      this.createCityLights()

      // 创建大气层效果
      this.createAdvancedAtmosphere()

      // 创建云层
      this.createClouds()

      // 创建星空
      this.createAdvancedStarField()

    } catch (error) {
      console.error('创建地球失败:', error)
      throw error
    }
  }

  async createAdvancedEarthMaterial() {
    // 创建日间纹理
    const dayTexture = this.createDayTexture()

    // 创建夜间纹理
    const nightTexture = this.createNightTexture()

    // 创建法线贴图
    const normalTexture = this.createNormalMap()

    // 创建镜面反射贴图
    const specularTexture = this.createSpecularMap()

    // 使用自定义着色器实现日夜过渡
    const vertexShader = `
      varying vec2 vUv;
      varying vec3 vNormal;
      varying vec3 vPosition;
      varying vec3 vWorldPosition;

      void main() {
        vUv = uv;
        vNormal = normalize(normalMatrix * normal);
        vPosition = position;
        vWorldPosition = (modelMatrix * vec4(position, 1.0)).xyz;
        gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
      }
    `

    const fragmentShader = `
      uniform sampler2D dayTexture;
      uniform sampler2D nightTexture;
      uniform sampler2D normalTexture;
      uniform sampler2D specularTexture;
      uniform vec3 sunDirection;
      uniform vec3 cameraPosition;
      uniform float time;

      varying vec2 vUv;
      varying vec3 vNormal;
      varying vec3 vPosition;
      varying vec3 vWorldPosition;

      void main() {
        // 采样纹理
        vec4 dayColor = texture2D(dayTexture, vUv);
        vec4 nightColor = texture2D(nightTexture, vUv);
        vec3 normalMap = texture2D(normalTexture, vUv).rgb * 2.0 - 1.0;
        vec3 specular = texture2D(specularTexture, vUv).rgb;

        // 计算法线
        vec3 normal = normalize(vNormal + normalMap * 0.1);

        // 太阳光照计算
        float sunDot = dot(normal, normalize(sunDirection));
        float sunIntensity = max(0.0, sunDot);

        // 日夜过渡
        float dayNightMix = smoothstep(-0.1, 0.1, sunDot);

        // 基础颜色混合
        vec3 baseColor = mix(nightColor.rgb, dayColor.rgb, dayNightMix);

        // 添加环境光
        vec3 ambientColor = baseColor * 0.3;

        // 添加漫反射光照
        vec3 diffuseColor = baseColor * sunIntensity * 0.8;

        // 添加镜面反射
        vec3 viewDirection = normalize(cameraPosition - vWorldPosition);
        vec3 reflectDirection = reflect(-normalize(sunDirection), normal);
        float specularIntensity = pow(max(0.0, dot(viewDirection, reflectDirection)), 32.0);
        vec3 specularColor = specular * specularIntensity * 0.5;

        // 大气散射效果
        float atmosphere = pow(1.0 - dot(normal, viewDirection), 2.0);
        vec3 atmosphereColor = vec3(0.3, 0.6, 1.0) * atmosphere * 0.2;

        // 最终颜色
        vec3 finalColor = ambientColor + diffuseColor + specularColor + atmosphereColor;

        gl_FragColor = vec4(finalColor, 1.0);
      }
    `

    this.earthMaterial = new THREE.ShaderMaterial({
      uniforms: {
        dayTexture: { value: dayTexture },
        nightTexture: { value: nightTexture },
        normalTexture: { value: normalTexture },
        specularTexture: { value: specularTexture },
        sunDirection: { value: new THREE.Vector3(1, 0, 0) },
        cameraPosition: { value: new THREE.Vector3() },
        time: { value: 0 }
      },
      vertexShader,
      fragmentShader
    })
  }

  createDayTexture() {
    const canvas = document.createElement('canvas')
    canvas.width = 1024
    canvas.height = 512
    const ctx = canvas.getContext('2d')

    // 创建更真实的地球日间纹理
    const imageData = ctx.createImageData(1024, 512)
    const data = imageData.data

    for (let i = 0; i < data.length; i += 4) {
      const x = (i / 4) % 1024
      const y = Math.floor((i / 4) / 1024)

      const lat = (y / 512 - 0.5) * Math.PI
      const lng = (x / 1024 - 0.5) * 2 * Math.PI

      // 生成陆地海洋分布
      const landMask = this.generateLandMask(lng, lat)

      if (landMask > 0.4) {
        // 陆地颜色
        const elevation = this.generateElevation(lng, lat)
        const temperature = Math.cos(lat) * 0.5 + 0.5

        if (elevation > 0.8) {
          // 高山 - 雪白色
          data[i] = 240 + elevation * 15
          data[i + 1] = 240 + elevation * 15
          data[i + 2] = 250
        } else if (elevation > 0.6) {
          // 山地 - 棕色
          data[i] = 139 + elevation * 50
          data[i + 1] = 119 + elevation * 30
          data[i + 2] = 101 + elevation * 20
        } else if (temperature > 0.7) {
          // 热带 - 深绿色
          data[i] = 34 + landMask * 50
          data[i + 1] = 139 + landMask * 60
          data[i + 2] = 34 + landMask * 30
        } else if (temperature > 0.3) {
          // 温带 - 浅绿色
          data[i] = 124 + landMask * 80
          data[i + 1] = 252 - landMask * 100
          data[i + 2] = 0 + landMask * 100
        } else {
          // 寒带 - 灰白色
          data[i] = 200 + landMask * 55
          data[i + 1] = 200 + landMask * 55
          data[i + 2] = 220 + landMask * 35
        }
      } else {
        // 海洋颜色 - 深蓝到浅蓝
        const depth = 1 - landMask
        data[i] = 25 + depth * 40
        data[i + 1] = 25 + depth * 120
        data[i + 2] = 112 + depth * 143
      }

      data[i + 3] = 255
    }

    ctx.putImageData(imageData, 0, 0)

    const texture = new THREE.CanvasTexture(canvas)
    texture.wrapS = THREE.RepeatWrapping
    texture.wrapT = THREE.RepeatWrapping
    return texture
  }

  createNightTexture() {
    const canvas = document.createElement('canvas')
    canvas.width = 1024
    canvas.height = 512
    const ctx = canvas.getContext('2d')

    // 黑色背景
    ctx.fillStyle = '#000011'
    ctx.fillRect(0, 0, 1024, 512)

    // 添加城市灯光
    const cities = this.getCityData()

    cities.forEach(city => {
      const x = (city.lng + 180) / 360 * 1024
      const y = (90 - city.lat) / 180 * 512

      // 城市光晕
      const gradient = ctx.createRadialGradient(x, y, 0, x, y, city.size * 3)
      gradient.addColorStop(0, `rgba(255, 220, 100, ${city.brightness})`)
      gradient.addColorStop(0.3, `rgba(255, 180, 50, ${city.brightness * 0.7})`)
      gradient.addColorStop(1, 'rgba(255, 150, 0, 0)')

      ctx.fillStyle = gradient
      ctx.beginPath()
      ctx.arc(x, y, city.size * 3, 0, Math.PI * 2)
      ctx.fill()
    })

    const texture = new THREE.CanvasTexture(canvas)
    texture.wrapS = THREE.RepeatWrapping
    texture.wrapT = THREE.RepeatWrapping
    return texture
  }

  getCityData() {
    // 主要城市数据
    return [
      { lat: 39.9042, lng: 116.4074, size: 8, brightness: 0.9 }, // 北京
      { lat: 31.2304, lng: 121.4737, size: 7, brightness: 0.8 }, // 上海
      { lat: 40.7128, lng: -74.0060, size: 9, brightness: 1.0 }, // 纽约
      { lat: 51.5074, lng: -0.1278, size: 8, brightness: 0.9 }, // 伦敦
      { lat: 48.8566, lng: 2.3522, size: 7, brightness: 0.8 }, // 巴黎
      { lat: 35.6762, lng: 139.6503, size: 9, brightness: 1.0 }, // 东京
      { lat: -33.8688, lng: 151.2093, size: 6, brightness: 0.7 }, // 悉尼
      { lat: 55.7558, lng: 37.6176, size: 7, brightness: 0.8 }, // 莫斯科
      { lat: 34.0522, lng: -118.2437, size: 8, brightness: 0.9 }, // 洛杉矶
      { lat: 19.0760, lng: 72.8777, size: 7, brightness: 0.8 }, // 孟买
      { lat: 25.2048, lng: 55.2708, size: 6, brightness: 0.7 }, // 迪拜
      { lat: 1.3521, lng: 103.8198, size: 5, brightness: 0.6 }, // 新加坡
      { lat: -23.5505, lng: -46.6333, size: 7, brightness: 0.8 }, // 圣保罗
      { lat: 19.4326, lng: -99.1332, size: 7, brightness: 0.8 }, // 墨西哥城
      { lat: 30.0444, lng: 31.2357, size: 6, brightness: 0.7 }, // 开罗
      // 添加更多小城市
      ...this.generateRandomCities(100)
    ]
  }

  generateRandomCities(count) {
    const cities = []
    for (let i = 0; i < count; i++) {
      cities.push({
        lat: (Math.random() - 0.5) * 160, // -80 到 80 度
        lng: (Math.random() - 0.5) * 360, // -180 到 180 度
        size: Math.random() * 3 + 1,
        brightness: Math.random() * 0.5 + 0.2
      })
    }
    return cities
  }

  createNormalMap() {
    const canvas = document.createElement('canvas')
    canvas.width = 512
    canvas.height = 256
    const ctx = canvas.getContext('2d')

    const imageData = ctx.createImageData(512, 256)
    const data = imageData.data

    for (let i = 0; i < data.length; i += 4) {
      const x = (i / 4) % 512
      const y = Math.floor((i / 4) / 512)

      // 生成简单的法线贴图
      const nx = Math.sin(x * 0.02) * 0.1 + 0.5
      const ny = Math.sin(y * 0.02) * 0.1 + 0.5
      const nz = 0.5

      data[i] = nx * 255
      data[i + 1] = ny * 255
      data[i + 2] = nz * 255 + 128
      data[i + 3] = 255
    }

    ctx.putImageData(imageData, 0, 0)
    return new THREE.CanvasTexture(canvas)
  }

  createSpecularMap() {
    const canvas = document.createElement('canvas')
    canvas.width = 512
    canvas.height = 256
    const ctx = canvas.getContext('2d')

    const imageData = ctx.createImageData(512, 256)
    const data = imageData.data

    for (let i = 0; i < data.length; i += 4) {
      const x = (i / 4) % 512
      const y = Math.floor((i / 4) / 512)

      const lat = (y / 256 - 0.5) * Math.PI
      const lng = (x / 512 - 0.5) * 2 * Math.PI

      // 海洋区域有镜面反射
      const landMask = this.generateLandMask(lng, lat)
      const specular = landMask < 0.4 ? 255 : 50 // 海洋亮，陆地暗

      data[i] = specular
      data[i + 1] = specular
      data[i + 2] = specular
      data[i + 3] = 255
    }

    ctx.putImageData(imageData, 0, 0)
    return new THREE.CanvasTexture(canvas)
  }

  generateLandMask(lng, lat) {
    // 改进的陆地生成算法
    let mask = 0
    mask += Math.sin(lng * 2 + lat * 3) * 0.3
    mask += Math.sin(lng * 4 - lat * 2) * 0.2
    mask += Math.sin(lng * 6 + lat * 4) * 0.1
    mask += Math.sin(lat * 8) * 0.2
    mask += Math.sin(lng * 3 + lat * 5) * 0.15
    return (mask + 1) * 0.5
  }

  generateElevation(lng, lat) {
    let elevation = 0
    elevation += Math.sin(lng * 8 + lat * 6) * 0.4
    elevation += Math.sin(lng * 16 - lat * 12) * 0.2
    elevation += Math.sin(lng * 32 + lat * 24) * 0.1
    elevation += Math.sin(lng * 5 + lat * 7) * 0.3
    return Math.max(0, elevation + 0.5)
  }

  createCityLights() {
    // 创建城市灯光点光源
    this.cityLights = []
    const cities = this.getCityData().slice(0, 20) // 只取前20个主要城市

    cities.forEach(city => {
      const light = new THREE.PointLight(0xffaa00, 0.5, 0.3)
      const pos = this.latLngToVector3(city.lat, city.lng, 1.01)
      light.position.copy(pos)
      this.scene.add(light)
      this.cityLights.push(light)
    })
  }

  latLngToVector3(lat, lng, radius = 1) {
    const phi = (90 - lat) * (Math.PI / 180)
    const theta = (lng + 180) * (Math.PI / 180)

    const x = -(radius * Math.sin(phi) * Math.cos(theta))
    const z = (radius * Math.sin(phi) * Math.sin(theta))
    const y = (radius * Math.cos(phi))

    return new THREE.Vector3(x, y, z)
  }

  createAdvancedAtmosphere() {
    if (!this.settings.showAtmosphere) return

    const geometry = new THREE.SphereGeometry(1.05, 64, 32)

    const vertexShader = `
      varying vec3 vNormal;
      varying vec3 vPosition;
      varying vec3 vWorldPosition;

      void main() {
        vNormal = normalize(normalMatrix * normal);
        vPosition = position;
        vWorldPosition = (modelMatrix * vec4(position, 1.0)).xyz;
        gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
      }
    `

    const fragmentShader = `
      uniform vec3 glowColor;
      uniform float intensity;
      uniform vec3 cameraPosition;
      uniform vec3 sunDirection;

      varying vec3 vNormal;
      varying vec3 vPosition;
      varying vec3 vWorldPosition;

      void main() {
        vec3 viewDirection = normalize(cameraPosition - vWorldPosition);
        float fresnel = 1.0 - dot(vNormal, viewDirection);
        fresnel = pow(fresnel, 2.0);

        // 太阳光照影响
        float sunDot = dot(vNormal, normalize(sunDirection));
        float sunInfluence = max(0.0, sunDot) * 0.5 + 0.5;

        vec3 atmosphereColor = glowColor * intensity * fresnel * sunInfluence;
        float alpha = fresnel * intensity * 0.6;

        gl_FragColor = vec4(atmosphereColor, alpha);
      }
    `

    const material = new THREE.ShaderMaterial({
      uniforms: {
        glowColor: { value: new THREE.Color(0x88ccff) },
        intensity: { value: 1.0 },
        cameraPosition: { value: new THREE.Vector3() },
        sunDirection: { value: new THREE.Vector3(1, 0, 0) }
      },
      vertexShader,
      fragmentShader,
      transparent: true,
      blending: THREE.AdditiveBlending,
      side: THREE.BackSide,
      depthWrite: false
    })

    this.atmosphere = new THREE.Mesh(geometry, material)
    this.scene.add(this.atmosphere)
  }

  createClouds() {
    if (!this.settings.showClouds) return

    const geometry = new THREE.SphereGeometry(1.02, 64, 32)
    const cloudTexture = this.createCloudTexture()

    const vertexShader = `
      varying vec2 vUv;
      varying vec3 vNormal;
      varying vec3 vWorldPosition;

      void main() {
        vUv = uv;
        vNormal = normalize(normalMatrix * normal);
        vWorldPosition = (modelMatrix * vec4(position, 1.0)).xyz;
        gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
      }
    `

    const fragmentShader = `
      uniform sampler2D cloudTexture;
      uniform float time;
      uniform float opacity;
      uniform vec3 sunDirection;
      uniform vec3 cameraPosition;

      varying vec2 vUv;
      varying vec3 vNormal;
      varying vec3 vWorldPosition;

      void main() {
        vec2 cloudUv = vUv + vec2(time * 0.005, 0.0);
        vec4 cloudColor = texture2D(cloudTexture, cloudUv);

        // 太阳光照
        float sunDot = dot(vNormal, normalize(sunDirection));
        float sunIntensity = max(0.0, sunDot);

        // 云层颜色
        vec3 shadowColor = vec3(0.4, 0.4, 0.5);
        vec3 lightColor = vec3(1.0, 1.0, 1.0);
        vec3 finalColor = mix(shadowColor, lightColor, sunIntensity);

        // 边缘发光
        vec3 viewDirection = normalize(cameraPosition - vWorldPosition);
        float fresnel = 1.0 - dot(vNormal, viewDirection);
        finalColor += vec3(0.2, 0.3, 0.5) * fresnel * 0.3;

        float alpha = cloudColor.a * opacity;
        gl_FragColor = vec4(finalColor, alpha);
      }
    `

    const material = new THREE.ShaderMaterial({
      uniforms: {
        cloudTexture: { value: cloudTexture },
        time: { value: 0.0 },
        opacity: { value: 0.7 },
        sunDirection: { value: new THREE.Vector3(1, 0, 0) },
        cameraPosition: { value: new THREE.Vector3() }
      },
      vertexShader,
      fragmentShader,
      transparent: true,
      depthWrite: false
    })

    this.clouds = new THREE.Mesh(geometry, material)
    this.scene.add(this.clouds)
  }

  createCloudTexture() {
    const canvas = document.createElement('canvas')
    canvas.width = 1024
    canvas.height = 512
    const ctx = canvas.getContext('2d')

    const imageData = ctx.createImageData(1024, 512)
    const data = imageData.data

    for (let i = 0; i < data.length; i += 4) {
      const x = (i / 4) % 1024
      const y = Math.floor((i / 4) / 1024)

      // 多层噪声生成云层
      let noise = 0
      noise += this.noise(x * 0.005, y * 0.005) * 0.5
      noise += this.noise(x * 0.01, y * 0.01) * 0.25
      noise += this.noise(x * 0.02, y * 0.02) * 0.125
      noise += this.noise(x * 0.04, y * 0.04) * 0.0625

      // 调整云层密度
      noise = Math.pow(Math.max(0, noise - 0.3), 1.5) * 2
      noise = Math.min(1, noise)

      const alpha = noise * 255
      data[i] = 255
      data[i + 1] = 255
      data[i + 2] = 255
      data[i + 3] = alpha
    }

    ctx.putImageData(imageData, 0, 0)

    const texture = new THREE.CanvasTexture(canvas)
    texture.wrapS = THREE.RepeatWrapping
    texture.wrapT = THREE.RepeatWrapping
    return texture
  }

  noise(x, y) {
    const n = Math.sin(x * 12.9898 + y * 78.233) * 43758.5453
    return (n - Math.floor(n))
  }

  createAdvancedStarField() {
    if (!this.settings.showStars) return

    const starCount = 8000
    const geometry = new THREE.BufferGeometry()

    const positions = new Float32Array(starCount * 3)
    const colors = new Float32Array(starCount * 3)
    const sizes = new Float32Array(starCount)

    for (let i = 0; i < starCount; i++) {
      const i3 = i * 3

      // 更大的星空范围
      const radius = 200 + Math.random() * 800
      const theta = Math.random() * Math.PI * 2
      const phi = Math.acos(2 * Math.random() - 1)

      positions[i3] = radius * Math.sin(phi) * Math.cos(theta)
      positions[i3 + 1] = radius * Math.sin(phi) * Math.sin(theta)
      positions[i3 + 2] = radius * Math.cos(phi)

      // 不同类型的星星颜色
      const starType = Math.random()
      if (starType < 0.6) {
        // 白色星星
        colors[i3] = 1.0
        colors[i3 + 1] = 1.0
        colors[i3 + 2] = 1.0
      } else if (starType < 0.8) {
        // 蓝色星星
        colors[i3] = 0.7
        colors[i3 + 1] = 0.8
        colors[i3 + 2] = 1.0
      } else if (starType < 0.9) {
        // 红色星星
        colors[i3] = 1.0
        colors[i3 + 1] = 0.6
        colors[i3 + 2] = 0.4
      } else {
        // 黄色星星
        colors[i3] = 1.0
        colors[i3 + 1] = 1.0
        colors[i3 + 2] = 0.6
      }

      // 不同大小的星星
      sizes[i] = Math.random() * 3 + 0.5
    }

    geometry.setAttribute('position', new THREE.BufferAttribute(positions, 3))
    geometry.setAttribute('color', new THREE.BufferAttribute(colors, 3))
    geometry.setAttribute('size', new THREE.BufferAttribute(sizes, 1))

    // 创建星星纹理
    const starTexture = this.createStarTexture()

    const material = new THREE.PointsMaterial({
      size: 2,
      sizeAttenuation: true,
      vertexColors: true,
      transparent: true,
      blending: THREE.AdditiveBlending,
      depthWrite: false,
      map: starTexture
    })

    this.stars = new THREE.Points(geometry, material)
    this.scene.add(this.stars)
  }

  createStarTexture() {
    const canvas = document.createElement('canvas')
    canvas.width = 64
    canvas.height = 64
    const ctx = canvas.getContext('2d')

    const gradient = ctx.createRadialGradient(32, 32, 0, 32, 32, 32)
    gradient.addColorStop(0, 'rgba(255, 255, 255, 1)')
    gradient.addColorStop(0.2, 'rgba(255, 255, 255, 0.8)')
    gradient.addColorStop(0.4, 'rgba(255, 255, 255, 0.4)')
    gradient.addColorStop(1, 'rgba(255, 255, 255, 0)')

    ctx.fillStyle = gradient
    ctx.fillRect(0, 0, 64, 64)

    return new THREE.CanvasTexture(canvas)
  }
  
  loadTexture(loader, url) {
    return new Promise((resolve, reject) => {
      loader.load(url, resolve, undefined, reject)
    })
  }
  
  async loadTexture(loader) {
    // 尝试加载在线纹理
    const urls = [
      'https://threejs.org/examples/textures/planets/earth_atmos_2048.jpg',
      'https://raw.githubusercontent.com/mrdoob/three.js/dev/examples/textures/planets/earth_atmos_2048.jpg'
    ]

    for (const url of urls) {
      try {
        return await new Promise((resolve, reject) => {
          loader.load(url, resolve, undefined, reject)
        })
      } catch (error) {
        console.warn(`纹理加载失败: ${url}`)
        continue
      }
    }

    throw new Error('所有纹理URL都加载失败')
  }

  setupLights() {
    // 环境光 - 模拟全局光照
    this.ambientLight = new THREE.AmbientLight(0x404080, 0.4)
    this.scene.add(this.ambientLight)

    // 主太阳光（方向光）
    this.sunLight = new THREE.DirectionalLight(0xffffff, 2.0)
    this.sunLight.position.set(5, 3, 5)
    this.sunLight.castShadow = true
    this.sunLight.shadow.mapSize.width = 4096
    this.sunLight.shadow.mapSize.height = 4096
    this.sunLight.shadow.camera.near = 0.1
    this.sunLight.shadow.camera.far = 50
    this.sunLight.shadow.camera.left = -10
    this.sunLight.shadow.camera.right = 10
    this.sunLight.shadow.camera.top = 10
    this.sunLight.shadow.camera.bottom = -10
    this.sunLight.shadow.bias = -0.0001
    this.scene.add(this.sunLight)

    // 辅助光源 - 模拟地球反射光
    this.fillLight = new THREE.DirectionalLight(0x88ccff, 0.3)
    this.fillLight.position.set(-5, -2, -3)
    this.scene.add(this.fillLight)

    // 背景光 - 模拟星光
    this.starLight = new THREE.AmbientLight(0x202040, 0.1)
    this.scene.add(this.starLight)

    // 半球光 - 模拟天空光照
    this.hemisphereLight = new THREE.HemisphereLight(0x87ceeb, 0x000011, 0.3)
    this.scene.add(this.hemisphereLight)
  }
  
  setupEventListeners() {
    window.addEventListener('resize', this.handleResize.bind(this))
  }
  
  animate() {
    try {
      this.animationId = requestAnimationFrame(this.animate)

      const time = Date.now() * 0.001

      // 更新控制器
      if (this.controls) {
        this.controls.update()
      }

      // 地球自转
      if (this.earth && this.settings.earthRotation > 0) {
        this.earth.rotation.y += this.settings.earthRotation
      }

      // 更新太阳位置（模拟地球公转）
      const sunAngle = time * 0.05
      const sunDistance = 10
      this.sunLight.position.set(
        Math.cos(sunAngle) * sunDistance,
        Math.sin(sunAngle * 0.3) * 3,
        Math.sin(sunAngle) * sunDistance
      )

      // 更新地球材质uniforms
      if (this.earthMaterial && this.earthMaterial.uniforms) {
        this.earthMaterial.uniforms.sunDirection.value.copy(this.sunLight.position).normalize()
        this.earthMaterial.uniforms.cameraPosition.value.copy(this.camera.position)
        this.earthMaterial.uniforms.time.value = time
      }

      // 更新大气层
      if (this.atmosphere && this.atmosphere.material.uniforms) {
        this.atmosphere.material.uniforms.cameraPosition.value.copy(this.camera.position)
        this.atmosphere.material.uniforms.sunDirection.value.copy(this.sunLight.position).normalize()
      }

      // 更新云层
      if (this.clouds && this.clouds.material.uniforms) {
        this.clouds.material.uniforms.time.value = time
        this.clouds.material.uniforms.sunDirection.value.copy(this.sunLight.position).normalize()
        this.clouds.material.uniforms.cameraPosition.value.copy(this.camera.position)
      }

      // 星空缓慢旋转
      if (this.stars) {
        this.stars.rotation.y += 0.0001
        this.stars.rotation.x += 0.00005
      }

      // 更新城市灯光强度（基于太阳位置）
      if (this.cityLights) {
        const sunIntensity = Math.max(0, this.sunLight.position.y / 10)
        const nightIntensity = 1 - sunIntensity

        this.cityLights.forEach(light => {
          light.intensity = nightIntensity * 0.8
        })
      }

      // 更新环境光强度
      if (this.ambientLight) {
        const sunHeight = this.sunLight.position.y / 10
        this.ambientLight.intensity = 0.2 + Math.max(0, sunHeight) * 0.3
      }

      // 更新回调
      if (this.onLocationUpdate) {
        const coords = this.getCameraCoordinates()
        this.onLocationUpdate(coords, this.camera.position.length())
      }

      if (this.onPerformanceUpdate && this.renderer) {
        this.onPerformanceUpdate({
          fps: 60, // 简化的FPS
          drawCalls: this.renderer.info.render.calls || 0,
          triangles: this.renderer.info.render.triangles || 0
        })
      }

      // 渲染场景
      if (this.renderer && this.scene && this.camera) {
        this.renderer.render(this.scene, this.camera)
      }
    } catch (error) {
      console.error('动画循环错误:', error)
    }
  }
  
  getCameraCoordinates() {
    const direction = this.camera.position.clone().normalize()
    const lat = Math.asin(direction.y) * 180 / Math.PI
    const lng = Math.atan2(direction.z, -direction.x) * 180 / Math.PI
    return { lat, lng }
  }
  
  handleResize(width, height) {
    const w = width || window.innerWidth
    const h = height || window.innerHeight
    
    this.camera.aspect = w / h
    this.camera.updateProjectionMatrix()
    this.renderer.setSize(w, h)
  }
  
  updateSettings(newSettings) {
    this.settings = { ...this.settings, ...newSettings }
  }
  
  updateAppSettings(newSettings) {
    // 处理应用设置
    console.log('更新应用设置:', newSettings)
  }
  
  searchLocations(query) {
    const locations = [
      // 中国主要城市
      { name: '北京', type: '首都', lat: 39.9042, lng: 116.4074 },
      { name: '上海', type: '直辖市', lat: 31.2304, lng: 121.4737 },
      { name: '广州', type: '省会', lat: 23.1291, lng: 113.2644 },
      { name: '深圳', type: '特区', lat: 22.5431, lng: 114.0579 },
      { name: '杭州', type: '省会', lat: 30.2741, lng: 120.1551 },
      { name: '南京', type: '省会', lat: 32.0603, lng: 118.7969 },
      { name: '成都', type: '省会', lat: 30.5728, lng: 104.0668 },
      { name: '重庆', type: '直辖市', lat: 29.5630, lng: 106.5516 },
      { name: '西安', type: '省会', lat: 34.3416, lng: 108.9398 },
      { name: '武汉', type: '省会', lat: 30.5928, lng: 114.3055 },

      // 国际主要城市
      { name: '纽约', type: '城市', lat: 40.7128, lng: -74.0060 },
      { name: '伦敦', type: '首都', lat: 51.5074, lng: -0.1278 },
      { name: '巴黎', type: '首都', lat: 48.8566, lng: 2.3522 },
      { name: '东京', type: '首都', lat: 35.6762, lng: 139.6503 },
      { name: '悉尼', type: '城市', lat: -33.8688, lng: 151.2093 },
      { name: '莫斯科', type: '首都', lat: 55.7558, lng: 37.6176 },
      { name: '洛杉矶', type: '城市', lat: 34.0522, lng: -118.2437 },
      { name: '孟买', type: '城市', lat: 19.0760, lng: 72.8777 },
      { name: '迪拜', type: '城市', lat: 25.2048, lng: 55.2708 },
      { name: '新加坡', type: '首都', lat: 1.3521, lng: 103.8198 },
      { name: '香港', type: '特区', lat: 22.3193, lng: 114.1694 },
      { name: '台北', type: '城市', lat: 25.0330, lng: 121.5654 },

      // 著名地标
      { name: '埃菲尔铁塔', type: '地标', lat: 48.8584, lng: 2.2945 },
      { name: '自由女神像', type: '地标', lat: 40.6892, lng: -74.0445 },
      { name: '长城', type: '地标', lat: 40.4319, lng: 116.5704 },
      { name: '金字塔', type: '地标', lat: 29.9792, lng: 31.1342 },
      { name: '泰姬陵', type: '地标', lat: 27.1751, lng: 78.0421 },
      { name: '马丘比丘', type: '地标', lat: -13.1631, lng: -72.5450 },
      { name: '富士山', type: '地标', lat: 35.3606, lng: 138.7274 },
      { name: '珠穆朗玛峰', type: '地标', lat: 27.9881, lng: 86.9250 }
    ]

    if (!query || query.trim() === '') {
      return []
    }

    const lowerQuery = query.toLowerCase().trim()

    return locations.filter(location => {
      return location.name.toLowerCase().includes(lowerQuery) ||
             location.type.toLowerCase().includes(lowerQuery)
    }).slice(0, 8) // 最多返回8个结果
  }
  
  async flyToLocation(location) {
    if (!location || typeof location.lat !== 'number' || typeof location.lng !== 'number') {
      console.error('无效的位置数据:', location)
      return
    }

    // 计算目标位置
    const targetPosition = this.latLngToVector3(location.lat, location.lng, 3)
    const targetLookAt = this.latLngToVector3(location.lat, location.lng, 1)

    // 获取当前位置
    const startPosition = this.camera.position.clone()
    const startLookAt = this.controls.target.clone()

    // 飞行动画
    return new Promise((resolve) => {
      const duration = 2000 // 2秒
      const startTime = Date.now()

      const animate = () => {
        const elapsed = Date.now() - startTime
        const progress = Math.min(elapsed / duration, 1)

        // 使用缓动函数
        const easeProgress = this.easeInOutCubic(progress)

        // 插值相机位置
        this.camera.position.lerpVectors(startPosition, targetPosition, easeProgress)
        this.controls.target.lerpVectors(startLookAt, targetLookAt, easeProgress)

        this.controls.update()

        if (progress < 1) {
          requestAnimationFrame(animate)
        } else {
          resolve()
        }
      }

      animate()
    })
  }

  easeInOutCubic(t) {
    return t < 0.5 ? 4 * t * t * t : 1 - Math.pow(-2 * t + 2, 3) / 2
  }
  
  resetView() {
    // 重置相机位置
    this.camera.position.set(0, 0, 5)
    this.controls.target.set(0, 0, 0)
    this.controls.update()
  }
  
  dispose() {
    try {
      // 停止动画循环
      if (this.animationId) {
        cancelAnimationFrame(this.animationId)
        this.animationId = null
      }

      // 清理几何体和材质
      if (this.earth) {
        this.earth.geometry.dispose()
        this.earth.material.dispose()
      }

      if (this.atmosphere) {
        this.atmosphere.geometry.dispose()
        this.atmosphere.material.dispose()
      }

      if (this.stars) {
        this.stars.geometry.dispose()
        this.stars.material.dispose()
      }

      // 清理渲染器
      if (this.renderer) {
        this.renderer.dispose()
        if (this.renderer.domElement && this.renderer.domElement.parentNode) {
          this.renderer.domElement.parentNode.removeChild(this.renderer.domElement)
        }
      }

      // 清理控制器
      if (this.controls) {
        this.controls.dispose()
      }

      // 移除事件监听器
      window.removeEventListener('resize', this.handleResize)

      console.log('地球引擎已清理')
    } catch (error) {
      console.error('清理地球引擎时出错:', error)
    }
  }
}

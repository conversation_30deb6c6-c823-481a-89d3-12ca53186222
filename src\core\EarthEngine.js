import * as THREE from 'three'
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls.js'

export class EarthEngine {
  constructor(container) {
    this.container = container
    this.scene = null
    this.camera = null
    this.renderer = null
    this.controls = null
    this.earth = null
    
    // 回调函数
    this.onLocationUpdate = null
    this.onPerformanceUpdate = null
    this.onLocationClick = null
    
    // 设置
    this.settings = {
      showAtmosphere: true,
      showClouds: true,
      showStars: true,
      showLabels: true,
      earthRotation: 0.001
    }
  }
  
  async init() {
    this.setupScene()
    this.setupCamera()
    this.setupRenderer()
    this.setupControls()
    await this.createEarth()
    this.setupLights()
    this.setupEventListeners()
    this.animate()
  }
  
  setupScene() {
    this.scene = new THREE.Scene()
    this.scene.background = new THREE.Color(0x000000)
  }
  
  setupCamera() {
    this.camera = new THREE.PerspectiveCamera(
      45,
      window.innerWidth / window.innerHeight,
      0.1,
      1000
    )
    this.camera.position.set(0, 0, 5)
  }
  
  setupRenderer() {
    this.renderer = new THREE.WebGLRenderer({
      antialias: true,
      alpha: true
    })
    this.renderer.setSize(window.innerWidth, window.innerHeight)
    this.renderer.setPixelRatio(Math.min(window.devicePixelRatio, 2))
    this.renderer.shadowMap.enabled = true
    this.renderer.shadowMap.type = THREE.PCFSoftShadowMap
    
    this.container.appendChild(this.renderer.domElement)
  }
  
  setupControls() {
    this.controls = new OrbitControls(this.camera, this.renderer.domElement)
    this.controls.enableDamping = true
    this.controls.dampingFactor = 0.05
    this.controls.minDistance = 1.5
    this.controls.maxDistance = 50
  }
  
  async createEarth() {
    // 创建高精度地球几何体
    const geometry = new THREE.SphereGeometry(1, 128, 64)

    // 创建地球材质组
    await this.createEarthMaterials()

    this.earth = new THREE.Mesh(geometry, this.earthMaterial)
    this.earth.castShadow = true
    this.earth.receiveShadow = true
    this.scene.add(this.earth)

    // 创建大气层
    await this.createAtmosphere()

    // 创建云层
    await this.createClouds()

    // 创建星空
    await this.createStarField()
  }

  async createEarthMaterials() {
    // 创建程序化地球纹理
    const earthTexture = this.createEarthTexture()

    // 使用标准材质以避免着色器兼容性问题
    this.earthMaterial = new THREE.MeshPhongMaterial({
      map: earthTexture,
      shininess: 100,
      transparent: false
    })
  }

  createEarthTexture() {
    const canvas = document.createElement('canvas')
    canvas.width = 1024
    canvas.height = 512
    const ctx = canvas.getContext('2d')

    // 创建地球表面纹理
    const imageData = ctx.createImageData(1024, 512)
    const data = imageData.data

    for (let i = 0; i < data.length; i += 4) {
      const x = (i / 4) % 1024
      const y = Math.floor((i / 4) / 1024)

      // 基于位置生成地形颜色
      const lat = (y / 512 - 0.5) * Math.PI
      const lng = (x / 1024 - 0.5) * 2 * Math.PI

      // 海洋和陆地
      const landMask = this.generateLandMask(lng, lat)

      if (landMask > 0.3) {
        // 陆地颜色
        const elevation = this.generateElevation(lng, lat)
        if (elevation > 0.7) {
          // 山脉 - 棕色/灰色
          data[i] = 139 + elevation * 50
          data[i + 1] = 119 + elevation * 30
          data[i + 2] = 101 + elevation * 20
        } else if (elevation > 0.4) {
          // 丘陵 - 绿色
          data[i] = 34 + elevation * 100
          data[i + 1] = 139 + elevation * 50
          data[i + 2] = 34 + elevation * 30
        } else {
          // 平原 - 浅绿色
          data[i] = 124 + landMask * 50
          data[i + 1] = 252 - landMask * 100
          data[i + 2] = 0 + landMask * 50
        }
      } else {
        // 海洋颜色
        const depth = 1 - landMask
        data[i] = 25 + depth * 40      // R
        data[i + 1] = 25 + depth * 80  // G
        data[i + 2] = 112 + depth * 100 // B
      }

      data[i + 3] = 255 // Alpha
    }

    ctx.putImageData(imageData, 0, 0)

    const texture = new THREE.CanvasTexture(canvas)
    texture.wrapS = THREE.RepeatWrapping
    texture.wrapT = THREE.RepeatWrapping
    return texture
  }

  generateLandMask(lng, lat) {
    // 简化的大陆形状生成
    let mask = 0
    mask += Math.sin(lng * 2 + lat * 3) * 0.3
    mask += Math.sin(lng * 4 - lat * 2) * 0.2
    mask += Math.sin(lng * 6 + lat * 4) * 0.1
    mask += Math.sin(lat * 8) * 0.2
    return (mask + 1) * 0.5
  }

  generateElevation(lng, lat) {
    // 简化的地形高度生成
    let elevation = 0
    elevation += Math.sin(lng * 8 + lat * 6) * 0.4
    elevation += Math.sin(lng * 16 - lat * 12) * 0.2
    elevation += Math.sin(lng * 32 + lat * 24) * 0.1
    return Math.max(0, elevation + 0.5)
  }

  createNormalTexture() {
    const canvas = document.createElement('canvas')
    canvas.width = 512
    canvas.height = 256
    const ctx = canvas.getContext('2d')

    // 创建简单的法线贴图
    ctx.fillStyle = '#8080ff'
    ctx.fillRect(0, 0, 512, 256)

    return new THREE.CanvasTexture(canvas)
  }

  createSpecularTexture() {
    const canvas = document.createElement('canvas')
    canvas.width = 512
    canvas.height = 256
    const ctx = canvas.getContext('2d')

    // 海洋区域有镜面反射
    ctx.fillStyle = '#404040'
    ctx.fillRect(0, 0, 512, 256)

    return new THREE.CanvasTexture(canvas)
  }
  
  loadTexture(loader, url) {
    return new Promise((resolve, reject) => {
      loader.load(url, resolve, undefined, reject)
    })
  }
  
  async createAtmosphere() {
    if (!this.settings.showAtmosphere) return

    const geometry = new THREE.SphereGeometry(1.05, 64, 32)

    const vertexShader = `
      varying vec3 vNormal;
      varying vec3 vPosition;

      void main() {
        vNormal = normalize(normalMatrix * normal);
        vPosition = position;
        gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
      }
    `

    const fragmentShader = `
      uniform vec3 glowColor;
      uniform float intensity;
      uniform vec3 viewVector;

      varying vec3 vNormal;
      varying vec3 vPosition;

      void main() {
        float fresnel = dot(normalize(vNormal), normalize(viewVector));
        fresnel = pow(1.0 - fresnel, 2.0);

        vec3 atmosphereColor = glowColor * intensity * fresnel;
        float alpha = fresnel * intensity * 0.8;

        gl_FragColor = vec4(atmosphereColor, alpha);
      }
    `

    const material = new THREE.ShaderMaterial({
      uniforms: {
        glowColor: { value: new THREE.Color(0x88ccff) },
        intensity: { value: 1.0 },
        viewVector: { value: new THREE.Vector3(0, 0, 1) }
      },
      vertexShader,
      fragmentShader,
      transparent: true,
      blending: THREE.AdditiveBlending,
      side: THREE.BackSide,
      depthWrite: false
    })

    this.atmosphere = new THREE.Mesh(geometry, material)
    this.scene.add(this.atmosphere)
  }

  async createClouds() {
    if (!this.settings.showClouds) return

    const geometry = new THREE.SphereGeometry(1.02, 64, 32)
    const cloudTexture = this.createCloudTexture()

    const vertexShader = `
      varying vec2 vUv;
      varying vec3 vNormal;

      void main() {
        vUv = uv;
        vNormal = normalize(normalMatrix * normal);
        gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
      }
    `

    const fragmentShader = `
      uniform sampler2D cloudTexture;
      uniform float time;
      uniform float opacity;
      uniform vec3 sunDirection;

      varying vec2 vUv;
      varying vec3 vNormal;

      void main() {
        vec2 cloudUv = vUv + vec2(time * 0.01, 0.0);
        vec4 cloudColor = texture2D(cloudTexture, cloudUv);

        float sunIntensity = max(0.0, dot(vNormal, sunDirection));
        vec3 finalColor = mix(vec3(0.6, 0.6, 0.7), vec3(1.0, 1.0, 1.0), sunIntensity);

        float alpha = cloudColor.a * opacity;
        gl_FragColor = vec4(finalColor, alpha);
      }
    `

    const material = new THREE.ShaderMaterial({
      uniforms: {
        cloudTexture: { value: cloudTexture },
        time: { value: 0.0 },
        opacity: { value: 0.6 },
        sunDirection: { value: new THREE.Vector3(1, 0, 0) }
      },
      vertexShader,
      fragmentShader,
      transparent: true,
      depthWrite: false
    })

    this.clouds = new THREE.Mesh(geometry, material)
    this.scene.add(this.clouds)
  }

  createCloudTexture() {
    const canvas = document.createElement('canvas')
    canvas.width = 512
    canvas.height = 256
    const ctx = canvas.getContext('2d')

    const imageData = ctx.createImageData(512, 256)
    const data = imageData.data

    for (let i = 0; i < data.length; i += 4) {
      const x = (i / 4) % 512
      const y = Math.floor((i / 4) / 512)

      let noise = 0
      noise += this.noise(x * 0.01, y * 0.01) * 0.5
      noise += this.noise(x * 0.02, y * 0.02) * 0.25
      noise += this.noise(x * 0.04, y * 0.04) * 0.125

      noise = Math.pow(Math.max(0, noise - 0.3), 2) * 3
      noise = Math.min(1, noise)

      const alpha = noise * 255
      data[i] = 255
      data[i + 1] = 255
      data[i + 2] = 255
      data[i + 3] = alpha
    }

    ctx.putImageData(imageData, 0, 0)
    return new THREE.CanvasTexture(canvas)
  }

  async createStarField() {
    if (!this.settings.showStars) return

    const starCount = 5000
    const geometry = new THREE.BufferGeometry()

    const positions = new Float32Array(starCount * 3)
    const colors = new Float32Array(starCount * 3)
    const sizes = new Float32Array(starCount)

    for (let i = 0; i < starCount; i++) {
      const i3 = i * 3

      const radius = 100 + Math.random() * 400
      const theta = Math.random() * Math.PI * 2
      const phi = Math.acos(2 * Math.random() - 1)

      positions[i3] = radius * Math.sin(phi) * Math.cos(theta)
      positions[i3 + 1] = radius * Math.sin(phi) * Math.sin(theta)
      positions[i3 + 2] = radius * Math.cos(phi)

      const starType = Math.random()
      if (starType < 0.7) {
        colors[i3] = 1.0
        colors[i3 + 1] = 1.0
        colors[i3 + 2] = 1.0
      } else if (starType < 0.85) {
        colors[i3] = 0.8
        colors[i3 + 1] = 0.9
        colors[i3 + 2] = 1.0
      } else {
        colors[i3] = 1.0
        colors[i3 + 1] = 0.7
        colors[i3 + 2] = 0.6
      }

      sizes[i] = Math.random() * 2 + 1
    }

    geometry.setAttribute('position', new THREE.BufferAttribute(positions, 3))
    geometry.setAttribute('color', new THREE.BufferAttribute(colors, 3))
    geometry.setAttribute('size', new THREE.BufferAttribute(sizes, 1))

    const material = new THREE.PointsMaterial({
      size: 2,
      sizeAttenuation: true,
      vertexColors: true,
      transparent: true,
      blending: THREE.AdditiveBlending,
      depthWrite: false
    })

    this.stars = new THREE.Points(geometry, material)
    this.scene.add(this.stars)
  }

  noise(x, y) {
    const n = Math.sin(x * 12.9898 + y * 78.233) * 43758.5453
    return (n - Math.floor(n))
  }

  setupLights() {
    // 环境光
    const ambientLight = new THREE.AmbientLight(0x404040, 0.2)
    this.scene.add(ambientLight)

    // 方向光（太阳光）
    this.sunLight = new THREE.DirectionalLight(0xffffff, 1.5)
    this.sunLight.position.set(5, 3, 5)
    this.sunLight.castShadow = true
    this.sunLight.shadow.mapSize.width = 2048
    this.sunLight.shadow.mapSize.height = 2048
    this.scene.add(this.sunLight)
  }
  
  setupEventListeners() {
    window.addEventListener('resize', this.handleResize.bind(this))
  }
  
  animate() {
    requestAnimationFrame(this.animate.bind(this))

    const time = Date.now() * 0.001

    // 更新控制器
    this.controls.update()

    // 地球自转
    if (this.earth && this.settings.earthRotation > 0) {
      this.earth.rotation.y += this.settings.earthRotation
    }

    // 更新材质（如果需要）

    // 更新大气层
    if (this.atmosphere && this.atmosphere.material.uniforms) {
      const viewVector = this.camera.position.clone().normalize()
      this.atmosphere.material.uniforms.viewVector.value.copy(viewVector)
    }

    // 更新云层
    if (this.clouds && this.clouds.material.uniforms) {
      this.clouds.material.uniforms.time.value = time
      this.clouds.material.uniforms.sunDirection.value.set(
        Math.cos(time * 0.1),
        0,
        Math.sin(time * 0.1)
      )
    }

    // 更新星空旋转
    if (this.stars) {
      this.stars.rotation.y += 0.0001
    }

    // 更新太阳光位置
    if (this.sunLight) {
      this.sunLight.position.set(
        Math.cos(time * 0.1) * 5,
        3,
        Math.sin(time * 0.1) * 5
      )
    }

    // 更新回调
    if (this.onLocationUpdate) {
      const coords = this.getCameraCoordinates()
      this.onLocationUpdate(coords, this.camera.position.length())
    }

    if (this.onPerformanceUpdate) {
      this.onPerformanceUpdate({
        fps: 60, // 简化的FPS
        drawCalls: this.renderer.info.render.calls,
        triangles: this.renderer.info.render.triangles
      })
    }

    // 渲染场景
    this.renderer.render(this.scene, this.camera)
  }
  
  getCameraCoordinates() {
    const direction = this.camera.position.clone().normalize()
    const lat = Math.asin(direction.y) * 180 / Math.PI
    const lng = Math.atan2(direction.z, -direction.x) * 180 / Math.PI
    return { lat, lng }
  }
  
  handleResize(width, height) {
    const w = width || window.innerWidth
    const h = height || window.innerHeight
    
    this.camera.aspect = w / h
    this.camera.updateProjectionMatrix()
    this.renderer.setSize(w, h)
  }
  
  updateSettings(newSettings) {
    this.settings = { ...this.settings, ...newSettings }
  }
  
  updateAppSettings(newSettings) {
    // 处理应用设置
    console.log('更新应用设置:', newSettings)
  }
  
  searchLocations(query) {
    // 模拟搜索结果
    const mockResults = [
      { name: '北京', type: '首都', lat: 39.9042, lng: 116.4074 },
      { name: '上海', type: '直辖市', lat: 31.2304, lng: 121.4737 }
    ]
    
    return mockResults.filter(location =>
      location.name.toLowerCase().includes(query.toLowerCase())
    )
  }
  
  flyToLocation(location) {
    // 简化的飞行动画
    console.log('飞行到:', location.name)
  }
  
  resetView() {
    // 重置相机位置
    this.camera.position.set(0, 0, 5)
    this.controls.target.set(0, 0, 0)
    this.controls.update()
  }
  
  dispose() {
    if (this.renderer) {
      this.renderer.dispose()
    }
    if (this.earth) {
      this.earth.geometry.dispose()
      this.earth.material.dispose()
    }
  }
}

import * as THREE from 'three'
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls.js'

export class EarthEngine {
  constructor(container) {
    this.container = container
    this.scene = null
    this.camera = null
    this.renderer = null
    this.controls = null
    this.earth = null
    this.atmosphere = null
    this.clouds = null
    this.stars = null
    this.sunLight = null
    this.animationId = null

    // 回调函数
    this.onLocationUpdate = null
    this.onPerformanceUpdate = null
    this.onLocationClick = null

    // 设置
    this.settings = {
      showAtmosphere: true,
      showClouds: true,
      showStars: true,
      showLabels: true,
      earthRotation: 0.001
    }

    // 绑定animate方法的this上下文
    this.animate = this.animate.bind(this)
  }
  
  async init() {
    try {
      this.setupScene()
      this.setupCamera()
      this.setupRenderer()
      this.setupControls()
      await this.createEarth()
      this.setupLights()
      this.setupEventListeners()
      this.animate()
      console.log('地球引擎初始化成功')
    } catch (error) {
      console.error('地球引擎初始化失败:', error)
      throw error
    }
  }
  
  setupScene() {
    this.scene = new THREE.Scene()
    this.scene.background = new THREE.Color(0x000000)
  }
  
  setupCamera() {
    this.camera = new THREE.PerspectiveCamera(
      45,
      window.innerWidth / window.innerHeight,
      0.1,
      1000
    )
    this.camera.position.set(0, 0, 5)
  }
  
  setupRenderer() {
    this.renderer = new THREE.WebGLRenderer({
      antialias: true,
      alpha: true
    })
    this.renderer.setSize(window.innerWidth, window.innerHeight)
    this.renderer.setPixelRatio(Math.min(window.devicePixelRatio, 2))
    this.renderer.shadowMap.enabled = true
    this.renderer.shadowMap.type = THREE.PCFSoftShadowMap
    
    this.container.appendChild(this.renderer.domElement)
  }
  
  setupControls() {
    this.controls = new OrbitControls(this.camera, this.renderer.domElement)
    this.controls.enableDamping = true
    this.controls.dampingFactor = 0.05
    this.controls.minDistance = 1.5
    this.controls.maxDistance = 50
  }
  
  async createEarth() {
    try {
      // 创建地球几何体
      const geometry = new THREE.SphereGeometry(1, 64, 32)

      // 创建简单但美观的地球材质
      const material = new THREE.MeshPhongMaterial({
        color: 0x2194ce,
        shininess: 100,
        transparent: false
      })

      // 尝试加载地球纹理
      try {
        const loader = new THREE.TextureLoader()
        const earthTexture = await this.loadTexture(loader)
        material.map = earthTexture
      } catch (error) {
        console.warn('使用程序化纹理')
        material.map = this.createSimpleEarthTexture()
      }

      this.earth = new THREE.Mesh(geometry, material)
      this.earth.castShadow = true
      this.earth.receiveShadow = true
      this.scene.add(this.earth)

      // 创建简单的大气层效果
      this.createSimpleAtmosphere()

      // 创建星空
      this.createSimpleStarField()

    } catch (error) {
      console.error('创建地球失败:', error)
      throw error
    }
  }

  createSimpleEarthTexture() {
    const canvas = document.createElement('canvas')
    canvas.width = 512
    canvas.height = 256
    const ctx = canvas.getContext('2d')

    // 创建简单的地球纹理
    const gradient = ctx.createLinearGradient(0, 0, 0, 256)
    gradient.addColorStop(0, '#87CEEB')  // 天蓝色
    gradient.addColorStop(0.3, '#4682B4') // 钢蓝色
    gradient.addColorStop(0.7, '#228B22') // 森林绿
    gradient.addColorStop(1, '#FFFFFF')   // 白色

    ctx.fillStyle = gradient
    ctx.fillRect(0, 0, 512, 256)

    // 添加一些陆地形状
    ctx.fillStyle = '#8FBC8F'
    for (let i = 0; i < 20; i++) {
      const x = Math.random() * 512
      const y = Math.random() * 256
      const size = Math.random() * 50 + 20
      ctx.beginPath()
      ctx.arc(x, y, size, 0, Math.PI * 2)
      ctx.fill()
    }

    const texture = new THREE.CanvasTexture(canvas)
    texture.wrapS = THREE.RepeatWrapping
    texture.wrapT = THREE.RepeatWrapping
    return texture
  }

  createSimpleAtmosphere() {
    if (!this.settings.showAtmosphere) return

    const geometry = new THREE.SphereGeometry(1.05, 32, 16)
    const material = new THREE.MeshBasicMaterial({
      color: 0x88ccff,
      transparent: true,
      opacity: 0.3,
      side: THREE.BackSide
    })

    this.atmosphere = new THREE.Mesh(geometry, material)
    this.scene.add(this.atmosphere)
  }

  createSimpleStarField() {
    if (!this.settings.showStars) return

    const starCount = 1000
    const geometry = new THREE.BufferGeometry()
    const positions = new Float32Array(starCount * 3)

    for (let i = 0; i < starCount; i++) {
      const i3 = i * 3
      const radius = 100 + Math.random() * 200
      const theta = Math.random() * Math.PI * 2
      const phi = Math.acos(2 * Math.random() - 1)

      positions[i3] = radius * Math.sin(phi) * Math.cos(theta)
      positions[i3 + 1] = radius * Math.sin(phi) * Math.sin(theta)
      positions[i3 + 2] = radius * Math.cos(phi)
    }

    geometry.setAttribute('position', new THREE.BufferAttribute(positions, 3))

    const material = new THREE.PointsMaterial({
      color: 0xffffff,
      size: 2,
      sizeAttenuation: false
    })

    this.stars = new THREE.Points(geometry, material)
    this.scene.add(this.stars)
  }
  
  loadTexture(loader, url) {
    return new Promise((resolve, reject) => {
      loader.load(url, resolve, undefined, reject)
    })
  }
  
  async loadTexture(loader) {
    // 尝试加载在线纹理
    const urls = [
      'https://threejs.org/examples/textures/planets/earth_atmos_2048.jpg',
      'https://raw.githubusercontent.com/mrdoob/three.js/dev/examples/textures/planets/earth_atmos_2048.jpg'
    ]

    for (const url of urls) {
      try {
        return await new Promise((resolve, reject) => {
          loader.load(url, resolve, undefined, reject)
        })
      } catch (error) {
        console.warn(`纹理加载失败: ${url}`)
        continue
      }
    }

    throw new Error('所有纹理URL都加载失败')
  }

  setupLights() {
    // 环境光
    const ambientLight = new THREE.AmbientLight(0x404040, 0.2)
    this.scene.add(ambientLight)

    // 方向光（太阳光）
    this.sunLight = new THREE.DirectionalLight(0xffffff, 1.5)
    this.sunLight.position.set(5, 3, 5)
    this.sunLight.castShadow = true
    this.sunLight.shadow.mapSize.width = 2048
    this.sunLight.shadow.mapSize.height = 2048
    this.scene.add(this.sunLight)
  }
  
  setupEventListeners() {
    window.addEventListener('resize', this.handleResize.bind(this))
  }
  
  animate() {
    try {
      this.animationId = requestAnimationFrame(this.animate)

      // 更新控制器
      if (this.controls) {
        this.controls.update()
      }

      // 地球自转
      if (this.earth && this.settings.earthRotation > 0) {
        this.earth.rotation.y += this.settings.earthRotation
      }

      // 星空缓慢旋转
      if (this.stars) {
        this.stars.rotation.y += 0.0001
      }

      // 更新回调
      if (this.onLocationUpdate) {
        const coords = this.getCameraCoordinates()
        this.onLocationUpdate(coords, this.camera.position.length())
      }

      if (this.onPerformanceUpdate && this.renderer) {
        this.onPerformanceUpdate({
          fps: 60, // 简化的FPS
          drawCalls: this.renderer.info.render.calls || 0,
          triangles: this.renderer.info.render.triangles || 0
        })
      }

      // 渲染场景
      if (this.renderer && this.scene && this.camera) {
        this.renderer.render(this.scene, this.camera)
      }
    } catch (error) {
      console.error('动画循环错误:', error)
    }
  }
  
  getCameraCoordinates() {
    const direction = this.camera.position.clone().normalize()
    const lat = Math.asin(direction.y) * 180 / Math.PI
    const lng = Math.atan2(direction.z, -direction.x) * 180 / Math.PI
    return { lat, lng }
  }
  
  handleResize(width, height) {
    const w = width || window.innerWidth
    const h = height || window.innerHeight
    
    this.camera.aspect = w / h
    this.camera.updateProjectionMatrix()
    this.renderer.setSize(w, h)
  }
  
  updateSettings(newSettings) {
    this.settings = { ...this.settings, ...newSettings }
  }
  
  updateAppSettings(newSettings) {
    // 处理应用设置
    console.log('更新应用设置:', newSettings)
  }
  
  searchLocations(query) {
    // 模拟搜索结果
    const mockResults = [
      { name: '北京', type: '首都', lat: 39.9042, lng: 116.4074 },
      { name: '上海', type: '直辖市', lat: 31.2304, lng: 121.4737 }
    ]
    
    return mockResults.filter(location =>
      location.name.toLowerCase().includes(query.toLowerCase())
    )
  }
  
  flyToLocation(location) {
    // 简化的飞行动画
    console.log('飞行到:', location.name)
  }
  
  resetView() {
    // 重置相机位置
    this.camera.position.set(0, 0, 5)
    this.controls.target.set(0, 0, 0)
    this.controls.update()
  }
  
  dispose() {
    try {
      // 停止动画循环
      if (this.animationId) {
        cancelAnimationFrame(this.animationId)
        this.animationId = null
      }

      // 清理几何体和材质
      if (this.earth) {
        this.earth.geometry.dispose()
        this.earth.material.dispose()
      }

      if (this.atmosphere) {
        this.atmosphere.geometry.dispose()
        this.atmosphere.material.dispose()
      }

      if (this.stars) {
        this.stars.geometry.dispose()
        this.stars.material.dispose()
      }

      // 清理渲染器
      if (this.renderer) {
        this.renderer.dispose()
        if (this.renderer.domElement && this.renderer.domElement.parentNode) {
          this.renderer.domElement.parentNode.removeChild(this.renderer.domElement)
        }
      }

      // 清理控制器
      if (this.controls) {
        this.controls.dispose()
      }

      // 移除事件监听器
      window.removeEventListener('resize', this.handleResize)

      console.log('地球引擎已清理')
    } catch (error) {
      console.error('清理地球引擎时出错:', error)
    }
  }
}

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Google Earth Clone - Three.js</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            background: #000;
            overflow: hidden;
            color: white;
        }

        #canvas-container {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 1;
        }

        #ui-container {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 10;
            pointer-events: none;
        }

        .search-container {
            position: absolute;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            pointer-events: auto;
        }

        .search-box {
            background: rgba(0, 0, 0, 0.8);
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 25px;
            padding: 12px 20px;
            color: white;
            font-size: 16px;
            width: 400px;
            outline: none;
            backdrop-filter: blur(10px);
        }

        .search-box::placeholder {
            color: rgba(255, 255, 255, 0.6);
        }

        .controls-panel {
            position: absolute;
            top: 20px;
            right: 20px;
            background: rgba(0, 0, 0, 0.8);
            border-radius: 10px;
            padding: 15px;
            pointer-events: auto;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .control-button {
            display: block;
            width: 100%;
            margin: 5px 0;
            padding: 10px 15px;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 5px;
            color: white;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .control-button:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-2px);
        }

        .info-panel {
            position: absolute;
            bottom: 20px;
            left: 20px;
            background: rgba(0, 0, 0, 0.8);
            border-radius: 10px;
            padding: 15px;
            pointer-events: auto;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            max-width: 300px;
        }

        .loading-screen {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: #000;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            z-index: 1000;
            transition: opacity 0.5s ease;
        }

        .loading-spinner {
            width: 50px;
            height: 50px;
            border: 3px solid rgba(255, 255, 255, 0.3);
            border-top: 3px solid #4285f4;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-bottom: 20px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .hidden {
            opacity: 0;
            pointer-events: none;
        }
    </style>
</head>
<body>
    <div id="loading-screen" class="loading-screen">
        <div class="loading-spinner"></div>
        <p>正在加载地球...</p>
    </div>

    <div id="canvas-container"></div>

    <div id="ui-container">
        <div class="search-container">
            <input type="text" class="search-box" placeholder="搜索地点..." id="search-input">
        </div>

        <div class="controls-panel">
            <button class="control-button" id="reset-view">重置视图</button>
            <button class="control-button" id="toggle-atmosphere">大气层</button>
            <button class="control-button" id="toggle-clouds">云层</button>
            <button class="control-button" id="toggle-stars">星空</button>
            <button class="control-button" id="toggle-labels">标签</button>
        </div>

        <div class="info-panel">
            <h3>控制说明</h3>
            <p><strong>鼠标左键:</strong> 旋转地球</p>
            <p><strong>鼠标滚轮:</strong> 缩放</p>
            <p><strong>鼠标右键:</strong> 平移</p>
            <p><strong>双击:</strong> 聚焦位置</p>
            <hr style="margin: 10px 0; border: 1px solid rgba(255,255,255,0.2);">
            <h4>快捷键</h4>
            <p><strong>R:</strong> 重置视图</p>
            <p><strong>G:</strong> 显示/隐藏调试面板</p>
            <p><strong>P:</strong> 性能监控</p>
            <p><strong>Space:</strong> 暂停/继续自转</p>
            <p><strong>1-4:</strong> 切换图层</p>
            <p><strong>H:</strong> 帮助</p>
            <p><strong>F:</strong> 全屏</p>
        </div>
    </div>

    <script type="module" src="/src/main.js"></script>
</body>
</html>
